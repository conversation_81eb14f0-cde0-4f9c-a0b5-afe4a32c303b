/**
 * 全局主题变量定义
 * 
 * 包含以下类别：
 * 1. 品牌色 - 主色、辅助色
 * 2. 功能色 - 成功、警告、错误
 * 3. 文字色 - 主要、次要、提示
 * 4. 背景色 - 主要、次要、浅色
 * 5. 边框色 - 分割线、边框
 * 6. 阴影 - 不同层级的阴影
 * 7. 圆角 - 不同大小的圆角
 * 8. 间距 - 统一的间距单位
 * 9. 动画 - 过渡效果
 * 10. 组件特定样式 - 针对特定组件的变量
 */

:root {
  /* 品牌色 */
  --brand-primary: #FF3C6A;
  --primary-color: var(--brand-primary); /* 别名，确保兼容性 */
  --brand-primary-rgb: 255, 60, 106;
  --brand-primary-hover: #ff2d5f;
  --brand-primary-light: rgba(255, 60, 106, 0.1);
  --brand-primary-lighter: rgba(255, 60, 106, 0.05);
  --brand-gradient: linear-gradient(45deg, #FF8C42, #FF3C6A);

  /* 功能色 */
  --success-color: #52c41a;
  --warning-color: #ff7a45;
  --warning-light: #ffd591;
  --error-color: #ff4d4f;
  --error-light: #ffccc7;
  --error-bg: #fff1f0;
  --info-color: #1890ff;

  /* 文字色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-tertiary: #999999;
  --text-disabled: #cccccc;
  --text-inverse: #ffffff;

  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-primary-rgb: 255, 255, 255;
  --bg-secondary: #f5f5f5;
  --bg-tertiary: #f8f9fa;
  --bg-mask: rgba(0, 0, 0, 0.45);
  --bg-hover: rgba(0, 0, 0, 0.04);
  --bg-active: rgba(0, 0, 0, 0.08);
  --bg-disabled: #f5f5f5;
  --bg-overlay: rgba(0, 0, 0, 0.65);
  --bg-inverse: rgba(255, 255, 255, 0.15);

  /* 边框色 */
  --border-color: #e0e0e0;
  --border-light: #eeeeee;
  --border-lighter: #f0f0f0;
  --border-hover: #d0d0d0;
  --border-focus: var(--brand-primary);

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
  --shadow-btn: 0 2px 8px rgba(0, 0, 0, 0.2);

  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* 间距 */
  --spacing-xxs: 4px;
  --spacing-xs: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;

  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 13px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;

  /* 动画 */
  --transition-normal: color 0.2s ease, border-color 0.2s ease;
  --transition-fast: color 0.15s ease, border-color 0.15s ease;
  --transition-slow: color 0.3s ease, border-color 0.3s ease;
  --transition-transform: transform 0.3s ease;

  /* 滚动条 */
  --scrollbar-width: 7px;
  
  /* 品牌阴影 */
  --shadow-brand: 0 0 0 1px var(--brand-primary);
  
  /* 模糊效果 */
  --blur-sm: blur(2px);
  --blur-md: blur(8px);
  
  /* 常用颜色 */
  --color-white: #ffffff;
  --color-black: #000000;
  
  /* 组件特定 */
  --dot-size: 4px;
  --icon-size-sm: 18px;
  --icon-size-md: 24px;

  --energy-gradient-yellow-light: linear-gradient(45deg, #FFD600, #FF9800);
  --energy-gradient-yellow-dark: linear-gradient(45deg, #ff9d66, #ffc53d);

  /* 组件特定 */
  --card-bg: #ffffff;
  --modal-bg: #ffffff;
  --dropdown-bg: #ffffff;
  --tooltip-bg: #f8f9fa;
  --menu-bg: #ffffff;
}

/* 暗黑主题 */
[data-theme="dark"] {
  /* 品牌色 */
  --brand-primary: #ff6b8f;
  --primary-color: var(--brand-primary); /* 别名，确保兼容性 */
  --brand-primary-rgb: 255, 107, 143;
  --brand-primary-hover: #ff8ca8;
  --brand-primary-light: rgba(255, 107, 143, 0.2);
  --brand-primary-lighter: rgba(255, 107, 143, 0.1);
  --brand-gradient: linear-gradient(45deg, #ff9d66, #ff6b8f);

  /* 功能色 */
  --success-color: #73d13d;
  --warning-color: #ff9c6e;
  --warning-light: #ffe58f;
  --error-color: #ff7875;
  --error-light: #ff9e9e;
  --error-bg: rgba(255, 77, 79, 0.15);
  --info-color: #40a9ff;

  /* 文字色 */
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-tertiary: #999999;
  --text-disabled: #666666;
  --text-inverse: #333333;

  /* 背景色 */
  --bg-primary: #1f1f1f;
  --bg-primary-rgb: 31, 31, 31;
  --bg-secondary: #454545;
  --bg-tertiary: #3f3f3f;
  --bg-mask: rgba(0, 0, 0, 0.65);
  --bg-hover: rgba(255, 255, 255, 0.08);
  --bg-active: rgba(255, 255, 255, 0.12);
  --bg-disabled: #2f2f2f;
  --bg-overlay: rgba(0, 0, 0, 0.75);
  --bg-inverse: rgba(0, 0, 0, 0.3);

  /* 边框色 */
  --border-color: #494949;
  --border-light: #585858;
  --border-lighter: #6a6a6a;
  --border-hover: #7a7a7a;
  --border-focus: var(--brand-primary);

  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.5);
  --shadow-btn: 0 2px 8px rgba(0, 0, 0, 0.4);

  --energy-gradient-yellow-light: linear-gradient(45deg, #ff9d66, #ffc53d);
  --energy-gradient-yellow-dark: linear-gradient(45deg, #E6C200, #E69500);

  /* 组件特定 */
  --card-bg: #2f2f2f;
  --modal-bg: #1f1f1f;
  --dropdown-bg: #2f2f2f;
  --tooltip-bg: #3f3f3f;
  --menu-bg: #2f2f2f;
}

/* 添加全局过渡效果 */
html {
  transition: background-color 0.3s ease, color 0.3s ease;
}

body {
  transition: background-color 0.3s ease, color 0.3s ease;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* 为所有元素添加基本过渡效果 */
* {
  transition-property: color, background-color, border-color, box-shadow, opacity;
  transition-duration: 0.15s;
  transition-timing-function: ease;
}

/* 排除不需要主题过渡效果的元素 */
.no-transition,
.animation,
.spinner,
.loading,
.progress-bar,
[class*="animate-"],
[class*="motion-"],
svg,
svg *,
[class*="copy-"] svg,
[class*="btn"] svg,
[class*="button"] svg {
  transition: none !important;
}

.app-container {
  transition: background-color 0.3s ease;
  background-color: var(--bg-primary);
}

/* 仅对需要过渡效果的元素应用过渡 */
.navbar,
.sidebar,
.main-content,
.modal-content,
.button,
.input,
.text,
.card,
.dropdown,
.tooltip,
.menu,
.tab,
.panel,
.dialog,
.alert,
.notification,
.badge,
.tag,
.avatar,
.divider,
.icon,
.switch,
.checkbox,
.radio,
.select,
.textarea,
.progress,
.slider,
.table,
.list-item {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* 添加快速响应元素类 */
button,
.button,
[class*="btn"],
[class*="icon"],
[role="button"],
a {
  transition-duration: 0.1s;
}

button svg,
.button svg,
[class*="btn"] svg,
[class*="button"] svg,
[class*="icon"] svg,
a svg {
  transition: none !important;
} 

@media (max-width: 768px) {
  .sidebar-guide-img-popup-global {
    display: none !important;
  }
} 