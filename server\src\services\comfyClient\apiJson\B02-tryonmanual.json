{"2": {"inputs": {"guidance": 50, "conditioning": ["5", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "3": {"inputs": {"strength": 1.0000000000000002, "strength_type": "multiply", "conditioning": ["2", 0], "style_model": ["13", 0], "clip_vision_output": ["16", 0]}, "class_type": "StyleModelApply", "_meta": {"title": "Apply Style Model"}}, "4": {"inputs": {"text": "low quality,blurry,", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["9", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "5": {"inputs": {"text": "", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["9", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "6": {"inputs": {"model": ["69", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "7": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "9": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "11": {"inputs": {"direction": "left-right", "pixels": 0, "method": "auto", "image_1": ["58", 1], "image_2": ["59", 1], "mask_1": ["58", 2], "mask_2": ["59", 2]}, "class_type": "easy makeImageForICLora", "_meta": {"title": "Make Image For ICLora"}}, "12": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "13": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "16": {"inputs": {"crop": "none", "clip_vision": ["12", 0], "image": ["58", 1]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "17": {"inputs": {"samples": ["27", 0], "vae": ["7", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "26": {"inputs": {"noise_mask": true, "positive": ["3", 0], "negative": ["4", 0], "vae": ["7", 0], "pixels": ["11", 0], "mask": ["11", 1]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "27": {"inputs": {"needInput": true, "seed": 499933040492815, "steps": 25, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["6", 0], "positive": ["26", 0], "negative": ["26", 1], "latent_image": ["28", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "随机种子"}}, "28": {"inputs": {"needInput": true, "amount": 1, "samples": ["26", 2]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "图片数量"}}, "58": {"inputs": {"context_expand_pixels": 20, "context_expand_factor": 1.0000000000000002, "fill_mask_holes": false, "blur_mask_pixels": 16, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "ranged size", "force_width": 1024, "force_height": 1024, "rescale_factor": 1.0000000000000002, "min_width": 1, "min_height": 1, "max_width": 1280, "max_height": 1280, "padding": 32, "image": ["73", 0], "mask": ["70", 1]}, "class_type": "InpaintCrop", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Crop node)"}}, "59": {"inputs": {"context_expand_pixels": 20, "context_expand_factor": 1.0000000000000002, "fill_mask_holes": false, "blur_mask_pixels": 16, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "forced size", "force_width": ["83", 0], "force_height": ["83", 1], "rescale_factor": 1.0000000000000002, "min_width": 512, "min_height": 512, "max_width": 768, "max_height": 768, "padding": 32, "image": ["76", 0], "mask": ["76", 1]}, "class_type": "InpaintCrop", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Crop node)"}}, "60": {"inputs": {"x": ["11", 5], "y": 0, "width": ["11", 3], "height": ["11", 4], "image": ["17", 0]}, "class_type": "ETN_CropImage", "_meta": {"title": "Crop Image"}}, "61": {"inputs": {"rescale_algorithm": "bislerp", "stitch": ["59", 0], "inpainted_image": ["67", 0]}, "class_type": "InpaintStitch", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Stitch node)"}}, "67": {"inputs": {"image": ["60", 0]}, "class_type": "ImpactImageBatchToImageList", "_meta": {"title": "Image Batch to Image List"}}, "68": {"inputs": {"images": ["61", 0]}, "class_type": "ImageListToImageBatch", "_meta": {"title": "Image List to Image Batch"}}, "69": {"inputs": {"unet_name": "flux1-fill-dev.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "70": {"inputs": {"threshold": 0.30000000000000004, "detail_method": "VITMatte", "needInput": true, "detail_erode": 3, "detail_dilate": 6, "black_point": 0.15000000000000002, "white_point": 0.99, "process_detail": true, "prompt": "swimsuit", "device": "cuda", "max_megapixels": 2, "image": ["75", 0], "sam_models": ["71", 0]}, "class_type": "LayerMask: SegmentAnythingUltra V3", "_meta": {"title": "款式描述-服装"}}, "71": {"inputs": {"sam_model": "sam_hq_vit_h (2.57GB)", "grounding_dino_model": "GroundingDINO_SwinB (938MB)"}, "class_type": "LayerMask: LoadSegmentAnythingModels", "_meta": {"title": "LayerMask: Load SegmentAnything Models(Advance)"}}, "73": {"inputs": {"fill_background": true, "background_color": "#FFFFFF", "RGBA_image": ["70", 0], "mask": ["70", 1]}, "class_type": "LayerUtility: ImageRemoveAlpha", "_meta": {"title": "LayerUtility: ImageRemoveAlpha"}}, "75": {"inputs": {"needInput": true, "url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "服装图片上传"}}, "76": {"inputs": {"needInput": true, "url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "模特图片上传+手动涂抹蒙版"}}, "82": {"inputs": {"filename_prefix": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "images": ["68", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "83": {"inputs": {"image": ["58", 1]}, "class_type": "GetImageSize", "_meta": {"title": "Get Image Size"}}, "84": {"inputs": {"masks": ["76", 1]}, "class_type": "Mask Fill Holes", "_meta": {"title": "Mask Fill Holes"}}, "85": {"inputs": {"mask": ["84", 0]}, "class_type": "MaskToImage", "_meta": {"title": "Convert Mask to Image"}}, "86": {"inputs": {"channel": "red", "image": ["85", 0]}, "class_type": "ImageToMask", "_meta": {"title": "Convert Image to Mask"}}}