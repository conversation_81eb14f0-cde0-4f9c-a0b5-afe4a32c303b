{"2": {"inputs": {"guidance": 50, "conditioning": ["5", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "3": {"inputs": {"strength": 1.0000000000000002, "strength_type": "multiply", "conditioning": ["2", 0], "style_model": ["13", 0], "clip_vision_output": ["16", 0]}, "class_type": "StyleModelApply", "_meta": {"title": "Apply Style Model"}}, "4": {"inputs": {"text": "low quality,blurry,", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["9", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "5": {"inputs": {"text": "", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["9", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "6": {"inputs": {"model": ["92", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "7": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "9": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp16.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "11": {"inputs": {"direction": "left-right", "pixels": 0, "method": "auto", "image_1": ["58", 1], "image_2": ["59", 1], "mask_1": ["58", 2], "mask_2": ["59", 2]}, "class_type": "easy makeImageForICLora", "_meta": {"title": "Make Image For ICLora"}}, "12": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "13": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "16": {"inputs": {"crop": "none", "clip_vision": ["12", 0], "image": ["58", 1]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "17": {"inputs": {"samples": ["27", 0], "vae": ["7", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "26": {"inputs": {"noise_mask": false, "positive": ["3", 0], "negative": ["4", 0], "vae": ["7", 0], "pixels": ["11", 0], "mask": ["11", 1]}, "class_type": "InpaintModelConditioning", "_meta": {"title": "InpaintModelConditioning"}}, "27": {"inputs": {"seed": 657934497926384, "steps": 25, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "denoise": 1, "model": ["6", 0], "positive": ["26", 0], "negative": ["26", 1], "latent_image": ["28", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "随机种子"}}, "28": {"inputs": {"amount": 1, "samples": ["26", 2]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "图片数量"}}, "58": {"inputs": {"context_expand_pixels": 20, "context_expand_factor": 1.0000000000000002, "fill_mask_holes": false, "blur_mask_pixels": 16, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "ranged size", "force_width": 1024, "force_height": 1024, "rescale_factor": 1.0000000000000002, "min_width": 1, "min_height": 1, "max_width": 1280, "max_height": 1280, "padding": 32, "image": ["104", 0], "mask": ["104", 1]}, "class_type": "InpaintCrop", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Crop node)"}}, "59": {"inputs": {"context_expand_pixels": 20, "context_expand_factor": 1.0000000000000002, "fill_mask_holes": false, "blur_mask_pixels": 16, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "forced size", "force_width": ["83", 0], "force_height": ["83", 1], "rescale_factor": 1.0000000000000002, "min_width": 512, "min_height": 512, "max_width": 768, "max_height": 768, "padding": 32, "image": ["105", 0], "mask": ["105", 1]}, "class_type": "InpaintCrop", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Crop node)"}}, "60": {"inputs": {"x": ["11", 5], "y": 0, "width": ["11", 3], "height": ["11", 4], "image": ["17", 0]}, "class_type": "ETN_CropImage", "_meta": {"title": "Crop Image"}}, "61": {"inputs": {"rescale_algorithm": "bislerp", "stitch": ["59", 0], "inpainted_image": ["67", 0]}, "class_type": "InpaintStitch", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Stitch node)"}}, "67": {"inputs": {"image": ["60", 0]}, "class_type": "ImpactImageBatchToImageList", "_meta": {"title": "Image Batch to Image List"}}, "68": {"inputs": {"images": ["61", 0]}, "class_type": "ImageListToImageBatch", "_meta": {"title": "Image List to Image Batch"}}, "69": {"inputs": {"unet_name": "flux1-fill-dev.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "82": {"inputs": {"filename_prefix": "ChangeFace", "images": ["93", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图片"}}, "83": {"inputs": {"image": ["58", 1]}, "class_type": "GetImageSize", "_meta": {"title": "Get Image Size"}}, "92": {"inputs": {"lora_name": "Flux/comfyui_portrait_lora64.safetensors", "strength_model": 1, "model": ["69", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "93": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": ***************, "steps": 15, "cfg": 2, "sampler_name": "euler_ancestral", "scheduler": "karras", "denoise": 0.****************, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5000000000000001, "bbox_dilation": 10, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.9300000000000002, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7000000000000002, "sam_mask_hint_use_negative": "False", "drop_size": 10, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "tiled_encode": {"__value__": [false, true]}, "tiled_decode": false, "speak_and_recognation": {"__value__": [false, true]}, "image": ["68", 0], "model": ["69", 0], "clip": ["9", 0], "vae": ["7", 0], "positive": ["2", 0], "negative": ["4", 0], "bbox_detector": ["99", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "99": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "104": {"inputs": {"url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "上传脸部参考图并涂抹蒙版"}}, "105": {"inputs": {"url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "上传模特图并涂抹蒙版"}}}