import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { UPLOAD_CONFIG, validateFile } from '../../config/uploads/upload';
import './index.css';
import { getCSRFToken } from '../../api/security/csrf';
import { message } from 'antd';

/**
 * 通用上传盒子组件
 * 
 * 统一处理以下类型的上传：
 * - fashion(时尚大片)：参考图片上传，无需抠图处理
 * - try-on(模特换装)：服装图片上传，无需抠图处理，模特图片上传，不抠图处理，需要蒙版
 * - background(背景替换)：前景图片上传，无需抠图处理
 * - recolor(服装复色)：服装复色图上传，无需抠图处理
 * - fabric(换面料)：服装图片上传，无需抠图处理
 */
const UploadBox = ({
  id,
  onUpload,
  onShowGuide,
  onUploadResult,
  panels = [],
  onChange,
  className,
  showSupportTag = false,
  pageType = 'fashion', // 默认为时尚大片页面
  uploadType = 'reference', // 默认为参考图片上传
  uploadSubType = '' // 子类型，用于区分同一页面的不同标签页
}) => {
  const [isDragging, setIsDragging] = useState(false);

  // 生成唯一ID的函数
  const generateUniqueId = () => {
    // 直接使用服务器文件名的格式生成ID，不使用前缀
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 10);
    // 注意：这里使用的格式与服务器生成的文件名格式保持一致 - ${timestamp}-${random}
    return `${timestamp}-${random}`;
  };

  // 根据页面类型获取上传提示文本
  const getUploadText = () => {
    if (pageType === 'background' && uploadType === 'foreground') {
      return '前景图片（要换背景的图片）';
    } else if (pageType === 'trending' && uploadType === 'pattern') {
      return '版型参考图片（或线稿）';
    } else if (pageType === 'trending' && uploadType === 'printing') {
      return '面料印花参考图片';
    } else if (pageType === 'upscale' && uploadType === 'source') {
      return '原始图片';
    } else if (pageType === 'extend' && uploadType === 'source') {
      return '原始图片';
    } else if (pageType === 'bleach' && uploadType === 'source') {
      return '原始图片';
    } else if (pageType === 'inpaint' && uploadType === 'source') {
      return '原始图片';
    } else if (pageType === 'matting' && uploadType === 'source') {
      return '原始图片';
    } else if (pageType === 'extract' && uploadType === 'source') {
      return '原始图片';
    } else if (pageType === 'fashion' && uploadType === 'reference') {
      return '参考图片';
    } else {
      return '服装图片';
    }
  };

  // 根据页面类型和上传类型判断是否允许多选
  const isMultipleAllowed = () => {
    // UploadBox组件统一只允许单选图片
    // 多选功能由AutoUploadBox组件专门处理
    return false;
  };

  // 根据页面类型和上传类型获取要创建的面板类型
  const getPanelType = () => {
    if (pageType === 'background' && uploadType === 'foreground') {
      return 'foreground';
    } else if (pageType === 'recolor') {
      return 'recolorClothing'; // 服装复色页面使用recolorClothing类型
    } else if (pageType === 'fabric') {
      return 'fabricClothing'; // 换面料页面使用fabricClothing类型
    } else if (pageType === 'bleach' && uploadType === 'source') {
      return 'source'; // 服装去色页面使用source类型
    } else if (uploadType === 'pattern') {
      return 'pattern'; // 版型图片使用pattern类型
    } else if (uploadType === 'reference') {
      return 'reference'; // 参考图片类型
    } else {
      return 'clothing';
    }
  };

  // 根据页面类型获取要使用的API类型
  const getApiImageType = () => {
    if (pageType === 'background' && uploadType === 'foreground') {
      return 'foreground';
    } else if (pageType === 'recolor') {
      return 'recolorClothing';
    } else if (pageType === 'fabric') {
      return 'fabricClothing';
    } else if (pageType === 'bleach' && uploadType === 'source') {
      return 'source'; // 服装去色页面使用source类型
    } else if (uploadType === 'pattern') {
      return 'pattern'; // 版型图片使用pattern类型
    } else if (uploadType === 'reference') {
      return 'reference'; // 参考图片类型
    } else {
      return 'clothing';
    }
  };

  // 根据页面类型获取面板标题
  const getPanelTitle = () => {
    if (pageType === 'background' && uploadType === 'foreground') {
      return '前景';
    } else if (pageType === 'trending' && uploadType === 'pattern') {
      return '版型';
    } else if (uploadType === 'reference') {
      return '参考图';
    } else if (pageType === 'upscale' && uploadType === 'source') {
      return '原始图片';
    } else if (pageType === 'extend' && uploadType === 'source') {
      return '原始图片';
    } else if (pageType === 'bleach' && uploadType === 'source') {
      return '原始图片';
    } else if (pageType === 'extract' && uploadType === 'source') {
      return '原始图片';
    } else if (pageType === 'inpaint' && uploadType === 'source') {
      return '原始图片';
    } else if (pageType === 'matting' && uploadType === 'source') {
      return '原始图片';
    } else {
      return '服装';
    }
  };

  // 处理拖拽事件
  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // 处理文件拖放
  const handleDrop = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    // 首先检查是否有自定义的拖拽数据（从TaskPanel拖拽过来的图片）
    try {
      const customData = e.dataTransfer.getData('application/json');
      if (customData) {
        const dragData = JSON.parse(customData);
        if (dragData.type === 'image-upload') {
          // 处理从TaskPanel拖拽过来的图片
          await handleImageFromTaskPanel(dragData);
          return;
        }
      }
    } catch (error) {
      console.log('解析拖拽数据失败，尝试处理文件:', error);
    }

    const files = Array.from(e.dataTransfer.files);
    
    // 使用统一的文件校验函数
    for (const file of files) {
      const validation = await validateFile(file);
      if (!validation.isValid) {
        message.error(validation.error);
        return;
      }
    }

    // 如果选择了多个文件，只处理第一个并给出提示
    if (files.length > 1) {
      message.info('只能上传一张图片，已选择第一张图片');
    }

    // 处理第一个文件
    if (files.length > 0) {
      handleSingleFileUpload(files[0]);
    }
  };

  // 处理从TaskPanel拖拽过来的图片
  const handleImageFromTaskPanel = async (dragData) => {
    try {
      const { imageUrl, fileName, taskId, imageIndex } = dragData;
      
      // 将HTTP转换为HTTPS，解决混合内容问题
      const httpsUrl = imageUrl.replace(/^http:/, 'https:');
      
      // 从URL获取图片数据
      const response = await fetch(httpsUrl);
      if (!response.ok) {
        throw new Error('无法获取图片数据');
      }
      
      const blob = await response.blob();
      
      // 创建一个File对象
      const file = new File([blob], fileName, {
        type: blob.type || 'image/jpeg',
        lastModified: Date.now()
      });
      
      console.log(`处理从任务面板拖拽的图片: ${fileName}, 任务ID: ${taskId}, 图片索引: ${imageIndex}`);
      
      // 对从TaskPanel拖拽过来的图片进行校验
      const validation = await validateFile(file);
      if (!validation.isValid) {
        message.error(validation.error);
        return;
      }
      
      // 使用现有的文件上传逻辑
      await handleSingleFileUpload(file);
      
      // message.success('图片已添加到上传区域');
      
    } catch (error) {
      console.error('处理拖拽图片时出错:', error);
      message.error('处理图片失败，请重试');
    }
  };

  // 处理单文件上传
  const handleSingleFileUpload = async (file) => {
    try {
      // 为图片创建一个唯一ID
      const imageId = generateUniqueId();
      const imageUrl = URL.createObjectURL(file);
      
      console.log(`创建${getPanelTitle()}面板，ID: ${imageId}`);
      console.log(`将保存原始文件引用，以便后续上传到服务器: ${file.name}, 大小: ${file.size}字节`);
      
      // 创建面板数据
      const panel = {
        componentId: imageId,
        title: getPanelTitle(),
        status: 'completed', // 直接设置为完成状态，因为不立即上传
        serverFileName: file.name, // 替换为serverFileName
        url: imageUrl, // 使用本地URL
        fileInfo: {
          name: file.name,
          size: file.size,
          type: file.type,
          serverFileName: file.name // 在fileInfo中也设置serverFileName
        },
        type: getPanelType(),
        file: file, // 保存原始文件对象，供后续上传使用
        // 确保在processInfo中也设置serverFileName
        processInfo: {
          serverFileName: file.name
        }
      };
      
      // 通知父组件创建完成的面板
      onUploadResult?.({ type: 'panels', panels: [panel] });
      
      // 显示上传成功消息
      message.success('图片上传成功');
      
      console.log(`${getPanelTitle()}面板创建成功，ID: ${imageId}，将在点击生成按钮时上传到服务器`);
      // 不立即上传到服务器，而是在点击生成按钮时再上传
    } catch (error) {
      console.error('处理上传图片时出错:', error);
      onUploadResult?.({ 
        type: 'error', 
        error: error.message 
      });
    }
  };

  return (
    <div className={`upload-area ${className || ''}`}>
      <input 
        type="file" 
        id={id}
        className="file-input" 
        accept={UPLOAD_CONFIG.getAcceptTypes()}
        onChange={(e) => {
          // 如果有文件被选择
          if (e.target.files.length > 0) {
            const files = Array.from(e.target.files);
            console.log(`文件选择器选择了 ${files.length} 个文件，页面类型: ${pageType}, 允许多选: ${isMultipleAllowed()}`);
            
            // 使用统一的文件校验函数
            const validateFiles = async () => {
              for (const file of files) {
                const validation = await validateFile(file);
                if (!validation.isValid) {
                  message.error(validation.error);
                  return false;
                }
              }
              return true;
            };
            
            // 执行校验
            validateFiles().then(isValid => {
              if (isValid) {
                // 如果选择了多个文件，只处理第一个并给出提示
                if (files.length > 1) {
                  message.info('只能上传一张图片，已选择第一张图片');
                }

                // 处理第一个文件
                if (files.length > 0) {
                  handleSingleFileUpload(files[0]);
                }
              }
            });
          }
        }}
        onClick={(e) => {
          // 阻止默认的文件选择行为，改为触发上传指南
          // 但在高清放大、图片取词、自动抠图、智能扩图和服装去色页面直接触发文件选择
          if (pageType === 'upscale' || pageType === 'extract' || 
              pageType === 'extend' || pageType === 'inpaint' || 
              pageType === 'matting' || pageType === 'bleach') {
            // 不阻止默认行为，直接打开文件选择器
            return;
          }
          // 其他页面仍使用原有逻辑
          e.preventDefault();
          e.stopPropagation();
          onShowGuide?.();
        }}
        multiple={isMultipleAllowed()}
      />
      <label 
        htmlFor={id} 
        className={`upload-zone ${panels.length > 0 ? 'compact' : ''} ${isDragging ? 'dragging' : ''}`}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="upload-content">
          {panels.length === 0 && (
            <div className="upload-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 14V6H13V14H11Z" fill="currentColor"/>
                <path d="M7 10L12 5L17 10H7Z" fill="currentColor"/>
                <path d="M6 18V16H18V18H6Z" fill="currentColor"/>
              </svg>
            </div>
          )}
          <div className="upload-text">
            <span className="primary-text">
              {panels.length > 0 && !(pageType === 'fashion' && uploadType === 'reference') ? "继续上传" : (
                <>拖拽 或 点击上传<br /><span className="highlight">{getUploadText()}</span></>
              )}
            </span>
            <span className="secondary-text">
              {panels.length > 0 ? (
                <div className="requirements-container">
                  支持{UPLOAD_CONFIG.getSupportedTypesString()}
                  <span className="divider"></span>
                  文件{UPLOAD_CONFIG.maxSize}MB以内
                  <span className="divider"></span>
                  分辨率范围 {UPLOAD_CONFIG.getResolutionString()}px
                </div>
              ) : (
                <>
                  支持 {UPLOAD_CONFIG.getSupportedTypesString()}
                  <br />
                  文件{UPLOAD_CONFIG.maxSize}MB以内
                  <br />
                  分辨率范围 {UPLOAD_CONFIG.getResolutionString()}px
                </>
              )}
            </span>
          </div>
        </div>
      </label>
    </div>
  );
};

UploadBox.propTypes = {
  id: PropTypes.string.isRequired,
  onUpload: PropTypes.func,
  onShowGuide: PropTypes.func,
  onUploadResult: PropTypes.func,
  panels: PropTypes.array,
  className: PropTypes.string,
  showSupportTag: PropTypes.bool,
  pageType: PropTypes.oneOf(['fashion', 'try-on', 'background', 'recolor', 'fabric', 'trending', 'upscale', 'extract', 'extend', 'inpaint', 'matting', 'bleach']),
  uploadType: PropTypes.oneOf(['clothing', 'foreground', 'pattern', 'source', 'reference', 'printing']),
  uploadSubType: PropTypes.string
};

export default UploadBox; 

