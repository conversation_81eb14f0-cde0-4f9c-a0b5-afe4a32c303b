/* 导入统一样式 */
@import '../../styles/scrollbars.css';

/* 任务卡片基础样式 */
.task-card {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
  margin-bottom: var(--spacing-xxs);
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

/* 新任务的动画效果 */
.task-card.new-task {
  animation: taskAppear 0.5s ease-out;
}

@keyframes taskAppear {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.task-card:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(0);
}

/* 任务头部样式 */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-light);
}

.task-info {
  display: flex;
  align-items: center;
  gap: 18px;
}

.task-time {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
}

.task-id-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-xxs);
  font-family: var(--font-family);
}

.task-id-label {
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
}

.task-id {
  font-family: var(--font-mono);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.copy-id-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  padding: var(--spacing-xxs);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: none;
}

.copy-id-btn:hover {
  color: var(--brand-primary);
}

/* 任务操作按钮样式 */
.task-actions {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
  flex-wrap: wrap;
}

.edit-btn {
  height: 28px;
  padding: 0 var(--spacing-sm);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: var(--transition-normal);
  margin-right: var(--spacing-xs);
}

.edit-btn:disabled {
  background: var(--bg-secondary);
  border-color: var(--border-light);
  color: var(--text-disabled);
  cursor: not-allowed;
}

.edit-btn:not(:disabled):hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.more-actions {
  position: relative;
}

.more-btn {
  width: 28px;
  height: 28px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
}

.more-btn:disabled {
  background: var(--bg-secondary);
  border-color: var(--border-light);
  cursor: not-allowed;
}

.more-btn:disabled span {
  opacity: 0.5;
}

.more-btn:not(:disabled):hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.more-btn span {
  display: block;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: var(--text-secondary);
  position: relative;
  transition: var(--transition-normal);
}

.more-btn span::before,
.more-btn span::after {
  content: '';
  position: absolute;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: var(--text-secondary);
  transition: var(--transition-normal);
}

.more-btn:not(:disabled):hover span,
.more-btn:not(:disabled):hover span::before,
.more-btn:not(:disabled):hover span::after {
  background: var(--brand-primary);
}

.more-btn span::before {
  left: -5px;
}

.more-btn span::after {
  right: -5px;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--spacing-xxs);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-xxs);
  min-width: 120px;
  display: none;
  z-index: 1000;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  width: 100%;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: none;
  background: none;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  text-align: left;
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.dropdown-item:hover {
  background: var(--bg-hover);
}

.dropdown-item.delete {
  color: var(--error-color);
}

.dropdown-item.delete:hover {
  background: var(--error-bg);
}

/* 透明背景 - 灰白格子，用于抠图结果显示 */
.image-slot.transparent-bg {
  background-image: linear-gradient(45deg, #d1d1d1 25%, transparent 25%),
                    linear-gradient(-45deg, #d1d1d1 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #d1d1d1 75%),
                    linear-gradient(-45deg, transparent 75%, #d1d1d1 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  background-color: var(--bg-primary);
}

[data-theme="dark"] .image-slot.transparent-bg {
  background-image: linear-gradient(45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(-45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, var(--bg-secondary) 75%),
                    linear-gradient(-45deg, transparent 75%, var(--bg-secondary) 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  background-color: var(--bg-primary);
}

/* 图片网格样式 */
.task-images {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  min-height: 128px;
}

.image-slot {
  position: relative;
  aspect-ratio: 3/4;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.image-slot img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  transition: var(--transition-transform);
}

.image-slot:hover img {
  transform: scale(1);
}

/* 完全重写遮罩实现方式 */
.image-slot::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--bg-overlay);
  backdrop-filter: var(--blur-sm);
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 1;
  border-radius: var(--radius-lg);
  pointer-events: none;
  clip-path: inset(0 round var(--radius-lg));
  -webkit-clip-path: inset(0 round var(--radius-lg));
}

/* 添加第二层遮罩，专门处理边缘 */
.image-slot::after {
  content: '';
  position: absolute;
  inset: -1px; /* 向外扩展1px */
  border-radius: calc(var(--radius-lg) + 1px);
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 0;
  pointer-events: none;
  box-shadow: inset 0 0 0 1px var(--bg-primary);
}

[data-theme="dark"] .image-slot::before {
  background: rgba(0, 0, 0, 0.9); /* 使用更深的黑色 */
  clip-path: inset(0 round var(--radius-lg));
  -webkit-clip-path: inset(0 round var(--radius-lg));
}

[data-theme="dark"] .image-slot::after {
  background: var(--bg-primary);
  opacity: 0;
}

.image-slot:hover::before {
  opacity: 1;
  pointer-events: auto;
}

[data-theme="dark"] .image-slot:hover::after {
  opacity: 0.5;
}

.image-slot .image-actions {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  z-index: 2;
}

.image-slot:hover .image-actions {
  opacity: 1;
  pointer-events: auto;
}

.image-actions .view-details-btn {
  background: var(--bg-inverse);
  color: var(--color-white);
  border: none;
  border-radius: var(--radius-sm);
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  box-shadow: var(--shadow-btn);
  backdrop-filter: none;
  transition: none;
}

/* 移动端隐藏查看详情按钮 */
@media (max-width: 768px) {
  .image-actions .view-details-btn {
    display: none !important;
  }
}

.image-actions .view-details-btn:hover {
  color: var(--brand-primary);
  transition: none;
}

.image-actions .download-btn {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background: var(--bg-inverse);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-btn);
  backdrop-filter: none;
  transition: none;
}

.image-actions .download-btn:hover {
  background: var(--bg-inverse);
}

.image-actions .download-btn svg {
  width: var(--icon-size-sm);
  height: var(--icon-size-sm);
  color: var(--color-white);
  transition: none;
}

.image-actions .download-btn:hover svg {
  color: var(--brand-primary);
}

.image-actions .drag-upload-btn {
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-sm);
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  background: var(--bg-inverse);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  box-shadow: var(--shadow-btn);
  backdrop-filter: none;
  transition: none;
}

.image-actions .drag-upload-btn:active {
  cursor: grabbing;
}

.image-actions .drag-upload-btn:hover {
  background: var(--bg-inverse);
}

.image-actions .drag-upload-btn svg {
  width: var(--icon-size-sm);
  height: var(--icon-size-sm);
  color: var(--color-white);
  transition: none;
}

.image-actions .drag-upload-btn:hover svg {
  color: var(--brand-primary);
}

.image-actions .task-preview-btn {
  position: absolute !important;
  right: var(--spacing-sm) !important;
  bottom: var(--spacing-sm) !important;
  top: auto !important;
  left: auto !important;
  width: 36px !important;
  height: 36px !important;
  border-radius: var(--radius-full) !important;
  background: var(--bg-inverse) !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  box-shadow: var(--shadow-btn) !important;
  backdrop-filter: none !important;
  transition: none !important;
  z-index: 20 !important;
  margin: 0 !important;
  transform: none !important;
  color: var(--color-white) !important;
  font-size: initial !important;
  text-shadow: none !important;
}

.image-actions .task-preview-btn:hover {
  background: var(--bg-inverse) !important;
}

.image-actions .task-preview-btn svg {
  width: 21px !important;
  height: 21px !important;
  color: var(--color-white) !important;
  transition: none !important;
}

.image-actions .task-preview-btn:hover svg {
  color: var(--brand-primary) !important;
}

/* 处理中状态样式 */
.processing-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.processing-indicator .spinner {
  width: var(--icon-size-md);
  height: var(--icon-size-md);
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--brand-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.processing-indicator span {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* 空槽样式 */
.empty-slot {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.empty-slot span {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* 暂无图片样式 */
.empty-message {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  font-size:  var(--font-size-sm);
  padding: 16px 0;
}

.empty-message span {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* 图片加载失败提示 */
.load-error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--error-color);
  font-size: var(--font-size-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 移动端触摸支持 - 确保hover状态在移动端保持稳定 */
@media (hover: none) and (pointer: coarse) {
  /* 移动端设备：使用touch样式替代hover */
  .image-slot {
    -webkit-tap-highlight-color: transparent;
  }
  
  /* 在移动端，点击后保持激活状态 */
  .image-slot:active::before,
  .image-slot.touch-active::before {
    opacity: 1 !important;
    pointer-events: auto !important;
  }
  
  .image-slot:active .image-actions,
  .image-slot.touch-active .image-actions {
    opacity: 1 !important;
    pointer-events: auto !important;
  }
  
  [data-theme="dark"] .image-slot:active::after,
  [data-theme="dark"] .image-slot.touch-active::after {
    opacity: 0.5 !important;
  }
  
  /* 移动端长按保持效果 */
  .image-slot:focus-within::before {
    opacity: 1 !important;
    pointer-events: auto !important;
  }
  
  .image-slot:focus-within .image-actions {
    opacity: 1 !important;
    pointer-events: auto !important;
  }
  
  [data-theme="dark"] .image-slot:focus-within::after {
    opacity: 0.5 !important;
  }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .task-images {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 8px !important;
  }
  
  /* 隐藏拖动上传按钮 */
  .image-actions .drag-upload-btn {
    display: none !important;
  }
  
  .task-header {
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    gap: 0 !important;
  }
  
  .task-info {
    flex: 1 1 auto !important;
    min-width: 0 !important;
    margin-right: 8px !important;
  }
  
  .task-actions {
    flex: none !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 0 !important;
  }
  
  .more-actions {
    margin-left: 0 !important;
  }
  
  .edit-btn {
    flex: none;
    margin-right: 0;
    min-width: auto;
    width: auto;
  }
  
  .more-btn {
    width: 28px !important;
    height: 28px !important;
    font-size: var(--font-size-xs) !important;
  }
}

@media (max-width: 480px) {
  .task-images {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 6px !important;
  }
  
  .task-info {
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
  }
  
  .task-time {
    font-size: 11px;
    white-space: nowrap;
  }
  
  .task-id-container {
    font-size: 11px;
  }
  
  .task-id-label {
    font-size: 11px;
  }
  
  .task-id {
    font-size: 11px;
  }
  
  .more-btn {
    width: 24px !important;
    height: 24px !important;
    font-size: var(--font-size-xs) !important;
  }
}

@media (max-width: 360px) {
  .task-images {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 4px !important;
  }
  
  .task-info {
    flex-direction: row;
    align-items: center;
    gap: 6px;
    flex-wrap: wrap;
  }
  
  .task-time {
    font-size: 10px;
    white-space: nowrap;
  }
  
  .task-id-container {
    font-size: 10px;
  }
  
  .task-id-label {
    font-size: 10px;
  }
  
  .task-id {
    font-size: 10px;
  }
  
  .more-btn {
    width: 22px !important;
    height: 22px !important;
    font-size: var(--font-size-xs) !important;
  }
}

/* 新增添加为虚拟模特按钮样式 */
.exclusive-model-btn {
  height: 32px;
  padding: 0 var(--spacing-sm);
  background: var(--brand-gradient);
  border: none;
  border-radius: var(--radius-sm);
  color: var(--text-inverse);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: var(--spacing-xs);
  white-space: nowrap;
}

.exclusive-model-btn:hover {
  filter: brightness(1.1);
}

.exclusive-model-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 添加手动URL输入区域样式 */
.api-url-input {
  margin-bottom: 15px;
  padding: 8px;
  background-color: #f0f4f8;
  border-radius: 4px;
}

.api-url-input .input-group {
  display: flex;
  gap: 8px;
}

.api-url-input input {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  font-size: 13px;
}

.api-url-input button {
  background-color: #4299e1;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 13px;
}

.api-url-input button:hover {
  background-color: #3182ce;
}

.api-url-help {
  margin-top: 5px;
  font-size: 11px;
  color: #718096;
}

/* 强制URL图片测试区域样式 */
.forced-image-section {
  grid-column: 1 / -1;
  margin-top: 20px;
  padding: 15px;
  background-color: #ebf8ff;
  border: 1px solid #bee3f8;
  border-radius: 6px;
}

.forced-image-section h4 {
  margin: 0 0 10px 0;
  color: #2b6cb0;
  font-size: 14px;
}

.forced-images {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
}

@media (max-width: 1200px) {
  .forced-images {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .forced-images {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .forced-images {
    grid-template-columns: 1fr;
  }
}

.task-progress {
  position: relative;
  width: 100%;
  height: 4px;
  background-color: var(--bg-secondary);
  border-radius: 2px;
  overflow: hidden;
  margin: 8px 0;
}

.progress-bar {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: -20px;
  right: 0;
  font-size: 12px;
  color: #666;
}

/* 任务生成中简洁进度区域样式 */
.task-processing-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 128px;
  background: var(--bg-secondary, #f7fafd);
  border-radius: var(--radius-lg, 10px);
  margin: 24px 0 12px 0;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.04);
  border: 1px solid var(--border-light, #e6f0fa);
  transition: background 0.3s;
}

.processing-desc {
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin: 0 0 12px 0;
  letter-spacing: 1px;
  text-align: center;
  color: var(--text-secondary);
}

.task-content-area {
  min-height: 128px;
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.progress-percent {
  position: absolute;
  top: 24px;
  transform: translateX(-50%);
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
  pointer-events: none;
  transition: transform 0.3s ease;
}

.task-progress-wrapper {
  position: relative;
  width: 100%;
  padding-left: 28px;
  padding-right: 28px;
}

.processing-row {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 8px;
}

.processing-desc,
.processing-percent {
  font-size: var(--font-size-sm);
  line-height: 1.6;
  font-weight: 500;
  color: var(--text-secondary);
}

.processing-percent {
  margin-left: 12px;
  font-size: 12px;
} 