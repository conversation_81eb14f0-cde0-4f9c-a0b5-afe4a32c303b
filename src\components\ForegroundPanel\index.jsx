import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { message } from 'antd';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';

/**
 * 前景图面板组件 - 用于背景页面中展示上传的前景图片和状态
 * 
 * 此组件专门用于background(换背景)页面，解决了与服装组件共用的问题
 */
const ForegroundPanel = ({
  panel,
  onExpandClick,
  onDelete,
  onReupload,
  onStatusChange,
  isActive,
  onPanelsChange
}) => {
  // 添加拖拽状态管理
  const [isDragOver, setIsDragOver] = useState(false);

  // 处理拖拽进入
  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  // 处理拖拽离开
  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // 只有当离开整个组件时才重置状态
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
    }
  };

  // 处理拖拽悬停
  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // 确保拖拽悬停时保持状态
    if (!isDragOver) {
      setIsDragOver(true);
    }
  };

  // 处理拖拽放下
  const handleDrop = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
    
    // 首先检查是否有自定义的拖拽数据（从TaskPanel拖拽过来的图片）
    try {
      const customData = e.dataTransfer.getData('application/json');
      if (customData) {
        const dragData = JSON.parse(customData);
        if (dragData.type === 'image-upload') {
          // 处理从TaskPanel拖拽过来的图片
          await handleImageFromTaskPanel(dragData);
          return;
        }
      }
    } catch (error) {
      console.log('解析拖拽数据失败，尝试处理文件:', error);
    }
    
    // 获取拖拽的文件
    const files = Array.from(e.dataTransfer.files);
    
    if (files.length > 0) {
      // 先删除当前面板
      if (panel && panel.componentId) {
        onDelete?.(panel.componentId);
        message.success('已删除前景图面板');
      }
      
      // 延迟一点时间后处理拖拽的文件
      setTimeout(() => {
        // 处理第一个文件
        const file = files[0];
        if (file && file.type.startsWith('image/')) {
          // 直接处理文件上传，创建新的面板
          const imageId = `foreground-${Date.now()}`;
          const imageUrl = URL.createObjectURL(file);
          
          const newPanel = {
            componentId: imageId,
            title: '前景',
            status: 'completed',
            serverFileName: file.name,
            url: imageUrl,
            fileInfo: {
              name: file.name,
              size: file.size,
              type: file.type,
              serverFileName: file.name
            },
            type: 'foreground',
            file: file,
            processInfo: {
              serverFileName: file.name
            }
          };
          
          // 通知父组件创建新面板
          onPanelsChange([newPanel]);
          message.success('前景图上传成功');
        } else {
          message.error('请拖拽图片文件');
        }
      }, 50);
    } else {
      // 如果没有文件，只执行删除操作
      if (panel && panel.componentId) {
        onDelete?.(panel.componentId);
        message.success('已删除前景图面板');
      }
    }
  };

  // 处理从TaskPanel拖拽过来的图片
  const handleImageFromTaskPanel = async (dragData) => {
    try {
      const { imageUrl, fileName, taskId, imageIndex } = dragData;
      
      // 先删除当前面板
      if (panel && panel.componentId) {
        onDelete?.(panel.componentId);
        message.success('已删除前景图面板');
      }
      
      // 延迟一点时间后处理拖拽的图片
      setTimeout(async () => {
        try {
          // 将HTTP转换为HTTPS，解决混合内容问题
          const httpsUrl = imageUrl.replace(/^http:/, 'https:');
          
          // 从URL获取图片数据
          const response = await fetch(httpsUrl);
          if (!response.ok) {
            throw new Error('无法获取图片数据');
          }
          
          const blob = await response.blob();
          
          // 创建一个File对象
          const file = new File([blob], fileName, {
            type: blob.type || 'image/jpeg',
            lastModified: Date.now()
          });
          
          console.log(`处理从任务面板拖拽的图片: ${fileName}, 任务ID: ${taskId}, 图片索引: ${imageIndex}`);
          
          // 创建新的面板
          const imageId = `foreground-${Date.now()}`;
          const newImageUrl = URL.createObjectURL(file);
          
          const newPanel = {
            componentId: imageId,
            title: '前景',
            status: 'completed',
            serverFileName: fileName,
            url: newImageUrl,
            fileInfo: {
              name: fileName,
              size: file.size,
              type: file.type,
              serverFileName: fileName
            },
            type: 'foreground',
            file: file,
            processInfo: {
              serverFileName: fileName
            }
          };
          
          // 通知父组件创建新面板
          onPanelsChange([newPanel]);
          message.success('前景图重新上传成功');
          
        } catch (error) {
          console.error('处理拖拽图片时出错:', error);
          message.error('处理图片失败，请重试');
        }
      }, 50);
      
    } catch (error) {
      console.error('处理从TaskPanel拖拽的图片时出错:', error);
      message.error('处理图片失败，请重试');
    }
  };

  // 处理上传结果
  const handleUploadResult = (results) => {
    console.log('处理上传结果:', JSON.stringify(results, null, 2));
    
    try {
      // 如果是标准的success/results格式
      if (results.success && Array.isArray(results.results)) {
        const newPanels = results.results.map((result, index) => {
          console.log(`处理结果项 #${index+1}:`, result);
          
          // 检查是否有relativePath字段
          let processedImageUrl = null;
          if (result.processedFile) {
            if (result.relativePath) {
              processedImageUrl = `http://localhost:3002${result.relativePath}`;
              console.log('使用相对路径构建URL:', processedImageUrl);
            } else {
              // 直接使用上传图片，不获取处理后的图片
              console.log('使用默认路径构建URL:', processedImageUrl);
            }
          }
          
          return {
            componentId: `foreground-${index + 1}`,
            title: results.results.length === 1 ? '前景' : `前景 ${index + 1}`,
            status: result.error ? 'error' : 'completed',
            error: result.error || null,
            serverFileName: result.serverFileName,
            processedFile: result.processedFile,
            // 保存完整的processInfo以便后续使用
            processInfo: {
              serverFileName: result.serverFileName,
              processedFile: result.processedFile,
              relativePath: result.relativePath,
              url: result.url
            },
            fileInfo: {
              ...(result.fileInfo || {}),
              format: result.fileInfo?.type || 'image/png',
              serverFileName: result.serverFileName
            },
            showOriginal: true
          };
        });
        console.log('更新后的面板数据:', newPanels);
        onPanelsChange(newPanels);
        return;
      }

      // 处理初始的panels类型数据
      if (results.type === 'panels' && Array.isArray(results.panels)) {
        const panels = results.panels.map((panel, index) => ({
          ...panel,
          title: results.panels.length === 1 ? '前景' : `前景 ${index + 1}`,
          // 确保从上传结果中保存processInfo
          processInfo: panel.processInfo || null,
          // 确保保存服务器文件名
          serverFileName: panel.serverFileName || panel.processInfo?.serverFileName || panel.componentId
        }));
        onPanelsChange(panels);
        return;
      }

      throw new Error('无法识别的响应格式');

    } catch (error) {
      console.error('处理上传结果时出错:', error);
      const errorPanel = {
        componentId: `foreground-${Date.now()}`,
        title: '前景',
        status: 'error',
        error: error.message || '处理失败',
        serverFileName: results?.serverFileName || results?.filename,
        showOriginal: true
      };
      onPanelsChange([errorPanel]);
    }
  };

  const handleExpandClick = (e) => {
    const buttonRect = e.currentTarget.getBoundingClientRect();
    onExpandClick(panel, {
      top: buttonRect.top,
      left: buttonRect.left + buttonRect.width
    });
  };

  const handleDelete = () => {
    if (panel && panel.componentId) {
      onDelete?.(panel.componentId);
      message.success('已删除前景图面板');
    }
  };

  const handleReupload = () => {
    if (panel && panel.componentId) {
      onReupload?.(panel);
    }
  };

  const getImageInfo = () => {
    if (!panel || !panel.processedFile) return null;

    if (panel.fileInfo) {
      return {
        size: `${(panel.fileInfo.size / (1024 * 1024)).toFixed(2)} MB`,
        resolution: `${panel.fileInfo.width} x ${panel.fileInfo.height} px`,
        format: panel.fileInfo.format
      };
    }

    return {
      size: '--',
      resolution: '--',
      format: '--'
    };
  };

  return (
    <div 
      className={`panel-component ${isDragOver ? 'drag-over' : ''}`}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <div className="component-header">
        <div className="component-info">
          <ThumbnailWithPreview
            imageUrl={panel.processedFile || panel.url}
            alt="前景图上传结果"
            status={panel.status}
            error={panel.error}
            onStatusChange={onStatusChange}
            featureName="前景图预览"
            transparentBg={true}
          />
          <div className="component-text">
            <h3>前景</h3>
            <div className="component-content">
              <p>
                {panel.status === 'completed' && '上传完成'}
                {panel.status === 'processing' && '自动抠图中...'}
                {panel.status === 'error' && panel.error}
              </p>
            </div>
          </div>
        </div>
        <button 
          className={`expand-btn ${isActive ? 'active' : ''}`}
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

ForegroundPanel.propTypes = {
  panel: PropTypes.shape({
    componentId: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    status: PropTypes.oneOf(['processing', 'completed', 'error']).isRequired,
    error: PropTypes.string,
    processedFile: PropTypes.string,
    url: PropTypes.string,
    serverFileName: PropTypes.string,
    originalImage: PropTypes.string,
    type: PropTypes.string,
    source: PropTypes.string,
    fileInfo: PropTypes.shape({
      size: PropTypes.number,
      width: PropTypes.number,
      height: PropTypes.number,
      format: PropTypes.string,
      serverFileName: PropTypes.string
    }),
    showOriginal: PropTypes.bool,
  }).isRequired,
  onExpandClick: PropTypes.func.isRequired,
  onDelete: PropTypes.func,
  onReupload: PropTypes.func,
  onStatusChange: PropTypes.func,
  isActive: PropTypes.bool,
  onPanelsChange: PropTypes.func.isRequired,
};

export default ForegroundPanel; 