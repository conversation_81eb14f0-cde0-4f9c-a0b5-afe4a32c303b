/**
 * ReferenceImageTool 组件样式
 * 
 * 参考图片工具组件的样式定义，与其他工具组件保持一致的视觉风格
 */
@import '../../styles/theme.css';

/* 参考图片工具容器 */
.reference-image-tool {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px 8px;
  background: var(--bg-primary);
  border-radius: 0;
  max-width: 100%;
}

.reference-image-tool .tools-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 参考图片头部 */
.reference-image-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0px;
  padding: 0 2px;
}

.reference-image-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
  text-align: center;
}

/* 参考图片缩略图 */
.reference-image-thumbnail {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 缩略图容器样式 */
.thumbnail-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 3px;
}

/* 悬浮预览按钮样式 */
.thumbnail-overlay {
  position: absolute;
  inset: 0;
  background: var(--bg-mask);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-normal);
  cursor: pointer;
  border-radius: 3px;
}

/* 悬浮时显示预览按钮 */
.thumbnail-container:hover .thumbnail-overlay {
  opacity: 1;
}

.thumbnail-overlay svg {
  width: var(--font-size-xl);
  height: var(--font-size-xl);
  color: #fff;
}

/* 移动端隐藏 */
@media (max-width: 768px) {
  .reference-image-tool {
    display: none;
  }
  
  .reference-image-divider {
    display: none;
  }
}

/* 暗色主题适配 */
[data-theme="dark"] .reference-image-tool {
  background: var(--bg-primary);
}

[data-theme="dark"] .reference-image-thumbnail {
  background: var(--bg-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .reference-image-label {
  color: var(--text-secondary);
} 