/* 导入统一样式 */
@import '../../styles/modals.css';
@import '../../styles/buttons.css';
@import '../../styles/inputs.css';
@import '../../styles/tabs.css';
@import '../../styles/panels.css';
@import '../../styles/scrollbars.css';
@import '../../styles/common.css';

/* 场景网格特定样式 */
.scenes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 12px;
  margin-top: 16px;
  padding: 0;
  width: 100%;
  will-change: transform; /* 添加硬件加速 */
}

/* 骨架屏样式 */
.scene-skeleton {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  width: 100%;
}

.scene-skeleton-item {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  aspect-ratio: 2/3;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
}

.scene-skeleton-image {
  width: 100%;
  height: calc(100% - 40px);
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.scene-skeleton-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  padding: var(--spacing-xs);
  background: var(--bg-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0;
  border-top: 1px solid var(--border-lighter);
}

.scene-skeleton-text {
  width: 70%;
  height: 16px;
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
}

.scene-skeleton-button {
  width: 24px;
  height: 24px;
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 场景选择模态框样式 */
.scene-select-modal-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* 使用dvh确保在移动端正确显示 */
  height: 100dvh;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999; /* 大幅提高z-index，确保在所有内容之上，包括移动端导航栏 */
  background-color: var(--bg-mask);
}

.scene-select-modal {
  composes: base-modal;
}

.scene-select-modal .modal-content {
  pointer-events: auto;
  width: 1200px;
  height: calc(100vh - 150px);
  min-height: 600px;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  margin-left: 0 !important; /* 覆盖全局样式 */
  z-index: 100000; /* 确保弹窗内容在包装器之上 */
}

/* 拖动相关样式 */
.scene-select-modal .modal-header,
.scene-select-modal .modal-tabs {
  cursor: grab;
  user-select: none;
}

.scene-select-modal .modal-header:active,
.scene-select-modal .modal-tabs:active {
  cursor: grabbing;
}

.scene-select-modal.dragging .modal-header,
.scene-select-modal.dragging .modal-tabs {
  cursor: grabbing;
}

.scene-select-modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  margin-top: 0;
  padding-bottom: 8px;
  will-change: transform;
  -webkit-overflow-scrolling: touch;
}

/* 更新标签页样式 */
.scene-select-modal .modal-header {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  padding: 20px 20px 0;
  margin: 0;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  background: var(--bg-primary);
}

/* 场景卡片样式 */
.scene-item {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  cursor: pointer;
  aspect-ratio: 2/3;
  padding: 0;
  width: 100%;
  display: block;
  border: 1px solid var(--border-light);
  transition: var(--transition-normal);
}

.scene-item:hover {
  border-color: var(--brand-primary);
  box-shadow: var(--shadow-brand);
}

.scene-item.selected {
  border-color: var(--brand-primary);
  box-shadow: var(--shadow-brand);
}

.scene-preview {
  position: relative;
  width: 100%;
  height: 100%;
  display: block;
}

.scene-preview img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 场景标题和预览按钮样式 */
.scene-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-xs);
  background: var(--bg-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0;
  border-top: 1px solid var(--border-lighter);
}

/* 新增上传入口样式 */
.upload-entry {
  border: 1px dashed var(--border-color) !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: var(--bg-secondary);
  position: relative;
  aspect-ratio: 2/3;
}

.upload-entry:hover {
  border: 1px dashed var(--brand-primary) !important;
  background: var(--brand-primary-light);
}

/* 拖拽悬停状态 */
.upload-entry.drag-over {
  border: 1px dashed var(--brand-primary) !important;
  background: var(--brand-primary-light);
}





/* 上传后的样式 */
.upload-entry.uploaded {
  border: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.upload-entry.uploaded:hover {
  border: 1px solid var(--brand-primary) !important;
  background: var(--brand-primary-light);
}

/* 上传后且被选中的样式 - 增加特异性和强制执行 */
.card-item.upload-entry.uploaded.selected {
  border: 1px solid var(--brand-primary) !important;
  background: var(--brand-primary-light);
}

.upload-entry .upload-area-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-entry .upload-label {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.upload-entry .upload-icon {
  font-size: 24px;
  color: var(--brand-primary);
  margin-bottom: 16px;
  opacity: 0.8;
  display: flex;
  align-items: center;
  justify-content: center;
}



.upload-entry .upload-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: var(--text-secondary);
  font-size: 14px;
}

.upload-entry .upload-text .upload-tip {
  font-size: 12px;
  color: var(--text-tertiary);
  margin-top: 4px;
}

.upload-entry .upload-text .upload-drag-tip {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-top: 4px;
  opacity: 0.8;
}





.scene-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.scene-number {
  font-size: var(--font-size-sm);
  opacity: 0.85;
  color: var(--text-secondary);
  font-weight: 500;
}

.scene-name {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.preview-button {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-sm);
  border: none;
  background: var(--bg-hover);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-normal);
  padding: 0;
  flex-shrink: 0;
}

.preview-button:hover {
  background: var(--bg-active);
  color: var(--text-primary);
}

.preview-button svg {
  width: var(--font-size-md);
  height: var(--font-size-md);
}

/* 自定义场景区域样式 */
.custom-scene {
  padding: 0 var(--spacing-lg);
}

.custom-scene .filter-section {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.custom-scene .section-title {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-weight: 500;
}

.custom-scene .prompt-input {
  margin-bottom: var(--spacing-lg);
}

.custom-scene .prompt-input label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.custom-scene .prompt-textarea {
  composes: textarea-base;
}

/* 图片预览弹窗样式 - 已迁移到独立组件 */

/* 场景列表网格布局 */
.scenes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 16px;
}

@media (max-width: 480px) {
  .scenes-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

.card-item {
  aspect-ratio: 2/3;
  border-radius: var(--radius-md);
  overflow: hidden;
  position: relative;
  border: 1px solid var(--border-color);
  transition: var(--transition-normal);
  cursor: pointer;
}

.card-item:hover {
  border-color: var(--brand-primary);
  box-shadow: var(--shadow-brand);
}

.card-item.selected {
  border: 1px solid var(--brand-primary);
  box-shadow: var(--shadow-brand);
}

.card-preview {
  width: 100%;
  height: 100%;
  position: relative;
}

.card-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.delete-button {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  opacity: 0.8;
  transition: var(--transition-normal);
}

.delete-button:hover {
  opacity: 1;
  background: var(--bg-secondary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.card-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-xs);
  background: var(--bg-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid var(--border-lighter);
}

.card-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  overflow: hidden;
}

.card-number {
  font-size: var(--font-size-sm);
  opacity: 0.85;
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.card-name {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 删除自定义的预览关闭按钮样式，使用统一样式 */

/* 添加移动端适配样式 */
@media (max-width: 768px) {
  .scene-select-modal .modal-content {
    width: 95% !important;
    min-height: 400px !important;
    max-height: calc(100vh - 10px) !important;
    max-height: calc(100dvh - 10px) !important;
  }
}

@media (max-width: 480px) {
  .scene-select-modal .modal-content {
    width: 98% !important;
    min-height: 450px !important;
    max-height: calc(100vh - 40px) !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
  }
}

@media (max-width: 360px) {
  .scene-select-modal .modal-content {
    width: 100% !important;
    min-height: 400px !important;
    max-height: calc(100vh - 20px) !important;
    border-radius: var(--radius-md) !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
  }
}

/* 真实移动设备适配 */
@media (hover: none) and (pointer: coarse) {
  .scene-select-modal-wrapper {
    padding: env(safe-area-inset-top, 0px) env(safe-area-inset-right, 0px) env(safe-area-inset-bottom, 0px) env(safe-area-inset-left, 0px);
  }

  .scene-select-modal .modal-content {
    max-height: calc(100dvh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px) - 20px) !important;
  }
}

/* 横屏模式适配 */
@media (hover: none) and (pointer: coarse) and (orientation: landscape) {
  .scene-select-modal .modal-content {
    height: calc(100dvh - 20px) !important;
    max-height: calc(100dvh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px) - 20px) !important;
  }
}

.scene-type-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 18px;
}
.scene-type-filter .filter-option {
  padding: 4px 18px;
  border: 1px solid var(--border-light);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-normal);
}
.scene-type-filter .filter-option:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}
.scene-type-filter .filter-option.active {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}
 




/* 移动端拖拽优化 */
@media (hover: none) and (pointer: coarse) {
  .upload-entry .upload-text .upload-drag-tip {
    display: none; /* 在移动端隐藏拖拽提示 */
  }
}
 