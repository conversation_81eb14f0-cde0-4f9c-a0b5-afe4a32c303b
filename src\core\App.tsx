import React, { useState, useEffect, useRef } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import './App.css';
import '../pages/subscribe/Subscribe.css';
import ErrorMessage from '../components/ErrorMessage';
// 导入认证组件样式
import '../components/Auth/styles/index.css';
import '../styles/close-buttons.css'; // 导入关闭按钮样式
import Footer from '../components/Footer'; // 导入Footer组件
import '../utils/websocketLifecycleManager';
// 页面组件导入
import Subscribe from '../pages/subscribe/Subscribe';
import PrivacyPolicy from '../pages/user/privacy/index.jsx';
import TermsOfService from '../pages/user/terms/index.jsx';
import ModelPage from '../pages/model/index.jsx';
import StylePage from '../pages/style/index.jsx';
import ToolsPage from '../pages/tools/index.jsx';
import FashionPage from '../pages/model/fashion/index.jsx';
import TryOnPage from '../pages/model/try-on/index.jsx';
import OptimizePage from '../pages/style/optimize/index.jsx';
import InspirationPage from '../pages/style/inspiration/index.jsx';
import RecolorPage from '../pages/model/recolor/index.jsx';
import FabricPage from '../pages/style/fabric/index.jsx';
import BackgroundPage from '../pages/model/background/index.jsx';
import VirtualPage from '../pages/model/virtual/index.jsx';
import TrendingPage from '../pages/style/trending/index.jsx';
import VideoPage from '../pages/video/index.jsx';
import ImgtextvideoPage from '../pages/video/imgtextvideo/index.jsx';
import MulimgvideoPage from '../pages/video/mulimgvideo/index.jsx';
import ExtractPage from '../pages/tools/extract/index.jsx';
import UpscalePage from '../pages/tools/upscale/index.jsx';
import MattingPage from '../pages/tools/matting/index.jsx';
import ExtendPage from '../pages/tools/extend/index.jsx';
import InpaintPage from '../pages/tools/inpaint/index.jsx';
import BleachPage from '../pages/tools/bleach/index.jsx';
import InformationPage from '../pages/user/information/index.jsx';
import ContactSupport from '../components/ContactSupport';
import DetailMigrationPage from '../pages/model/detail-migration/index.jsx';
import AccountPage from '../pages/user/account/index.jsx';
import HandFixPage from '../pages/model/hand-fix/index.jsx';
import ChangeModelPage from '../pages/model/change-model/index.jsx';
import ChangePosturePage from '../pages/model/change-posture/index.jsx';
import ChangeFacePage from '../pages/model/change-face/index.jsx';
import DrawingPage from '../pages/style/drawing/index.jsx';
import SoundControl from '../components/SoundControl';
import ThemeToggle from '../components/ThemeToggle';
import AdminPage from '../components/AdminPage/AdminPage';
import PromotionBanner from '../components/PromotionBanner';
import ImagePreviewModal from '../components/common/ImagePreviewModal';

// 图标导入
import { IconType } from 'react-icons';
import {
  FiList,
  FiCamera,
  FiImage,
  FiUsers,
  FiMaximize2,
  FiScissors,
  FiMinimize2,
  FiTool,
  FiPlus
} from 'react-icons/fi';

import {
  MdOutlineCheckroom,
  MdOutlinePalette,
  MdOutlineAutoAwesome,
  MdOutlineStyle,
  MdOutlineFavoriteBorder,
  MdOutlineFormatShapes,
  MdOutlineImageSearch,
  MdOutlineZoomOutMap,
  MdOutlineAutoFixHigh,
  MdOutlineDraw,
  MdOutlinePhotoCamera,
  MdOutlineSpaceDashboard,
  MdClose,
  MdOutlineSettings,
  MdOutlineLightMode,
  MdOutlineDarkMode,
  MdOutlineTexture,
  MdOutlineDescription,
  MdOutlineTextSnippet,
  MdOutlineDocumentScanner,
  MdOutlineVideocam,
  MdOutlineVideoLibrary,
  MdOutlineMovie,
  MdOutlineOndemandVideo,
  MdOutlineVideoFile,
  MdOutlinePlayCircle,
  MdOutlineChevronRight,
  MdOutlineChevronLeft,
  MdOutlineFace,
  MdOutlineTransform,
  MdOutlineHealing,
  MdOutlineSwitchAccount,
  MdOutlineHandshake,
  MdOutlineCenterFocusStrong,
  MdOutlineSchool,
  MdOutlineMoreHoriz,
  MdOutlineVolumeUp,
  MdOutlineVolumeOff,
  MdOutlineCleaningServices,
  MdOutlineWaterDrop
} from 'react-icons/md';

import { GiBeachBag, GiDress, GiTShirt, GiHand } from 'react-icons/gi';
import { IconContext } from 'react-icons';

// 数据和配置导入
import {
  GENDER_CATEGORIES,
  AGE_CATEGORIES,
  REGION_CATEGORIES,
  BODY_TYPE_CATEGORIES,
  SUIT_TYPES,
  STYLES
} from '../data/categories';

import { FEATURES_CONFIG, getAllFeatures } from '../config/features/features';
import { TIPS_CONFIG } from '../config/guides/usage-skills';
import showcaseConfig from '../config/showcase';
import { useAuth } from '../contexts/AuthContext';
import { AuthProvider } from '../contexts/AuthContext';
import { TaskProvider, useTaskContext } from '../contexts/TaskContext';
import audioService from '../services/AudioService';
import {
  SHOWCASE_BASE_URL,
  FUNCTION_CONFIGS,
} from '../config/showcase/showcase'; // Corrected import path

// 类型定义
interface Notification {
  type: string;
  message: string | React.ReactNode;
  duration?: number;
}

// 所有功能模块配置
const ALL_FEATURES: {
  [key: string]: string[];
} = {
  '款式设计': ['爆款开发', '爆款延伸', '款式优化', '灵感探索', '换面料', '生成线稿'],
  '模特图': ['时尚大片', '模特换装', '换模特', '服装复色', '换背景', '换姿势', '模特换脸', '虚拟模特', '细节还原', '手部修复'],
  'AI视频': ['图文成片', '多图成片'],
  '快捷工具': ['自动抠图', '智能扩图', '图片取词', '消除笔', '高清放大', '服装去色']
};

// 图标映射
const FEATURE_ICONS: { [key: string]: IconType } = {
  '时尚大片': MdOutlinePhotoCamera,
  '模特换装': MdOutlineCheckroom,
  '换模特': MdOutlineSwitchAccount,
  '服装复色': MdOutlinePalette,
  '换面料': MdOutlineTexture,
  '换背景': FiImage,
  '虚拟模特': MdOutlineFavoriteBorder,
  '细节还原': MdOutlineCenterFocusStrong,
  '手部修复': MdOutlineHandshake,
  '换姿势': MdOutlineAutoFixHigh,
  '模特换脸': MdOutlineFace,
  '爆款开发': MdOutlineAutoAwesome,
  '爆款延伸': MdOutlineTransform,
  '灵感探索': MdOutlineStyle,
  '款式优化': FiScissors,
  '生成线稿': MdOutlineDraw,
  '图文成片': MdOutlineOndemandVideo,
  '多图成片': MdOutlineVideoLibrary,
  '图片取词': MdOutlineDocumentScanner,
  '高清放大': MdOutlineImageSearch,
  '自动抠图': MdOutlineFormatShapes,
  '智能扩图': MdOutlineZoomOutMap,
  '消除笔': MdOutlineHealing,
  '服装去色': MdOutlineWaterDrop
};

// 默认首页侧边栏常用功能 - 已隐藏所有功能
const DEFAULT_COMMON: string[] = [];

// 导入Auth组件
import AuthModals from '../components/Auth/AuthModals';
import ProtectedRoute from '../components/ProtectedRoute';
import EditFeaturesModal from '../components/EditFeaturesModal';
import api from '../api';
import { useModal } from '../contexts/ModalContext';
import { getUserSubscription } from '../api/credits';
import Masonry from 'masonry-layout';
import DivergentPage from '../pages/style/divergent/index.jsx';

// 在组件内添加颜色插值函数
const GRADIENT_COLORS = ['#4158D0', '#C850C0', '#FFCC70'];
function interpolateColor(color1: string, color2: string, factor: number) {
  // color1, color2: '#RRGGBB', factor: 0~1
  const c1 = color1.match(/\w\w/g)!.map(x => parseInt(x, 16));
  const c2 = color2.match(/\w\w/g)!.map(x => parseInt(x, 16));
  const result = c1.map((v, i) => Math.round(v + (c2[i] - v) * factor));
  return `#${result.map(x => x.toString(16).padStart(2, '0')).join('')}`;
}
function getGradientColor(index: number, total: number) {
  if (total <= 2) return GRADIENT_COLORS[0];
  if (index === total - 1) return 'linear-gradient(45deg, #4158D0, #C850C0, #FFCC70)'; // 最后一张卡片用渐变色
  // 渐变分布在前n-1张
  const seg = (GRADIENT_COLORS.length - 1);
  const pos = index / (total - 2) * seg;
  const segIdx = Math.floor(pos);
  const localFactor = pos - segIdx;
  if (segIdx >= GRADIENT_COLORS.length - 1) return GRADIENT_COLORS[GRADIENT_COLORS.length - 1];
  return interpolateColor(GRADIENT_COLORS[segIdx], GRADIENT_COLORS[segIdx + 1], localFactor);
}

// 获取边框颜色的函数，确保返回纯色值
function getBorderColor(index: number, total: number) {
  if (total <= 2) return GRADIENT_COLORS[0];
  if (index === total - 1) return '#4158D0'; // 最后一张卡片使用第一个渐变色作为边框
  // 渐变分布在前n-1张
  const seg = (GRADIENT_COLORS.length - 1);
  const pos = index / (total - 2) * seg;
  const segIdx = Math.floor(pos);
  const localFactor = pos - segIdx;
  if (segIdx >= GRADIENT_COLORS.length - 1) return GRADIENT_COLORS[GRADIENT_COLORS.length - 1];
  return interpolateColor(GRADIENT_COLORS[segIdx], GRADIENT_COLORS[segIdx + 1], localFactor);
}

const tagToIcon: {[key: string]: IconType} = {
  // 款式设计
  'trending': MdOutlineAutoAwesome,
  'optimize': MdOutlineDraw,
  'inspiration': MdOutlineStyle,
  'fabric': MdOutlineTexture,
  'drawing': MdOutlineDraw,
  // 模特图
  'fashion': MdOutlinePhotoCamera,
  'tryon': MdOutlineCheckroom,
  'change-model': MdOutlineSwitchAccount,
  'change-posture': MdOutlineAutoFixHigh,
  'change-face': MdOutlineFace,
  'recolor': MdOutlinePalette,
  'background': FiImage,
  'virtual': MdOutlineFavoriteBorder,
  'detail-migration': MdOutlineCenterFocusStrong,
  'hand-fix': MdOutlineHandshake,
  // 快捷工具
  'matting': MdOutlineFormatShapes,
  'extend': MdOutlineZoomOutMap,
  'extract': MdOutlineDocumentScanner,
  'upscale': MdOutlineImageSearch
};

const App: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toggleMute, isSoundMuted } = useTaskContext();

  // 根据路径判断是否是后台管理员
  const isAdmin = location.pathname.startsWith('/admin');
  const auth = useAuth() as any;
  
  // 使用ModalContext
  const { modalState } = useModal();

  // 计算是否有弹窗打开
  const hasModalOpen = Object.values(modalState as any).some((modal: any) => modal.visible);

  // 状态变量
  const [isDarkTheme, setIsDarkTheme] = useState<boolean>(() => {
    const savedTheme = localStorage.getItem('theme');
    return savedTheme === 'dark'; // 默认使用明亮主题，只有当明确保存为dark时才使用暗黑主题
  });

  // 任务提示音状态
  const [isMuted, setIsMuted] = useState<boolean>(() => {
    return isSoundMuted();
  });

  // 处理任务提示音切换
  const handleToggleMute = () => {
    const newMutedState = toggleMute();
    setIsMuted(newMutedState);
    
    // 如果从静音状态切换到非静音状态，播放提示音作为示例
    if (!newMutedState) {
      // 短暂延迟确保状态已更新
      setTimeout(() => {
        // 先播放成功提示音
        audioService.playTaskComplete();
        // 1秒后播放错误提示音，让用户了解两种提示音的区别
        setTimeout(() => {
          audioService.playTaskError();
        }, 1000);
      }, 100);
    }
  };

  const [isSidebarOpen, setIsSidebarOpen] = useState<boolean>(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState<boolean>(() => {
    const savedState = localStorage.getItem('sidebarCollapsed');
    return savedState === 'true';
  });
  const [showCollapseBtn, setShowCollapseBtn] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<string>('首页');
  const [currentTipIndex, setCurrentTipIndex] = useState<number>(0);
  const [tipsPerView] = useState<number>(3);
  const [showEditModal, setShowEditModal] = useState<boolean>(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState<boolean>(false);
  const [showMobileTip, setShowMobileTip] = useState<boolean>(() => {
    const savedTime = localStorage.getItem('mobileTipClosedTime');
    if (savedTime) {
      const hours = (Date.now() - parseInt(savedTime)) / (1000 * 60 * 60);
      return hours >= 24;
    }
    return true;
  });
  const [showUserDropdown, setShowUserDropdown] = useState<boolean>(false);

  // 常用功能状态 - 已隐藏所有功能
  const [commonFeatures, setCommonFeatures] = useState<string[]>(() => {
    // 清空localStorage中保存的常用功能，确保不显示任何功能
    localStorage.removeItem('commonFeatures');
    return [];
  });

  // 认证相关状态变量
  const [showLoginModal, setShowLoginModal] = useState<boolean>(false);
  const [showResetPassword, setShowResetPassword] = useState<boolean>(false);

  // 通知状态
  const [notification, setNotification] = useState<Notification>({ type: '', message: '' });

  // 展示图片状态
  const [showcaseCount, setShowcaseCount] = useState<number>(28);
  const [showLoadMoreMessage, setShowLoadMoreMessage] = useState<boolean>(false);

  // 视频播放状态
  const [playingVideoId, setPlayingVideoId] = useState<number | null>(null);
  const [playingVideoUrl, setPlayingVideoUrl] = useState<string | null>(null);
  const [showVideoModal, setShowVideoModal] = useState<boolean>(false);

  // 图片预览状态
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null);
  const [showImageModal, setShowImageModal] = useState<boolean>(false);
  const [previewFeatureName, setPreviewFeatureName] = useState<string>('');

  // 联系客服状态
  const [showContactModal, setShowContactModal] = useState<boolean>(false);

  // 用户订阅状态
  const [userSubscription, setUserSubscription] = useState<any>(null);

  // 侧边栏引导图片状态
  const [hoveredSidebarIdx, setHoveredSidebarIdx] = useState<number | null>(null);
  const [showSidebarGuide, setShowSidebarGuide] = useState(false);
  const hoverTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [sidebarGuidePopup, setSidebarGuidePopup] = useState<null | { x: number, y: number, pageType: string }>(null);
  const [imageLoadError, setImageLoadError] = useState<Record<string, boolean>>({});
  const [imageExists, setImageExists] = useState<Record<string, boolean>>({});
  const [isPopupVisible, setIsPopupVisible] = useState(false);

  // 动态logo状态
  const [logoImageUrl, setLogoImageUrl] = useState<string | null>(null);
  const [logoImageLoaded, setLogoImageLoaded] = useState(false);
  const [logoImageError, setLogoImageError] = useState(false);

  // 展示区域引用
  const showcaseRef = useRef<HTMLDivElement>(null);
  const masonryRef = useRef<any>(null);
  const tipsContainerRef = useRef<HTMLDivElement>(null);

  // 检查logo图片是否存在
  const checkLogoImage = async () => {
    // 根据主题选择不同的logo图片
    const logoFileName = isDarkTheme ? 'logo01b' : 'logo01a';
    const logoUrl = `https://file.aibikini.cn/config/icons/logo/${logoFileName}.png`;
    
    try {
      const response = await fetch(logoUrl, { method: 'HEAD' });
      if (response.ok) {
        setLogoImageUrl(logoUrl);
      } else {
        setLogoImageError(true);
      }
    } catch (error) {
      // 静默处理logo图片检查失败，使用默认文字logo
      setLogoImageError(true);
    }
  };

  // 处理logo图片加载成功
  const handleLogoImageLoad = () => {
    setLogoImageLoaded(true);
  };

  // 处理logo图片加载失败
  const handleLogoImageError = () => {
    setLogoImageError(true);
    setLogoImageUrl(null);
  };

  // 添加useEffect钩子
  // 1. 检查本地存储的登录状态
  useEffect(() => {
    const savedUser = localStorage.getItem('user');
    console.log(savedUser);
    if (savedUser) {
      auth.setUser(JSON.parse(savedUser));
    }
  }, [auth.setUser]);

  // 2. 监听路由变化，更新currentPage
  useEffect(() => {
    const pathToPage: { [key: string]: string } = {
      '/': '首页',
      '/style': '款式设计',
      '/model': '模特图',
      '/tools': '快捷工具'
    };

    const path = '/' + location.pathname.split('/')[1];
    if (pathToPage[path]) {
      setCurrentPage(pathToPage[path]);
    }

    // 保存当前路径
    sessionStorage.setItem('prevPath', location.pathname);
  }, [location.pathname]);

  // 3. 在组件挂载时检查logo图片
  useEffect(() => {
    checkLogoImage();
  }, [isDarkTheme]); // 当主题变化时重新检查logo

  // 4. 当弹窗打开时隐藏侧边栏折叠按钮
  useEffect(() => {
    if (hasModalOpen) {
      setShowCollapseBtn(false);
    }
  }, [hasModalOpen]);

  // 5. 修改加载更多处理函数
  const handleLoadMore = () => {
    setShowcaseCount(prevCount => {
      const newCount = prevCount + 28; // 每次增加28个
      if (newCount >= showcaseItems.length) {
        setShowLoadMoreMessage(true);
        return showcaseItems.length;
      }
      return newCount;
    });
  };

  // 6. 监听任务提示音状态变化
  useEffect(() => {
    const checkMuteState = () => {
      const currentMutedState = isSoundMuted();
      if (currentMutedState !== isMuted) {
        setIsMuted(currentMutedState);
      }
    };

    // 初始检查
    checkMuteState();

    // 设置定时器定期检查状态
    const interval = setInterval(checkMuteState, 1000);

    return () => clearInterval(interval);
  }, [isMuted, isSoundMuted]);

  // 退出登录处理函数
  const handleLogout = () => {
    auth.setUser(null);
    auth.logout();
    navigate('/');
  };

  // 移动端提示关闭处理函数
  const handleCloseMobileTip = () => {
    setShowMobileTip(false);
    localStorage.setItem('mobileTipClosedTime', Date.now().toString());
  };

  // 移动菜单切换处理函数
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // 添加提示卡片导航处理函数
  const handlePrevTips = () => {
    if (tipsContainerRef.current) {
      const container = tipsContainerRef.current;
      const cardWidth = 200; // 卡片宽度 + 间距
      const scrollAmount = cardWidth * 3; // 每次滚动3个卡片
      container.scrollLeft -= scrollAmount;
      
      // 使用requestAnimationFrame确保状态更新及时
      requestAnimationFrame(() => {
        checkScrollState();
      });
    }
  };

  const handleNextTips = () => {
    if (tipsContainerRef.current) {
      const container = tipsContainerRef.current;
      const cardWidth = 200; // 卡片宽度 + 间距
      const scrollAmount = cardWidth * 3; // 每次滚动3个卡片
      container.scrollLeft += scrollAmount;
      
      // 使用requestAnimationFrame确保状态更新及时
      requestAnimationFrame(() => {
        checkScrollState();
      });
    }
  };

  // 检查滚动状态的函数
  const checkScrollState = () => {
    if (tipsContainerRef.current) {
      const container = tipsContainerRef.current;
      const scrollLeft = container.scrollLeft;
      const scrollWidth = container.scrollWidth;
      const clientWidth = container.clientWidth;
      
      // 左侧按钮：当滚动位置为0时隐藏
      setCanScrollLeft(scrollLeft > 0);
      
      // 右侧按钮：当滚动到最右边时隐藏
      // 增加一个更宽松的阈值（5px），确保按钮能及时隐藏
      const threshold = 5;
      const canScrollRight = scrollLeft < scrollWidth - clientWidth - threshold;
      setCanScrollRight(canScrollRight);
      
      // 调试信息
      console.log('Scroll state:', {
        scrollLeft,
        scrollWidth,
        clientWidth,
        threshold,
        canScrollRight,
        remaining: scrollWidth - clientWidth - scrollLeft
      });
    }
  };

  // 监听容器滚动事件
  useEffect(() => {
    const container = tipsContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollState);
      // 初始化滚动状态
      checkScrollState();
      
      return () => {
        container.removeEventListener('scroll', checkScrollState);
      };
    }
  }, []);

  // 添加常用功能勾选处理函数
  const isCommon = (featureName: string): boolean => {
    return commonFeatures.includes(featureName);
  };

  const handleCheckboxChange = (featureName: string) => {
    let newFeatures: string[];
    if (isCommon(featureName)) {
      newFeatures = commonFeatures.filter(f => f !== featureName);
    } else {
      newFeatures = [...commonFeatures, featureName];
    }

    // 更新状态
    setCommonFeatures(newFeatures);

    // 保存到localStorage
    localStorage.setItem('commonFeatures', JSON.stringify(newFeatures));
  };

  // 处理视频播放
  const handleVideoPlay = (item: any) => {
    if (item.isVideo) {
      let videoUrl = item.videoUrl;
      
      // 如果是宣传片，根据图片URL生成视频URL
      if (item.tags && item.tags.includes('publicity')) {
        // 从图片URL中提取序号，然后生成视频URL
        const imageUrl = item.image;
        const match = imageUrl.match(/video-(\d+)\.jpg$/);
        if (match) {
          const sequenceNum = match[1];
          videoUrl = `https://file.aibikini.cn/config/showcase/publicity/video-${sequenceNum}.mp4`;
        }
      }
      
      if (videoUrl) {
        setPlayingVideoId(item.id);
        setPlayingVideoUrl(videoUrl);
        setShowVideoModal(true);
      }
    }
  };

  // 关闭视频模态框
  const handleCloseVideoModal = () => {
    setShowVideoModal(false);
    setPlayingVideoId(null);
    setPlayingVideoUrl(null);
  };

  // 处理图片预览
  const handleImagePreview = (item: any) => {
    if (item.previewImage) {
      setPreviewImageUrl(item.previewImage);
      // 获取带大类的功能名称
      const featureName = item.tags && item.tags.length > 0 ? getFeatureNameWithCategory(item.tags[0]) : '';
      setPreviewFeatureName(featureName);
      setShowImageModal(true);
    }
  };

  // 关闭图片预览模态框
  const handleCloseImageModal = () => {
    setShowImageModal(false);
    setPreviewImageUrl(null);
    setPreviewFeatureName('');
  };

  // 获取功能板块名称的函数
  const getFeatureName = (tag: string): string => {
    const tagToName: {[key: string]: string} = {
      // 款式设计
      'trending': '爆款开发',
      'divergent': '爆款延伸',
      'optimize': '款式优化',
      'inspiration': '灵感探索',
      'fabric': '换面料',
      'drawing': '生成线稿',
      // 模特图
      'fashion': '时尚大片',
      'tryon': '模特换装',
      'change-model': '换模特',
      'change-posture': '换姿势',
      'change-face': '模特换脸',
      'recolor': '服装复色',
      'background': '换背景',
      'virtual': '虚拟模特',
      'detail-migration': '细节还原',
      'hand-fix': '手部修复',
      // AI视频
      'publicity': '宣传片',
      'imgtextvideo': '图文成片',
      'mulimgvideo': '多图成片',
      // 快捷工具
      'matting': '自动抠图',
      'extend': '智能扩图',
      'extract': '图片取词',
      'upscale': '高清放大',
      'inpaint': '消除笔',
      'bleach': '服装去色'
    };
    return tagToName[tag] || '未知功能';
  };

  // 获取带大类的功能名称的函数
  const getFeatureNameWithCategory = (tag: string): string => {
    const tagToCategory: {[key: string]: string} = {
      // 款式设计
      'trending': '款式设计',
      'divergent': '款式设计',
      'optimize': '款式设计',
      'inspiration': '款式设计',
      'fabric': '款式设计',
      'drawing': '款式设计',
      // 模特图
      'fashion': '模特图',
      'tryon': '模特图',
      'change-model': '模特图',
      'change-posture': '模特图',
      'change-face': '模特图',
      'recolor': '模特图',
      'background': '模特图',
      'virtual': '模特图',
      'detail-migration': '模特图',
      'hand-fix': '模特图',
      // 展示
      'publicity': '展示',
      // AI视频
      'imgtextvideo': 'AI视频',
      'mulimgvideo': 'AI视频',
      // 快捷工具
      'matting': '快捷工具',
      'extend': '快捷工具',
      'extract': '快捷工具',
      'upscale': '快捷工具',
      'inpaint': '快捷工具',
      'bleach': '快捷工具'
    };
    
    const category = tagToCategory[tag] || '其他';
    const featureName = getFeatureName(tag);
    return `${category} · ${featureName}`;
  };

  // 处理"尝试做同款"按钮点击
  const handleCreateSimilar = (item: any) => {
    // 根据案例标签跳转到对应的功能页面
    if (item && item.tags && item.tags.length > 0) {
      const tag = item.tags[0]; // 获取第一个标签

      // 宣传片不是网站功能，不跳转
      if (tag === 'publicity') {
        return;
      }

      // 标签与路径映射关系
      const tagToPath: {[key: string]: string} = {
        'fashion': '/model/fashion',      // 时尚大片
        'tryon': '/model/try-on',         // 模特换装
        'recolor': '/model/recolor',      // 服装复色
        'fabric': '/style/fabric',        // 换面料
        'drawing': '/style/drawing',      // 生成线稿
        'background': '/model/background', // 换背景
        'change-posture': '/model/change-posture', // 换姿势
        'change-face': '/model/change-face', // 模特换脸
        'virtual': '/model/virtual',      // 虚拟模特
        'trending': '/style/trending',    // 爆款开发
        'divergent': '/style/divergent',  // 爆款延伸
        'optimize': '/style/optimize',    // 款式优化
        'inspiration': '/style/inspiration', // 灵感探索
        'imgtextvideo': '/video/imgtextvideo', // 图文成片
        'mulimgvideo': '/video/mulimgvideo',   // 多图成片
        'upscale': '/tools/upscale',      // 高清放大
        'matting': '/tools/matting',      // 自动抠图
        'extend': '/tools/extend',        // 智能扩图
        'inpaint': '/tools/inpaint',       // 消除笔
        'bleach': '/tools/bleach'          // 服装去色
      };

      const path = tagToPath[tag];
      if (path) {
        // 在新标签页中打开
        window.open(path, '_blank');
      }
    }
  };

  // 修改优秀案例展示部分
  // 首页渲染优秀案例
  const [showcaseItems, setShowcaseItems] = useState<any[]>([]);
  const [loadingShowcase, setLoadingShowcase] = useState(true);

  useEffect(() => {
    const fetchAllShowcaseItems = async () => {
      setLoadingShowcase(true);
      const allItems: any[] = [];
      const promises = Object.entries(showcaseConfig.FUNCTION_CONFIGS).map(async ([tag, config]: [string, any]) => {
        if (tag === 'img-text-video' || tag === 'multi-img-video') return;
        const { urlPath, name, isVideo } = config;
        for (let i = 1; i <= 5; i++) { // Fetch up to 5 examples from each category for the main page
          const sequentialNum = i.toString().padStart(3, '0');
          
          // 宣传片使用特殊的命名规则
          let imageUrl, previewImageUrl;
          if (tag === 'publicity') {
            const publicityNum = i.toString().padStart(3, '0');
            imageUrl = `${showcaseConfig.SHOWCASE_BASE_URL}${urlPath}/video-${publicityNum}.jpg`;
            previewImageUrl = `${showcaseConfig.SHOWCASE_BASE_URL}${urlPath}/video-${publicityNum}.jpg`;
          } else {
            imageUrl = `${showcaseConfig.SHOWCASE_BASE_URL}${urlPath}/${sequentialNum}.jpg`;
            previewImageUrl = `${showcaseConfig.SHOWCASE_BASE_URL}${urlPath}/${sequentialNum}-a.jpg`;
          }
          
          try {
            const response = await fetch(imageUrl, { method: 'HEAD' });
            if (response.ok) {
              allItems.push({
                id: `${tag}-${i}`,
                image: imageUrl,
                previewImage: previewImageUrl,
                alt: `${name} 案例 ${i}`,
                tags: [tag],
                title: `${name} 案例 ${i}`,
                isVideo: isVideo,
              });
            } else {
              break;
            }
          } catch (error) {
            break;
          }
        }
      });

      await Promise.all(promises);
      
      // Shuffle for variety
      for (let i = allItems.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [allItems[i], allItems[j]] = [allItems[j], allItems[i]];
      }

      setShowcaseItems(allItems);
      setLoadingShowcase(false);
    };

    fetchAllShowcaseItems();
  }, []);

  const renderShowcase = () => {
    if (loadingShowcase) {
      return (
        <div className="showcase-section">
          <h2>优秀案例</h2>
          <div className="showcase-loading-container">
            <div className="showcase-loading-content">
              <div className="showcase-spinner"></div>
              <span>加载中...</span>
            </div>
          </div>
        </div>
      );
    }
    // ... logic to get videoItems and otherItems from `showcaseItems` state
    const videoItems = showcaseItems.filter(item =>
      item.tags.includes('imgtextvideo') || item.tags.includes('mulimgvideo') || item.tags.includes('publicity')
    ).slice(0, 6);

    const otherItems = showcaseItems.filter(item =>
      !item.tags.includes('imgtextvideo') && !item.tags.includes('mulimgvideo') && !item.tags.includes('publicity')
    ).slice(0, showcaseCount - videoItems.length);

    // 合并AI视频案例和其他案例
    const displayItems = [...videoItems, ...otherItems];

    return (
      <div className="showcase-section" key={`showcase-${location.pathname}`}>
        <h2>优秀案例</h2>
        <div className="showcase-waterfall" ref={showcaseRef}>
          {displayItems.map((item) => (
            <div
              key={item.id}
              className={`showcase-item ${item.isVideo ? 'video-item' : ''}`}
            >
              <img
                src={item.image}
                alt={item.alt}
                loading="lazy"
                onLoad={() => masonryRef.current?.layout()}
              />
              {item.isVideo && (
                <div className="video-overlay" onClick={() => handleVideoPlay(item)}>
                  <div className="play-button">▶</div>
                </div>
              )}
              <div className="showcase-buttons">
                {/* 功能板块名称 - 只在首页显示 */}
                <div className="showcase-feature-label">
                  {item.tags && item.tags.length > 0 && tagToIcon[item.tags[0]] && (
                    <span style={{marginRight: 6, display: 'flex', alignItems: 'center'}}>{renderIcon(tagToIcon[item.tags[0]])}</span>
                  )}
                  {item.tags && item.tags.length > 0 && getFeatureName(item.tags[0])}
                </div>
                <button
                  className="showcase-btn preview-btn"
                  onClick={() => item.isVideo ? handleVideoPlay(item) : handleImagePreview(item)}
                >
                  {renderIcon(item.isVideo ? MdOutlinePlayCircle : MdOutlineZoomOutMap)}
                  {item.isVideo ? '播放' : '预览'}
                </button>
                {item.tags && !item.tags.includes('publicity') && (
                  <button className="showcase-btn showcase-create-btn" onClick={() => handleCreateSimilar(item)}>
                    {renderIcon(MdOutlineAutoAwesome)}
                    尝试做同款
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
        {!showLoadMoreMessage && showcaseCount < showcaseItems.length && (
          <div className="load-more-container">
            <button className="load-more-btn" onClick={handleLoadMore}>
              显示更多
            </button>
          </div>
        )}
        {showLoadMoreMessage && (
          <div className="load-more-message">
            更多优秀作品 等你来创造
          </div>
        )}
        
        {/* 移动端底部按钮区域 */}
        <div className="mobile-bottom-buttons">
          <div className="theme-toggle-btn" onClick={toggleTheme} title={isDarkTheme ? '切换为亮色模式' : '切换为暗色模式'}>
            {isDarkTheme ? renderIcon(MdOutlineLightMode) : renderIcon(MdOutlineDarkMode)}
            <span>主题切换</span>
          </div>
          <div className="sound-toggle-btn" onClick={handleToggleMute} title={isMuted ? '打开提示音' : '关闭提示音'}>
            {isMuted ? renderIcon(MdOutlineVolumeOff) : renderIcon(MdOutlineVolumeUp)}
            <span>任务提示音</span>
          </div>
        </div>
      </div>
    );
  };

  // 渲染导航栏
  const renderNavbar = () => {
  return (
      <>
        <nav className="navbar">
          <div className="navbar-left">
            <div className="logo" onClick={() => navigate('/')}>
              {logoImageUrl && !logoImageError ? (
                <img 
                  src={logoImageUrl}
                  alt="AIBIKINI Logo"
                  className="logo-image"
                  onLoad={handleLogoImageLoad}
                  onError={handleLogoImageError}
                  style={{ 
                    opacity: logoImageLoaded ? 1 : 0,
                    transition: 'opacity 0.3s ease'
                  }}
                />
              ) : (
                <span className="logo-text">AIBIKINI</span>
              )}
            </div>
            <button className="mobile-menu-btn" onClick={toggleMobileMenu}>
              <span></span>
              <span></span>
              <span></span>
            </button>
            <div className={`nav-links ${isMobileMenuOpen ? 'mobile-active' : ''}`}>
              {[
                { name: '首页', path: '/' },
                { name: '款式设计', path: '/style', hasDropdown: true },
                { name: '模特图', path: '/model', hasDropdown: true },
                // { name: 'AI视频', path: '/video', hasDropdown: true }, // 临时隐藏"AI视频"按钮，如需恢复显示请取消本行注释
                { name: '快捷工具', path: '/tools', hasDropdown: true },
                { name: '教程', path: '/tutorial', isExternal: true, externalUrl: 'https://www.yuque.com/artahasy/aibikini' }
              ].map(({ name, path, isExternal, externalUrl, hasDropdown }) => {
                const isActive = path === '/'
                  ? location.pathname === '/'
                  : location.pathname.startsWith(path);

                return (
                  <li
                    key={name}
                    className={`nav-item ${isActive ? 'active' : ''} ${hasDropdown ? 'has-dropdown' : ''}`}
                    onClick={() => {
                      if (isExternal && externalUrl) {
                        window.open(externalUrl, '_blank');
                      } else {
                        setCurrentPage(name);
                        navigate(path);
                      }
                    }}
                  >
                    {name}
                    {hasDropdown && (
                      <div className="nav-dropdown">
                        {ALL_FEATURES[name]?.map((feature, index, array) => {
                          const pathMap: { [key: string]: string } = {
                            '时尚大片': '/model/fashion',
                            '模特换装': '/model/try-on',
                            '换模特': '/model/change-model',
                            '服装复色': '/model/recolor',
                            '换面料': '/style/fabric',
                            '换背景': '/model/background',
                            '换姿势': '/model/change-posture',
                            '模特换脸': '/model/change-face',
                            '虚拟模特': '/model/virtual',
                            '细节还原': '/model/detail-migration',
                            '手部修复': '/model/hand-fix',
                            '爆款开发': '/style/trending',
                            '爆款延伸': '/style/divergent',
                            '款式优化': '/style/optimize',
                            '灵感探索': '/style/inspiration',
                            '生成线稿': '/style/drawing',
                            '图文成片': '/video/imgtextvideo',
                            '多图成片': '/video/mulimgvideo',
                            '图片取词': '/tools/extract',
                            '高清放大': '/tools/upscale',
                            '自动抠图': '/tools/matting',
                            '智能扩图': '/tools/extend',
                            '消除笔': '/tools/inpaint',
                            '服装去色': '/tools/bleach'
                          };

                          const featurePath = pathMap[feature];
                          if (!featurePath) return null;

                          // 判断是否为最后一项
                          const isLastItem = index === array.length - 1;
                          const padding = isLastItem ? '12px 15px 18px' : '12px 15px';

                          return (
                            <div
                              key={feature}
                              className="dropdown-item"
                              style={{fontSize: '15px', padding}}
                              onClick={(e) => {
                                e.stopPropagation();
                                setCurrentPage(name);
                                navigate(featurePath);
                              }}
                            >
                              {FEATURE_ICONS[feature] && renderIcon(FEATURE_ICONS[feature])}
                              {feature}
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </li>
                );
              })}
            </div>
          </div>
          <div className="navbar-right">
            <button className="subscribe-btn" onClick={() => window.open('/subscribe', '_blank')}>
              {userSubscription?.plan ? getPlanDisplayName(userSubscription.plan, userSubscription.planName) : '订阅'}
            </button>
            {auth.user ? (
              <div className="user-menu">
                <div className="dropdown-container" 
                  onMouseEnter={() => setShowUserDropdown(true)}
                  onMouseLeave={() => setShowUserDropdown(false)}
                >
                  <button className="logout-btn">
                    用户中心
                </button>
                  {showUserDropdown && (
                    <div className="user-dropdown" style={{width: '140px'}}>
                      <div className="dropdown-item" style={{padding: '8px 15px', fontSize: '12px', cursor: 'default', borderBottom: '1px solid var(--border-color)', backgroundColor: 'var(--bg-primary)', pointerEvents: 'none'}}>
                        <div>{auth.user?.username || ''}</div>
                        <div>{auth.user?.phone ? auth.user.phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2') : ''}</div>
                      </div>
                      <div className="dropdown-item" style={{padding: '12px 15px', fontSize: '15px'}} onClick={() => {
                        window.open('/information', '_blank');
                        setShowUserDropdown(false);
                      }}>
                        账号信息
                      </div>  
                      {auth.user?.role === 'admin' && (
                        <div className="dropdown-item" style={{padding: '12px 15px', fontSize: '15px'}} onClick={() => {
                          window.open('/admin', '_blank');
                          setShowUserDropdown(false);
                        }}>
                        管理后台
                      </div>
                      )}
                      <div className="dropdown-item" style={{padding: '12px 15px', fontSize: '15px'}} onClick={() => {
                        window.open('/account', '_blank');
                        setShowUserDropdown(false);
                      }}>
                        算力明细
                      </div>
                      <div className="dropdown-item" style={{padding: '12px 15px', fontSize: '15px'}} onClick={() => {
                        setShowContactModal(true);
                        setShowUserDropdown(false);
                      }}>
                        联系我们
                      </div>
                      <div className="dropdown-item" style={{padding: '12px 15px 18px', fontSize: '15px'}} onClick={() => {
                        handleLogout();
                        setShowUserDropdown(false);
                      }}>
                        退出登录
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <button className="login-btn" onClick={() => setShowLoginModal(true)}>
                登录 / 注册
              </button>
            )}
          </div>
        </nav>
        <nav className="sub-navbar">
          <ul className="sub-nav-links">
            {[
              { name: '首页', path: '/' },
              { name: '款式设计', path: '/style', hasDropdown: true },
              { name: '模特图', path: '/model', hasDropdown: true },
              // { name: 'AI视频', path: '/video', hasDropdown: true }, // 临时隐藏"AI视频"按钮，如需恢复显示请取消本行注释
              { name: '快捷工具', path: '/tools', hasDropdown: true },
              { name: '教程', path: '/tutorial', isExternal: true, externalUrl: 'https://www.yuque.com/artahasy/aibikini' }
            ].map(({ name, path, isExternal, externalUrl, hasDropdown }) => (
              <li
                key={name}
                className={`sub-nav-item ${currentPage === name ? 'active' : ''} ${hasDropdown ? 'has-dropdown' : ''}`}
                onClick={() => {
                  if (isExternal && externalUrl) {
                    window.open(externalUrl, '_blank');
                  } else {
                    setCurrentPage(name);
                    navigate(path);
                  }
                }}
              >
                {name}
                {hasDropdown && (
                  <div className="sub-nav-dropdown">
                    {ALL_FEATURES[name]?.map((feature, index, array) => {
                      const pathMap: { [key: string]: string } = {
                        '时尚大片': '/model/fashion',
                        '模特换装': '/model/try-on',
                        '换模特': '/model/change-model',
                        '服装复色': '/model/recolor',
                        '换面料': '/style/fabric',
                        '换背景': '/model/background',
                        '换姿势': '/model/change-posture',
                        '模特换脸': '/model/change-face',
                        '虚拟模特': '/model/virtual',
                        '细节还原': '/model/detail-migration',
                        '手部修复': '/model/hand-fix',
                        '爆款开发': '/style/trending',
                        '爆款延伸': '/style/divergent',
                        '款式优化': '/style/optimize',
                        '灵感探索': '/style/inspiration',
                        '生成线稿': '/style/drawing',
                        '图文成片': '/video/imgtextvideo',
                        '多图成片': '/video/mulimgvideo',
                        '图片取词': '/tools/extract',
                        '高清放大': '/tools/upscale',
                        '自动抠图': '/tools/matting',
                        '智能扩图': '/tools/extend',
                        '消除笔': '/tools/inpaint',
                        '服装去色': '/tools/bleach'
                      };

                      const featurePath = pathMap[feature];
                      if (!featurePath) return null;

                      // 判断是否为最后一项
                      const isLastItem = index === array.length - 1;
                      const padding = isLastItem ? '12px 15px 18px' : '12px 15px';

                      return (
                        <div
                          key={feature}
                          className="sub-dropdown-item"
                          style={{fontSize: '15px', padding}}
                          onClick={(e) => {
                            e.stopPropagation();
                            setCurrentPage(name);
                            navigate(featurePath);
                          }}
                        >
                          {FEATURE_ICONS[feature] && renderIcon(FEATURE_ICONS[feature])}
                          {feature}
                        </div>
                      );
                    })}
                  </div>
                )}
              </li>
            ))}
          </ul>
        </nav>
      </>
    );
  };

  // 渲染侧边栏
  const renderSidebar = () => {
    if (currentPage === '首页') {
      return (
        <div className={`sidebar ${isSidebarCollapsed ? 'collapsed' : ''}`}>
          <h3>
            <span>常用功能</span>
            <button
              className="sidebar-edit-btn"
              onClick={(e) => {
                e.stopPropagation();
                setShowEditModal(!showEditModal);
              }}
            >
              {renderIcon(FiPlus)}
            </button>
          </h3>
          <div className="sidebar-section">
            <button
              className="sidebar-mobile-btn"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setShowEditModal(prev => !prev);
              }}
            >
              {renderIcon(FiPlus)}
            </button>
            {commonFeatures.map((item, idx) => {
              // 临时隐藏指定页面按钮（如需恢复显示"消除笔"、"图文成片"、"多图成片"，请删除下方 if 判断）
              if ([
                '图文成片', // imgtextvideo
                '多图成片'  // mulimgvideo
              ].includes(item)) {
                // === 恢复显示方法：删除本 if 判断及其内容 ===
                return null;
              }
              const pathMap = {
                '时尚大片': '/model/fashion',
                '模特换装': '/model/try-on',
                '换模特': '/model/change-model',
                '服装复色': '/model/recolor',
                '换面料': '/style/fabric',
                '换背景': '/model/background',
                '换姿势': '/model/change-posture',
                '模特换脸': '/model/change-face',
                '虚拟模特': '/model/virtual',
                '细节还原': '/model/detail-migration',
                '手部修复': '/model/hand-fix',
                '爆款开发': '/style/trending',
                '爆款延伸': '/style/divergent',
                '款式优化': '/style/optimize',
                '灵感探索': '/style/inspiration',
                '生成线稿': '/style/drawing',
                '图文成片': '/video/imgtextvideo',
                '多图成片': '/video/mulimgvideo',
                '图片取词': '/tools/extract',
                '高清放大': '/tools/upscale',
                '自动抠图': '/tools/matting',
                '智能扩图': '/tools/extend',
                '消除笔': '/tools/inpaint',
                '服装去色': '/tools/bleach'
              };

              const isActive = location.pathname === pathMap[item as keyof typeof pathMap];

              return (
                <div
                  id={`sidebar-item-${idx}`}
                  key={idx}
                  className={`sidebar-item ${isActive ? 'active' : ''}`}
                  onClick={() => {
                    switch(item) {
                      case '时尚大片':
                        setCurrentPage('模特图');
                        navigate('/model/fashion');
                        break;
                      case '模特换装':
                        setCurrentPage('模特图');
                        navigate('/model/try-on');
                        break;
                      case '服装复色':
                        setCurrentPage('模特图');
                        navigate('/model/recolor');
                        break;
                      case '换面料':
                        setCurrentPage('款式设计');
                        navigate('/style/fabric');
                        break;
                      case '换背景':
                        setCurrentPage('模特图');
                        navigate('/model/background');
                        break;
                      case '换姿势':
                        setCurrentPage('模特图');
                        navigate('/model/change-posture');
                        break;
                      case '模特换脸':
                        setCurrentPage('模特图');
                        navigate('/model/change-face');
                        break;
                      case '虚拟模特':
                        setCurrentPage('模特图');
                        navigate('/model/virtual');
                        break;
                      case '细节还原':
                        setCurrentPage('模特图');
                        navigate('/model/detail-migration');
                        break;
                      case '手部修复':
                        setCurrentPage('模特图');
                        navigate('/model/hand-fix');
                        break;
                      case '换模特':
                        setCurrentPage('模特图');
                        navigate('/model/change-model');
                        break;
                      case '爆款开发':
                        setCurrentPage('款式设计');
                        navigate('/style/trending');
                        break;
                      case '爆款延伸':
                        setCurrentPage('款式设计');
                        navigate('/style/divergent');
                        break;
                      case '款式优化':
                        setCurrentPage('款式设计');
                        navigate('/style/optimize');
                        break;
                      case '灵感探索':
                        setCurrentPage('款式设计');
                        navigate('/style/inspiration');
                        break;
                      case '生成线稿':
                        setCurrentPage('款式设计');
                        navigate('/style/drawing');
                        break;
                      case '图文成片':
                        setCurrentPage('AI视频');
                        navigate('/video/imgtextvideo');
                        break;
                      case '多图成片':
                        setCurrentPage('AI视频');
                        navigate('/video/mulimgvideo');
                        break;
                      case '图片取词':
                        setCurrentPage('快捷工具');
                        navigate('/tools/extract');
                        break;
                      case '高清放大':
                        setCurrentPage('快捷工具');
                        navigate('/tools/upscale');
                        break;
                      case '自动抠图':
                        setCurrentPage('快捷工具');
                        navigate('/tools/matting');
                        break;
                      case '智能扩图':
                        setCurrentPage('快捷工具');
                        navigate('/tools/extend');
                        break;
                      case '消除笔':
                        setCurrentPage('快捷工具');
                        navigate('/tools/inpaint');
                        break;
                      case '服装去色':
                        setCurrentPage('快捷工具');
                        navigate('/tools/bleach');
                        break;
                      default:
                        break;
                    }
                    setShowEditModal(false);
                  }}
                  onMouseEnter={e => {
                    if (hoverTimerRef.current) clearTimeout(hoverTimerRef.current);
                    const target = e.currentTarget as HTMLElement;
                    const pageType = extractPageType(pathMap[item as keyof typeof pathMap] || '');
                    
                    // 如果已知图片不存在，直接不显示弹窗
                    if (imageLoadError[pageType] || imageExists[pageType] === false) {
                      return;
                    }
                    
                    hoverTimerRef.current = setTimeout(async () => {
                      const rect = target.getBoundingClientRect();
                      const imgUrl = getSidebarGuideImgUrl(pageType);
                      
                      // 如果还没有检查过这个图片，先检查是否存在
                      if (imageExists[pageType] === undefined) {
                        const exists = await checkImageExists(imgUrl);
                        setImageExists(prev => ({ ...prev, [pageType]: exists }));
                        
                        // 如果图片不存在，不显示弹窗
                        if (!exists) {
                          return;
                        }
                      }
                      
                      setSidebarGuidePopup({
                        x: rect.right,
                        y: rect.top + rect.height / 2,
                        pageType
                      });
                    }, 600);
                  }}
                  onMouseLeave={() => {
                    if (hoverTimerRef.current) clearTimeout(hoverTimerRef.current);
                    // 先播放淡出动画
                    setIsPopupVisible(false);
                    // 等待动画完成后再隐藏弹窗
                    setTimeout(() => {
                      setSidebarGuidePopup(null);
                    }, 300); // 与动画时长一致
                  }}
                  style={{ position: 'relative' }}
                >
                  {/* 根据功能名称选择对应图标 */}
                  {item === '时尚大片' && renderIcon(MdOutlinePhotoCamera)}
                  {item === '模特换装' && renderIcon(MdOutlineCheckroom)}
                  {item === '换模特' && renderIcon(MdOutlineSwitchAccount)}
                  {item === '服装复色' && renderIcon(MdOutlinePalette)}
                  {item === '换面料' && renderIcon(MdOutlineTexture)}
                  {item === '换背景' && renderIcon(FiImage)}
                  {item === '换姿势' && renderIcon(MdOutlineAutoFixHigh)}
                  {item === '模特换脸' && renderIcon(MdOutlineFace)}
                  {item === '虚拟模特' && renderIcon(MdOutlineFavoriteBorder)}
                  {item === '细节还原' && renderIcon(MdOutlineCenterFocusStrong)}
                  {item === '手部修复' && renderIcon(MdOutlineHandshake)}
                  {item === '爆款开发' && renderIcon(MdOutlineAutoAwesome)}
                  {item === '爆款延伸' && renderIcon(MdOutlineTransform)}
                  {item === '灵感探索' && renderIcon(MdOutlineStyle)}
                  {item === '款式优化' && renderIcon(FiScissors)}
                  {item === '生成线稿' && renderIcon(MdOutlineDraw)}
                  {item === '图文成片' && renderIcon(MdOutlineOndemandVideo)}
                  {item === '多图成片' && renderIcon(MdOutlineVideoLibrary)}
                  {item === '图片取词' && renderIcon(MdOutlineDocumentScanner)}
                  {item === '高清放大' && renderIcon(MdOutlineImageSearch)}
                  {item === '自动抠图' && renderIcon(MdOutlineFormatShapes)}
                  {item === '智能扩图' && renderIcon(MdOutlineZoomOutMap)}
                  {item === '消除笔' && renderIcon(MdOutlineHealing)}
                  {item === '服装去色' && renderIcon(MdOutlineWaterDrop)}
                  <span>{item}</span>
                </div>
              );
            })}
            {/* 移动端专用展开/折叠按钮 */}
            <button
              className="sidebar-mobile-toggle-btn"
              onClick={() => setIsSidebarCollapsed(v => !v)}
              aria-label={isSidebarCollapsed ? '展开侧边栏' : '折叠侧边栏'}
              style={{ marginLeft: 'auto', marginRight: 14 }}
            >
              {isSidebarCollapsed ? renderIcon(FiMaximize2) : renderIcon(FiMinimize2)}
            </button>
          </div>
          <div className="sidebar-bottom-group">
            <ThemeToggle sidebarStyle={true} isDarkTheme={isDarkTheme} toggleTheme={toggleTheme} />
            <SoundControl sidebarStyle={true} />
          </div>
        </div>
      );
    } else if (currentPage === '款式设计' || currentPage === '模特图' || currentPage === 'AI视频' || currentPage === '快捷工具') {
      const getPath = () => {
        switch(currentPage) {
          case '款式设计': return '/style';
          case '模特图': return '/model';
          case 'AI视频': return '/video';
          case '快捷工具': return '/tools';
          default: return '/';
        }
      };

      return (
        <div className={`sidebar ${isSidebarCollapsed ? 'collapsed' : ''}`}>
          <h3>
            <span>{currentPage}</span>
            <button className="sidebar-back-btn" onClick={() => navigate(getPath())}>
              {renderIcon(MdOutlineSpaceDashboard)}
            </button>
          </h3>
          <div className="sidebar-section">
            <button className="sidebar-mobile-btn" onClick={() => navigate(getPath())}>
              {renderIcon(MdOutlineSpaceDashboard)}
            </button>

            {ALL_FEATURES[currentPage]?.map((feature, idx) => {
              // 临时隐藏指定页面按钮（如需恢复显示"图文成片"、"多图成片"，请删除下方 if 判断）
              if ([
                '图文成片', // imgtextvideo
                '多图成片'  // mulimgvideo
              ].includes(feature)) {
                // === 恢复显示方法：删除本 if 判断及其内容 ===
                return null;
              }
              const pathMap: { [key: string]: string } = {
                '时尚大片': '/model/fashion',
                '模特换装': '/model/try-on',
                '换模特': '/model/change-model',
                '服装复色': '/model/recolor',
                '换面料': '/style/fabric',
                '换背景': '/model/background',
                '换姿势': '/model/change-posture',
                '模特换脸': '/model/change-face',
                '虚拟模特': '/model/virtual',
                '细节还原': '/model/detail-migration',
                '手部修复': '/model/hand-fix',
                '爆款开发': '/style/trending',
                '爆款延伸': '/style/divergent',
                '款式优化': '/style/optimize',
                '灵感探索': '/style/inspiration',
                '生成线稿': '/style/drawing',
                '图文成片': '/video/imgtextvideo',
                '多图成片': '/video/mulimgvideo',
                '图片取词': '/tools/extract',
                '高清放大': '/tools/upscale',
                '自动抠图': '/tools/matting',
                '智能扩图': '/tools/extend',
                '消除笔': '/tools/inpaint',
                '服装去色': '/tools/bleach'
              };

              const isActive = location.pathname === pathMap[feature];

              return (
                <div
                  id={`sidebar-item-feature-${idx}`}
                  key={idx}
                  className={`sidebar-item ${isActive ? 'active' : ''}`}
                  onClick={() => {
                    if (pathMap[feature]) {
                      navigate(pathMap[feature]);
                    }
                  }}
                  onMouseEnter={e => {
                    if (hoverTimerRef.current) clearTimeout(hoverTimerRef.current);
                    const target = e.currentTarget as HTMLElement;
                    const pageType = extractPageType(pathMap[feature] || '');
                    
                    // 如果已知图片不存在，直接不显示弹窗
                    if (imageLoadError[pageType] || imageExists[pageType] === false) {
                      return;
                    }
                    
                    hoverTimerRef.current = setTimeout(async () => {
                      const rect = target.getBoundingClientRect();
                      const imgUrl = getSidebarGuideImgUrl(pageType);
                      
                      // 如果还没有检查过这个图片，先检查是否存在
                      if (imageExists[pageType] === undefined) {
                        const exists = await checkImageExists(imgUrl);
                        setImageExists(prev => ({ ...prev, [pageType]: exists }));
                        
                        // 如果图片不存在，不显示弹窗
                        if (!exists) {
                          return;
                        }
                      }
                      
                      setSidebarGuidePopup({
                        x: rect.right,
                        y: rect.top + rect.height / 2,
                        pageType
                      });
                    }, 600);
                  }}
                  onMouseLeave={() => {
                    if (hoverTimerRef.current) clearTimeout(hoverTimerRef.current);
                    // 先播放淡出动画
                    setIsPopupVisible(false);
                    // 等待动画完成后再隐藏弹窗
                    setTimeout(() => {
                      setSidebarGuidePopup(null);
                    }, 300); // 与动画时长一致
                  }}
                  style={{ position: 'relative' }}
                >
                  {/* 使用FEATURE_ICONS映射获取图标 */}
                  {FEATURE_ICONS[feature] && renderIcon(FEATURE_ICONS[feature])}
                  <span>{feature}</span>
                </div>
              );
            })}
            {/* 移动端专用展开/折叠按钮 */}
            <button
              className="sidebar-mobile-toggle-btn"
              onClick={() => setIsSidebarCollapsed(v => !v)}
              aria-label={isSidebarCollapsed ? '展开侧边栏' : '折叠侧边栏'}
              style={{ marginLeft: 8, marginRight: 14 }}
            >
              {isSidebarCollapsed ? renderIcon(FiMaximize2) : renderIcon(FiMinimize2)}
            </button>
          </div>
          <div className="sidebar-bottom-group">
            <ThemeToggle sidebarStyle={true} isDarkTheme={isDarkTheme} toggleTheme={toggleTheme} />
            <SoundControl sidebarStyle={true} />
          </div>
        </div>
      );
    }
    return null;
  };

  // 修改主内容区渲染函数
  const renderMainContent = () => {
    // 如果是首页
    if (!isAdmin) {
      return (
        <div className="main-content home-page">
          {/* 使用新的 PromotionBanner 组件 - 只在有公告时显示 */}
          {announcement && (
            <PromotionBanner
              isLoggedIn={!!auth.user}
              isSubscribed={userSubscription?.hasActiveSubscription || false}
              title={announcement.title}
              subtitle={announcement.content}
              buttonText={!!auth.user ? "开始创作" : "立即开始"}
              onLoginClick={() => setShowLoginModal(true)}
              onSubscribeClick={() => setShowContactModal(true)}
              backgroundImage={announcement?.images?.[0]?.url}
              showMask={typeof announcement?.showMask === 'boolean' ? announcement.showMask : true}
              showTitle={typeof announcement?.showTitle === 'boolean' ? announcement.showTitle : true}
              showSubtitle={typeof announcement?.showSubtitle === 'boolean' ? announcement.showSubtitle : true}
            />
          )}

          {/* 功能模块介绍 */}
          <div className="features-section">
            <h2>核心功能</h2>
            <div className="feature-cards">
              {getAllFeatures()
                // 临时隐藏指定页面卡片（如需恢复显示"图文成片"、"多图成片"，请删除下方 filter 判断）
                .filter(feature => ![
                  '图文成片', // imgtextvideo
                  '多图成片'  // mulimgvideo
                ].includes(feature.name))
                .map((feature) => (
                <div
                  key={feature.id}
                  className="feature-card"
                  style={{ position: 'relative' }}
                >
                  <img src={feature.image} alt={feature.name} />
                  <div className="feature-content">
                    <h3>{feature.name}</h3>
                    <p>{feature.description}</p>
                  </div>
                  {/* 悬浮时显示的"立即使用"按钮 */}
                  <div className="feature-use-button">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        const pathMap: { [key: string]: string } = {
                          '时尚大片': '/model/fashion',
                          '模特换装': '/model/try-on',
                          '换模特': '/model/change-model',
                          '服装复色': '/model/recolor',
                          '换面料': '/style/fabric',
                          '换背景': '/model/background',
                          '虚拟模特': '/model/virtual',
                          '细节还原': '/model/detail-migration',
                          '手部修复': '/model/hand-fix',
                          '换姿势': '/model/change-posture',
                          '模特换脸': '/model/change-face',
                          '爆款开发（融合+线稿）': '/style/trending',
                          '款式优化': '/style/optimize',
                          '灵感探索（创款+创意）': '/style/inspiration',
                          '生成线稿': '/style/drawing',
                          '图文成片': '/video/imgtextvideo',
                          '多图成片': '/video/mulimgvideo',
                          '图片取词': '/tools/extract',
                          '高清放大': '/tools/upscale',
                          '自动抠图（去背景+抠衣服）': '/tools/matting',
                          '智能扩图': '/tools/extend',
                          '消除笔': '/tools/inpaint',
                          '服装去色': '/tools/bleach'
                        };

                        if (pathMap[feature.name]) {
                          window.open(pathMap[feature.name], '_blank');
                        }
                      }}
                    >
                      立即使用
                    </button>
                  </div>
                </div>
              ))}
              <div className="feature-card coming-soon-card">
                <div className="feature-image">
                  <img src="https://file.aibikini.cn/config/features/coming-soon.jpg" alt="更多功能" className="coming-soon-img" />
                  <div className="coming-soon-content">
                    <h3>更多精彩 即将上线</h3>
                  </div>
                </div>
                <div className="feature-content">
                  <h3>更多功能</h3>
                  <p>持续创新，为您带来更多AI智能设计体验</p>
                </div>
              </div>
            </div>
          </div>

          {/* 登录状态显示使用技巧 */}
          {auth.user && (
            <div className="tips-section">
              <h2>使用技巧</h2>
              <button 
                className="tips-nav-button prev" 
                onClick={handlePrevTips}
                disabled={!canScrollLeft}
                aria-label="上一页"
              >
                <span className="sr-only">上一页</span>
              </button>
              <div className="tips-container" ref={tipsContainerRef}>
                <div className="tips-cards">
                  {TIPS_CONFIG.map((tip, idx) => (
                    <div key={tip.id} className={`tip-card ${tip.isMore ? 'more-tips' : ''}`} onClick={() => {
                      if (tip.link) {
                        window.open(tip.link, '_blank');
                      }
                    }} style={{
                      border: tip.isMore ? '1px solid transparent' : `1px solid ${getBorderColor(idx, TIPS_CONFIG.length)}`,
                      background: tip.isMore ? 'linear-gradient(var(--bg-primary), var(--bg-primary)) padding-box, var(--brand-gradient) border-box' : 'transparent',
                      backgroundClip: tip.isMore ? 'padding-box, border-box' : 'initial'
                    }}>
                      <div className="tip-image" style={{ background: tip.isMore ? 'var(--brand-gradient)' : getGradientColor(idx, TIPS_CONFIG.length), display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                        {TIPS_ICONS[idx] && React.createElement(TIPS_ICONS[idx] as React.ComponentType<any>, { style: { color: 'var(--text-inverse)', width: 20, height: 20 } })}
                      </div>
                      <div className="tip-content">
                        {!tip.isMore && (
                          <>
                            <h3 className="tip-title" dangerouslySetInnerHTML={{ __html: tip.title }}></h3>
                            <p className="tip-description-desktop">{tip.description}</p>
                            <p className="tip-description-mobile">{tip.mobileDescription}</p>
                          </>
                        )}
                        {tip.isMore && (
                          <>
                            <h3 className="tip-title">更多教程</h3>
                            <p className="tip-description-desktop">{tip.description}</p>
                            <p className="tip-description-mobile">{tip.mobileDescription}</p>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                  {/* 添加空白空间，确保最后一张卡片的边框完整显示 */}
                  <div style={{ width: '20px', flexShrink: 0 }}></div>
                </div>
              </div>
              <button 
                className="tips-nav-button next" 
                onClick={handleNextTips}
                disabled={!canScrollRight}
                aria-label="下一页"
              >
                <span className="sr-only">下一页</span>
              </button>
            </div>
          )}

          {/* 优秀案例展示 */}
          {renderShowcase()}
        </div>
      );
    }

    // 其他页面
    return (
      <div className="main-content">
        <AdminPage />
      </div>
    );
  };

  // 渲染图标函数
  const renderIcon = (IconComponent: IconType) => {
    // 为视频播放按钮使用更大的图标
    if (IconComponent === MdOutlinePlayCircle) {
      return React.createElement(IconComponent as any, { size: 22 });
    }
    return React.createElement(IconComponent as React.ComponentType);
  };

  // 在App组件的顶部添加useEffect
  useEffect(() => {
    // 监听路由变化，关闭编辑弹窗
    setShowEditModal(false);
  }, [location.pathname]);

  // 添加鼠标移动处理函数，显示/隐藏折叠按钮
  const handleMouseMove = (e: React.MouseEvent) => {
    // 获取侧边栏宽度
    const sidebarWidth = isSidebarCollapsed ? 58 : 158;
    // 获取鼠标横坐标
    const mouseX = e.clientX || 0;
    // 如果鼠标在侧边栏区域内，显示折叠按钮
    setShowCollapseBtn(mouseX <= sidebarWidth);
  };

  // 处理通知显示
  const handleNotification = (notif: { type: string; message: string | React.ReactNode; duration?: number }) => {
    setNotification(notif);
    // 支持自定义停留时间，默认3秒
    const duration = notif.duration ? notif.duration * 1000 : 3000;
    setTimeout(() => {
      setNotification({ type: '', message: '' });
    }, duration);
  };

  // 修改主题切换函数
  const toggleTheme = () => {
    const newTheme = !isDarkTheme;
    setIsDarkTheme(newTheme);
    document.documentElement.setAttribute('data-theme', newTheme ? 'dark' : 'light');
    localStorage.setItem('theme', newTheme ? 'dark' : 'light');
  };

  // 切换侧边栏折叠/展开状态
  const toggleSidebar = () => {
    const newState = !isSidebarCollapsed;
    setIsSidebarCollapsed(newState);
    localStorage.setItem('sidebarCollapsed', newState.toString());
  };

  // 添加主题初始化效果
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', isDarkTheme ? 'dark' : 'light');
  }, []);

  // 简单函数，为组件添加认证属性
  const withAuth = (Component: any) => {
    return <Component isLoggedIn={!!auth.user} userId={auth.user?.id || 'developer'} />;
  };

  // 在全局window对象上添加showLoginModal方法
  useEffect(() => {
    // 将打开登录模态框的方法添加到全局window对象
    (window as any).showLoginModal = () => setShowLoginModal(true);

    // 清理函数
    return () => {
      delete (window as any).showLoginModal;
    };
  }, []);

  // 添加事件监听器
  useEffect(() => {
    const handleShowResetPassword = () => {
      setShowResetPassword(true);
    };

    window.addEventListener('showResetPassword', handleShowResetPassword);

    return () => {
      window.removeEventListener('showResetPassword', handleShowResetPassword);
    };
  }, []);

  const [isLoading, setIsLoading] = useState(true);
  const [showTips, setShowTips] = useState(true);
  const [announcement, setAnnouncement] = useState<any | null>(null);
  const [creditBalance, setCreditBalance] = useState<number | null>(null);

  // 获取活跃公告
  useEffect(() => {
    const fetchActiveAnnouncement = async () => {
      try {
        const response = await api.get('/announcements/active');
        if (response.data?.length > 0) {
          setAnnouncement(response.data[0]); // 获取第一条公告
        }
      } catch (error) {
        console.error('获取公告失败:', error);
      }
    };

    fetchActiveAnnouncement();
  }, []);

  // 获取用户算力余额
  const fetchCreditBalance = async () => {
    try {
      const response = await api.get('/user/credits');
      console.log('获取到的算力余额:', response.data);
    } catch (error) {
      console.error('获取算力余额失败:', error);
    }
  };

  // 获取用户订阅信息
  const fetchUserSubscription = async () => {
    try {
      const response = await getUserSubscription();
      console.log('获取到的用户订阅信息:', response);
      setUserSubscription(response);
    } catch (error) {
      console.error('获取用户订阅信息失败:', error);
      // 不显示错误消息，因为免费用户可能没有订阅
    }
  };

  // 获取订阅计划显示名称
  const getPlanDisplayName = (planCode: string, planName?: string) => {
    // 优先使用后端返回的计划名称
    if (planName) {
      return planName;
    }
    
    // 后备方案：使用硬编码映射
    const planNameMap: { [key: string]: string } = {
      'free': '免费版',
      'design': '设计版',
      'model': '模特版', 
      'full': '完整版',
      'enterprise': '企业版'
    };
    
    return planNameMap[planCode] || '免费版';
  };

  // 监听用户登录状态变化
  useEffect(() => {
    if (auth.user) {
      fetchCreditBalance();
      fetchUserSubscription();
    } else {
      setUserSubscription(null);
    }
  }, [auth.user]);

  // 定义卡片功能图标顺序
  const TIPS_ICONS = [
    MdOutlineSchool, // 新手必看 - 更换为学校图标
    MdOutlineAutoAwesome, // 爆款开发
    MdOutlineTransform, // 爆款延伸
    MdOutlineStyle,      // 灵感探索
    MdOutlineDraw,       // 款式优化
    MdOutlinePhotoCamera, // 时尚大片
    MdOutlineCheckroom,   // 模特换装
    MdOutlineSwitchAccount, // 换模特
    MdOutlinePalette,    // 服装复色
    FiImage,             // 换背景
    MdOutlineFavoriteBorder, // 虚拟模特
    MdOutlineCenterFocusStrong, // 细节还原
    MdOutlineHandshake,   // 手部修复
    MdOutlineMoreHoriz   // 更多教程
  ];

  const [canScrollLeft, setCanScrollLeft] = useState<boolean>(false);
  const [canScrollRight, setCanScrollRight] = useState<boolean>(true);

  const getSidebarGuideImgUrl = (pageType: string) => {
    const baseUrl = 'https://file.aibikini.cn/config/sidebar';
    return `${baseUrl}/${pageType}/01.jpg`;
  };
  const extractPageType = (path: string) => {
    const parts = path.split('/').filter(Boolean);
    if (parts.length === 0) return '';
    return parts[parts.length - 1];
  };

  // 预检查图片是否存在
  const checkImageExists = async (url: string): Promise<boolean> => {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  };

  // 在弹窗显示时设置可见状态
  useEffect(() => {
    if (sidebarGuidePopup) {
      // 延迟一帧设置可见状态，确保动画生效
      requestAnimationFrame(() => {
        setIsPopupVisible(true);
      });
    }
  }, [sidebarGuidePopup]);

  // 初始化Masonry布局
  useEffect(() => {
    if (showcaseRef.current && showcaseItems.length > 0) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [showcaseItems, showcaseCount, location.pathname]); // 恢复 location.pathname 依赖

  // 监听图片加载
  useEffect(() => {
    if (showcaseRef.current && showcaseItems.length > 0) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [showcaseItems, showcaseCount, location.pathname]); // 恢复 location.pathname 依赖

  // 移除复杂的路由切换处理逻辑，因为key已经处理了

  return ( !isAdmin ? (
    <>
      {/* 根据当前路径决定渲染内容 */}
      {location.pathname === '/privacy' ? (
        <PrivacyPolicy />
      ) : location.pathname === '/terms' ? (
        <TermsOfService />
      ) : (
        <div className="app"
          onClick={(e) => {
            // 检查点击事件的目标是否是编辑按钮或其子元素
            const isEditButton = (e.target as HTMLElement).closest('.sidebar-edit-btn, .sidebar-mobile-btn');
            if (!isEditButton) {
              setShowEditModal(false);
            }
          }}
          onMouseMove={handleMouseMove}
        >
          {renderNavbar()}
          <div  className={`content-wrap ${isSidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
            {/* 侧边栏折叠按钮 */}
            <button
              className={`sidebar-collapse-btn ${showCollapseBtn && !hasModalOpen ? 'visible' : ''}`}
              onClick={(e) => {
                e.stopPropagation();
                toggleSidebar();
              }}
              onMouseEnter={() => !hasModalOpen && setShowCollapseBtn(true)}
              onMouseLeave={(e) => {
                // 检查鼠标是否仍在侧边栏区域
                const sidebarWidth = isSidebarCollapsed ? 58 : 158;
                const mouseX = e.clientX || 0;
                if (mouseX > sidebarWidth) {
                  setShowCollapseBtn(false);
                }
              }}
            >
              {renderIcon(isSidebarCollapsed ? MdOutlineChevronRight : MdOutlineChevronLeft)}
            </button>
            {renderSidebar()}
            <Routes>
              <Route path="/" element={renderMainContent()} />
              <Route path="/subscribe" element={<Subscribe />} />
              <Route path="/information" element={withAuth(() => <InformationPage />)} />
              <Route path="/style/*" element={withAuth(StylePage)} />
              <Route path="/style/optimize" element={withAuth(OptimizePage)} />
              <Route path="/style/inspiration" element={withAuth(InspirationPage)} />
              <Route path="/style/trending" element={withAuth(TrendingPage)} />
              <Route path="/style/fabric" element={withAuth(FabricPage)} />
              <Route path="/model" element={withAuth(ModelPage)} />
              <Route path="/model/fashion" element={withAuth(FashionPage)} />
              <Route path="/model/try-on" element={withAuth(TryOnPage)} />
              <Route path="/model/recolor" element={withAuth(RecolorPage)} />
              <Route path="/model/background" element={withAuth(BackgroundPage)} />
              <Route path="/model/virtual" element={withAuth(VirtualPage)} />
              <Route path="/model/detail-migration" element={withAuth(DetailMigrationPage)} />
              <Route path="/model/hand-fix" element={withAuth(HandFixPage)} />
              <Route path="/model/change-model" element={withAuth(ChangeModelPage)} />
              <Route path="/model/change-posture" element={withAuth(ChangePosturePage)} />
<Route path="/model/change-face" element={withAuth(ChangeFacePage)} />
              <Route path="/style/drawing" element={withAuth(DrawingPage)} />
              <Route path="/video/*" element={withAuth(VideoPage)} />
              <Route path="/video/imgtextvideo" element={withAuth(ImgtextvideoPage)} />
              <Route path="/video/mulimgvideo" element={withAuth(MulimgvideoPage)} />
              <Route path="/tools/*" element={withAuth(ToolsPage)} />
              <Route path="/tools/extract" element={withAuth(ExtractPage)} />
              <Route path="/tools/upscale" element={withAuth(UpscalePage)} />
              <Route path="/tools/matting" element={withAuth(MattingPage)} />
              <Route path="/tools/extend" element={withAuth(ExtendPage)} />
              <Route path="/tools/inpaint" element={withAuth(InpaintPage)} />
<Route path="/tools/bleach" element={withAuth(BleachPage)} />
              <Route path="/account" element={withAuth(AccountPage)} />
              <Route 
                path="/admin/*" 
                element={
                  <ProtectedRoute requiredRole="admin">
                    <AdminPage />
                  </ProtectedRoute>
                } 
              />
              <Route path="/style/divergent" element={withAuth(DivergentPage)} />
            </Routes>
          </div>
          
          {/* 移动端提示 */}
          {showMobileTip && (
            <div className="mobile-tip">
              使用电脑访问，获得最佳体验~
              <button className="close-tip" onClick={handleCloseMobileTip} />
            </div>
          )}

          {/* 通知提示 */}
          {notification.message && (
            <div className={`notification ${notification.type}`} style={{ whiteSpace: 'pre-line' }}>
              {notification.message}
            </div>
          )}

          {/* 编辑常用功能弹窗 */}
          {showEditModal && (
            <EditFeaturesModal
              isOpen={showEditModal}
              isSidebarCollapsed={isSidebarCollapsed}
              allFeatures={ALL_FEATURES}
              commonFeatures={commonFeatures}
              onClose={() => setShowEditModal(false)}
              onFeatureChange={handleCheckboxChange}
              renderIcon={renderIcon}
            />
          )}

          {/* 使用AuthModals组件 */}
          <AuthModals
            showLoginModal={showLoginModal}
            showResetPassword={showResetPassword}
            setShowLoginModal={setShowLoginModal}
            setShowResetPassword={setShowResetPassword}
            onNotification={handleNotification}
            renderIcon={renderIcon}
          />

          {/* 视频预览模态框 */}
          {showVideoModal && playingVideoUrl && (
            <div
              className="image-preview-modal"
              onClick={handleCloseVideoModal}
            >
              <div
                className="preview-content video-preview-content"
                onClick={e => e.stopPropagation()}
              >
                <button
                  className="preview-close-button"
                  onClick={handleCloseVideoModal}
                >
                  {renderIcon(MdClose)}
                </button>
                <video
                  src={playingVideoUrl}
                  controls
                  autoPlay
                  className="preview-video"
                />
              </div>
            </div>
          )}

          {/* 使用新的独立ImagePreviewModal组件 */}
          <ImagePreviewModal
            visible={showImageModal}
            imageUrl={previewImageUrl}
            onClose={handleCloseImageModal}
            alt={previewFeatureName || "预览图片"}
            showHint={!previewFeatureName} // 如果有功能名称就不显示提示
            maxScale={4}
            minScale={0.5}
            featureName={previewFeatureName as any} // 传递功能名称
          />

          {/* 联系客服模态框 */}
          {showContactModal && (
            <div className="modal-overlay" onClick={() => setShowContactModal(false)}>
              <div className="modal-content contact-modal" onClick={(e) => e.stopPropagation()}>
                <div className="modal-header">
                  <h2>联系客服</h2>
                  <button
                    className="medium-close-button"
                    onClick={() => setShowContactModal(false)}
                  >
                    {renderIcon(MdClose)}
                  </button>
                </div>
                <div className="modal-body">
                  <ContactSupport />
                      </div>
              </div>
            </div>
          )}
          {/* 只在首页和订阅页面显示Footer */}
          {(location.pathname === '/' || location.pathname === '/subscribe') && (
            <div className={isSidebarCollapsed ? 'sidebar-collapsed' : ''}>
              <Footer />
            </div>
          )}

          {sidebarGuidePopup && (
            <div
              className="sidebar-guide-img-popup-global"
              style={{
                position: 'fixed',
                left: sidebarGuidePopup.x + 12, // 向左移动4个像素（从16改为12）
                top: sidebarGuidePopup.y - 112.5, // 垂直居中（225/2）
                zIndex: 99999,
                background: 'var(--bg-primary)',
                border: '1px solid var(--border-color)', // 完整的边框
                borderRadius: '10px',
                boxShadow: 'var(--shadow-sm)', // 使用ControlPanel相同的阴影效果
                padding: '8px',
                width: '316px', // 300px图片 + 16px内边距
                height: '241px', // 225px图片 + 16px内边距
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                pointerEvents: 'none',
                opacity: isPopupVisible ? 1 : 0,
                transform: isPopupVisible ? 'scale(1) translateX(0)' : 'scale(0.9) translateX(-10px)',
                transition: 'opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                animation: isPopupVisible ? 'popupFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1)' : 'none',
              }}
            >
              {/* 小三角形尖尖 */}
              <div style={{
                position: 'absolute',
                left: '-8px', // 向左移动一个像素
                top: 'calc(50% - 10px)',
                width: 0,
                height: 0,
                borderTop: '10px solid transparent',
                borderBottom: '10px solid transparent',
                borderRight: '9px solid var(--bg-primary)', // 宽度+1px
                borderRadius: '3px',
                zIndex: 100000,
                pointerEvents: 'none',
                animation: isPopupVisible ? 'triangleFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both' : 'none',
              }} />
              {/* 小三角形的边框 */}
              <div style={{
                position: 'absolute',
                left: '-9px', // 边框三角形也左移1px
                top: 'calc(50% - 11px)',
                width: 0,
                height: 0,
                borderTop: '11px solid transparent',
                borderBottom: '11px solid transparent',
                borderRight: '10px solid var(--border-color)', // 宽度+1px
                zIndex: 99999,
                pointerEvents: 'none',
                animation: isPopupVisible ? 'triangleFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both' : 'none',
              }} />
              <img
                src={getSidebarGuideImgUrl(sidebarGuidePopup.pageType)}
                alt="功能引导"
                style={{ 
                  width: '300px', // 精确的图片宽度
                  height: '225px', // 精确的图片高度（四比三比例）
                  objectFit: 'cover', 
                  borderRadius: '8px',
                  animation: isPopupVisible ? 'imageFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) 0.15s both' : 'none',
                }}
                onError={() => {
                  // 图片加载失败时，标记为不存在并隐藏弹窗
                  setImageExists(prev => ({ ...prev, [sidebarGuidePopup.pageType]: false }));
                  setSidebarGuidePopup(null);
                }}
              />
            </div>
          )}
        </div>
      )}
    </>
  )
:(
  <AdminPage />
)
);
};

export default App;<style>
{`
  @keyframes popupFadeIn {
    from {
      opacity: 0;
      transform: scale(0.9) translateX(-10px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateX(0);
    }
  }
  
  @keyframes triangleFadeIn {
    from {
      opacity: 0;
      transform: translateX(-5px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes imageFadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
`}
</style>

