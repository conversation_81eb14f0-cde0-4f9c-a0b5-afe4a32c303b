// 场景分类配置
export const SCENE_CATEGORIES = [
  { key: 'beach', name: '海滩' },
  { key: 'pool', name: '泳池' },
  { key: 'nature', name: '自然' },
  { key: 'street', name: '街道' },
  { key: 'indoor', name: '室内' },
  { key: 'other', name: '其他' }
];

// OSS基础路径
export const SCENE_OSS_BASE = 'https://file.aibikini.cn/config/scenes/';

// 每个分类最大图片数量
export const MAX_IMAGES_PER_CATEGORY = 20;

// 动态获取某分类下所有图片URL - 优化版本，使用并行请求
export const fetchSceneImages = async (categoryKey, maxCount = MAX_IMAGES_PER_CATEGORY) => {
  // 创建并行请求数组
  const requests = [];
  for (let i = 1; i <= maxCount; i++) {
    const num = String(i).padStart(2, '0');
    const thumbnailUrl = `${SCENE_OSS_BASE}${categoryKey}/${num}.jpg`;
    const fullImageUrl = `${SCENE_OSS_BASE}${categoryKey}/${num}-a.jpg`;
    
    // 创建HEAD请求Promise
    const request = fetch(thumbnailUrl, { method: 'HEAD' })
      .then(res => {
        if (res.ok) {
          return {
            thumbnail: thumbnailUrl,
            fullImage: fullImageUrl,
            index: i
          };
        }
        return null;
      })
      .catch(() => null);
    
    requests.push(request);
  }
  
  // 并行执行所有请求
  const results = await Promise.all(requests);
  
  // 过滤有效结果并按顺序排列
  const urls = results
    .filter(result => result !== null)
    .sort((a, b) => a.index - b.index)
    .map(result => ({
      thumbnail: result.thumbnail,
      fullImage: result.fullImage
    }));
  
  return urls;
};

// 统一加载所有分类图片
export const fetchAllSceneImages = async () => {
  const all = [];
  for (const cat of SCENE_CATEGORIES) {
    const images = await fetchSceneImages(cat.key);
    // 按顺序平铺，保留分类信息
    all.push(...images.map((imageUrls, index) => ({
      id: `${cat.key}-${String(index + 1).padStart(2, '0')}`,
      category: cat.key,
      categoryName: cat.name,
      name: `${cat.name}场景`,
      // 缩略图用于卡片显示
      thumbnail: imageUrls.thumbnail + '?v=' + new Date().getTime(),
      // 大图用于预览和任务生成
      image: imageUrls.fullImage + '?v=' + new Date().getTime()
    })));
  }
  return all;
}; 