/* 使用通用面板样式 */
@import '../../styles/panels.css';

/* 拖拽删除功能样式 */
.panel-component.drag-over {
  position: relative;
}

/* 半透明遮罩效果 */
.panel-component.drag-over::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
  z-index: 5;
  pointer-events: none;
}

/* 文字提示样式 */
.panel-component.drag-over::after {
  content: "重新上传";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--brand-primary);
  font-size: 14px;
  font-weight: normal;
  z-index: 10;
  pointer-events: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 面料图片特有的样式可以在此处添加 */
.fabric-status {
  margin-left: 8px;
  font-size: 13px;
  color: var(--brand-primary);
  display: inline-block;
} 