import React, { useState } from 'react';
import { MdOutlineZoomOutMap } from 'react-icons/md';
import ImagePreviewModal from '../common/ImagePreviewModal';
import './index.css';

/**
 * 参考图片工具组件 - 显示参考图片缩略图
 * 
 * @param {Object} props
 * @param {Object} props.referenceImage - 参考图片信息，包含url、alt和type属性
 */
const ReferenceImageTool = ({ referenceImage }) => {
  const [previewVisible, setPreviewVisible] = useState(false);

  // 处理预览按钮点击
  const handlePreviewClick = (e) => {
    e.stopPropagation(); // 阻止事件冒泡
    setPreviewVisible(true);
  };

  // 关闭预览弹窗
  const handlePreviewClose = () => {
    setPreviewVisible(false);
  };

  return (
    <div className="reference-image-tool">
      <div className="tools-group">
        <div className="reference-image-header">
          <span className="reference-image-label">{referenceImage.type || '参考图片'}</span>
        </div>
        <div className="reference-image-thumbnail">
          <div className="thumbnail-container">
            <img 
              src={referenceImage.url} 
              alt={referenceImage.alt || referenceImage.type || '参考图片'}
              title={referenceImage.alt || referenceImage.type || '参考图片'}
            />
            {/* 悬浮预览按钮 */}
            <div 
              className="thumbnail-overlay"
              onClick={handlePreviewClick}
              title="预览图片"
            >
              <MdOutlineZoomOutMap />
            </div>
          </div>
        </div>
      </div>

      {/* 图片预览弹窗 */}
      <ImagePreviewModal
        visible={previewVisible}
        imageUrl={referenceImage.url}
        onClose={handlePreviewClose}
        alt={referenceImage.alt || referenceImage.type || '参考图片'}
        featureName={`${referenceImage.type || '参考图片'}预览`}
      />
    </div>
  );
};

export default ReferenceImageTool; 