// 功能模块配置
export const FEATURES_CONFIG = {
  // 款式设计相关功能
  styleDesign: [
    {
      id: 'feature3',
      name: '爆款开发（融合+线稿）',
      description: 'AI智能辅助，快速开发爆款泳装设计',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-trending.jpg',
      category: '款式设计'
    },
    {
      id: 'feature22',
      name: '爆款延伸',
      description: '基于现有爆款进行延伸设计，创造更多可能性',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-divergent.jpg',
      category: '款式设计'
    },
    {
      id: 'feature6',
      name: '款式优化',
      description: '精准调整细节，优化每一款泳装设计',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-optimize.jpg',
      category: '款式设计'
    },
    {
      id: 'feature8',
      name: '灵感探索（创款+创意）',
      description: '随机生成灵感，激发更多创意设计思路',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-inspiration.jpg',
      category: '款式设计'
    },
    {
      id: 'feature13',
      name: '换面料',
      description: '智能更换服装面料纹理，呈现不同材质效果',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-fabric.jpg',
      category: '款式设计'
    },
    {
      id: 'feature21',
      name: '生成线稿',
      description: 'AI智能生成服装线稿，为设计提供精准的轮廓基础',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-drawing.jpg',
      category: '款式设计'
    }
  ],
  
  // 模特图相关功能
  modelPhoto: [
    {
      id: 'feature1',
      name: '时尚大片',
      description: '发挥AI真正的优势，打造创意的品牌风格',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-fashion.jpg',
      category: '模特图'
    },
    {
      id: 'feature2',
      name: '模特换装',
      description: '智能模拟真实穿着效果，快速生成模特图',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-tryon.jpg',
      category: '模特图'
    },
    {
      id: 'feature17',
      name: '换模特',
      description: '一键替换图片中的模特，保持原有服装效果',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/change-model.jpg',
      category: '模特图'
    },
    {
      id: 'feature4',
      name: '服装复色',
      description: '一键更换服装颜色，快速生成多个配色方案',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-recolor.jpg',
      category: '模特图'
    },
    {
      id: 'feature5',
      name: '换背景',
      description: '智能抠图换背景，打造专业展示效果',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-background.jpg',
      category: '模特图'
    },
    {
      id: 'feature20',
      name: '换姿势',
      description: '智能调整模特姿势，生成不同动作姿势的专业展示效果',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/change-posture.jpg',
      category: '模特图'
    },
    {
      id: 'feature23',
      name: '模特换脸',
      description: '智能替换模特面部，保持原有服装和姿势效果',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/change-face.jpg',
      category: '模特图'
    },
    {
      id: 'feature7',
      name: '虚拟模特',
      description: '定制专属虚拟模特，塑造品牌个性形象',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-virtual.jpg',
      category: '模特图'
    },
    {
      id: 'feature18',
      name: '细节还原',
      description: '智能还原服装细节，保持细节元素一致性',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/detail-migration.jpg',
      category: '模特图'
    },
    {
      id: 'feature19',
      name: '手部修复',
      description: '智能修复图片中的手部瑕疵，提升图片质量',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/hand-fix.jpg',
      category: '模特图'
    }
  ],

  // AI视频相关功能
  videoTools: [
    {
      id: 'feature15',
      name: '图文成片',
      description: '文本与图片结合，自动生成产品展示视频',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-imgtextvideo.jpg',
      category: 'AI视频'
    },
    {
      id: 'feature16',
      name: '多图成片',
      description: '将多张产品图片自动组合成视频，添加专业转场效果',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-mulimgvideo.jpg',
      category: 'AI视频'
    }
  ],

  // 快捷工具相关功能
  tools: [
    {
      id: 'feature10',
      name: '自动抠图（去背景+抠衣服）',
      description: '极速智能抠图，去除背景或抠出服装主体',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-matting.jpg',
      category: '快捷工具'
    },
    {
      id: 'feature11',
      name: '智能扩图',
      description: '突破图片边界，扩展出不同比例要求的图片',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-extend.jpg',
      category: '快捷工具'
    },
    {
      id: 'feature14',
      name: '图片取词',
      description: '智能扫描图片内容，快速描述出图片信息',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-extract.jpg',
      category: '快捷工具'
    },
    {
      id: 'feature12',
      name: '消除笔',
      description: '智能去除不需要的元素，完善图片细节',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-inpaint.jpg',
      category: '快捷工具'
    },
    {
      id: 'feature9',
      name: '高清放大',
      description: '快速提升图片分辨率，保持清晰细节',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-upscale.jpg',
      category: '快捷工具'
    },
    {
      id: 'feature24',
      name: '服装去色',
      description: '智能去除服装颜色，生成白坯布的效果',
      image: process.env.PUBLIC_URL + 'https://file.aibikini.cn/config/features/feature-bleach.jpg',
      category: '快捷工具'
    }
  ]
};

// 获取所有功能列表
export const getAllFeatures = () => {
  const allFeatures = [...FEATURES_CONFIG.styleDesign, ...FEATURES_CONFIG.modelPhoto, ...FEATURES_CONFIG.videoTools, ...FEATURES_CONFIG.tools];
  
  // 按照指定顺序重新排列
  const orderMap = {
    '时尚大片': 1,
    '模特换装': 2,
    '换模特': 3,
    '服装复色': 4,
    '换背景': 5,
    '换姿势': 6,
    '爆款开发（融合+线稿）': 7,
    '爆款延伸': 8,
    '款式优化': 9,
    '灵感探索（创款+创意）': 10,
    '换面料': 11,
    '生成线稿': 12,
    '虚拟模特': 13,
    '模特换脸': 14,
    '细节还原': 15,
    '手部修复': 16,
    '自动抠图（去背景+抠衣服）': 17,
    '智能扩图': 18,
    '图片取词': 19,
    '消除笔': 20,
    '高清放大': 21,
    '服装去色': 22,
    '图文成片': 23,
    '多图成片': 24,

  };
  
  return allFeatures.sort((a, b) => {
    const orderA = orderMap[a.name] || 999;
    const orderB = orderMap[b.name] || 999;
    return orderA - orderB;
  });
};

// 根据类别获取功能
export const getFeaturesByCategory = (category) => {
  if (category === '款式设计') {
    return FEATURES_CONFIG.styleDesign;
  }
  if (category === '模特图') {
    return FEATURES_CONFIG.modelPhoto;
  }
  if (category === 'AI视频') {
    return FEATURES_CONFIG.videoTools;
  }
  if (category === '快捷工具') {
    return FEATURES_CONFIG.tools;
  }
  return [];
};

// 根据ID获取功能
export const getFeatureById = (id) => {
  const allFeatures = getAllFeatures();
  return allFeatures.find(feature => feature.id === id);
};

// 默认导出所有功能模块配置和方法
export default {
  FEATURES_CONFIG,
  getAllFeatures,
  getFeaturesByCategory,
  getFeatureById
}; 