.showcase-container {
  padding: 16px;
  background-color: var(--bg-primary);
  border-radius: 8px;
}

/* 加载状态样式 */
.showcase-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 40px 16px;
}

.showcase-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.showcase-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--brand-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.showcase-loading-content span {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 瀑布流容器样式 */
.showcase-waterfall {
  width: 100%;
  margin: 0 auto;
  padding: 0;
}

.showcase-item {
  width: calc(20% - 12.8px); /* 5列布局 */
  margin-bottom: 16px;
  background: transparent;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  opacity: 1;
  visibility: visible;
  position: relative;
  break-inside: avoid; /* 防止卡片被分割 */
}

.showcase-item:hover {
  transform: translateY(0px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.showcase-item img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
  border-radius: 8px;
}

/* 响应式布局 */
@media (max-width: 1800px) {
  .showcase-item {
    width: calc(25% - 12px); /* 4列 */
  }
}

@media (max-width: 1400px) {
  .showcase-item {
    width: calc(33.333% - 11px); /* 3列 */
  }
}

@media (max-width: 1024px) {
  .showcase-item {
    width: calc(33.333% - 11px); /* 3列 - 移动端至少显示3列 */
  }
}

@media (max-width: 768px) {
  .showcase-item {
    width: calc(33.333% - 11px); /* 3列 - 中等移动端显示3列 */
  }
}

@media (max-width: 480px) {
  .showcase-item {
    width: calc(50% - 12px); /* 2列 - 小屏幕移动端显示2列 */
    margin-bottom: 16px;
  }
}

.showcase-item h3 {
  margin: 8px 16px 4px;
  font-size: 16px;
  color: var(--text-primary);
}

.showcase-item p {
  margin: 0 16px 16px;
  font-size: 14px;
  color: var(--text-secondary);
}

/* 按钮容器样式 */
.showcase-buttons {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2;
}

.showcase-item:hover .showcase-buttons {
  opacity: 1;
  pointer-events: auto;
}

.showcase-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  font-weight: normal;
  display: flex;
  align-items: center;
  gap: 4px;
}

.preview-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: transparent;
  color: white;
  font-size: 16px;
  text-shadow: 0 2px 3px rgba(0, 0, 0, 0.4);
  pointer-events: auto;
}

/* 移动端响应式调整 */
@media (max-width: 768px) {
  .preview-btn {
    font-size: 12px !important; /* 统一使用小字号 */
  }
}

@media (max-width: 480px) {
  .preview-btn {
    font-size: 12px !important; /* 统一使用小字号 */
  }
}

