import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getFeaturesByCategory } from '../../config/features';
import './index.css';

const ToolsPage = () => {
  const navigate = useNavigate();
  const [error, setError] = useState(null);

  const handleNavigate = (path) => {
    try {
      navigate(path);
    } catch (err) {
      setError({
        title: '导航错误',
        message: '无法跳转到指定页面，请稍后重试。'
      });
    }
  };

  if (error) {
    return (
      <div className="error-container">
        <h2>{error.title}</h2>
        <p>{error.message}</p>
        <button onClick={() => setError(null)}>返回</button>
      </div>
    );
  }

  const features = getFeaturesByCategory('快捷工具');

  return (
    <div className="model-page">
      <div className="model-header">
        <h1>快捷工具</h1>
        <p>便捷的图片处理工具集，提升设计效率</p>
      </div>
      <div className="page-feature-grid">
        {features
          .map((feature) => (
          <div 
            key={feature.id} 
            className="page-feature-card"
            onClick={() => {
              const pathMap = {
                '自动抠图（去背景+抠衣服）': '/tools/matting',
                '智能扩图': '/tools/extend',
                '图片取词': '/tools/extract',
                '高清放大': '/tools/upscale',
                '消除笔': '/tools/inpaint',
                '服装去色': '/tools/bleach'
              };
              handleNavigate(pathMap[feature.name]);
            }}
          >
            <div className="feature-icon">
              <img src={feature.image} alt={feature.name} />
            </div>
            <h3>{feature.name}</h3>
            <p>{feature.description}</p>
          </div>
        ))}
      </div>
      <div className="feature-tip">
        点击上方卡片或侧边栏按钮，进入对应功能页面
      </div>
    </div>
  );
};

export default ToolsPage; 