import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getFeaturesByCategory } from '../../config/features';
import './index.css';

const ModelPage = () => {
  const navigate = useNavigate();
  const [error, setError] = useState(null);

  const handleNavigate = (path) => {
    try {
      navigate(path);
    } catch (err) {
      setError({
        title: '导航错误',
        message: '无法跳转到指定页面，请稍后重试。'
      });
    }
  };

  if (error) {
    return (
      <div className="error-container">
        <h2>{error.title}</h2>
        <p>{error.message}</p>
        <button onClick={() => setError(null)}>返回</button>
      </div>
    );
  }

  const features = getFeaturesByCategory('模特图');

  return (
    <div className="model-page">
      <div className="model-header">
        <h1>模特图片</h1>
        <p>使用AI技术创作专业级模特图片，支持多种创作模式</p>
      </div>
      <div className="page-feature-grid">
        {features
          // 临时隐藏指定页面卡片（如需恢复显示其他功能，请删除下方 filter 判断）
          .filter(feature => true)
          .map((feature) => (
          <div 
            key={feature.id} 
            className="page-feature-card"
            onClick={() => {
              const pathMap = {
                '时尚大片': '/model/fashion',
                '模特换装': '/model/try-on',
                '换模特': '/model/change-model',
                '服装复色': '/model/recolor',
                '换背景': '/model/background',
                '虚拟模特': '/model/virtual',
                '细节还原': '/model/detail-migration',
                '手部修复': '/model/hand-fix',
                '换姿势': '/model/change-posture',
                '模特换脸': '/model/change-face'
              };
              handleNavigate(pathMap[feature.name]);
            }}
          >
            <div className="feature-icon">
              <img src={feature.image} alt={feature.name} />
            </div>
            <h3>{feature.name}</h3>
            <p>{feature.description}</p>
          </div>
        ))}
      </div>
      <div className="feature-tip">
        点击上方卡片或侧边栏按钮，进入对应功能页面
      </div>
    </div>
  );
};

export default ModelPage; 