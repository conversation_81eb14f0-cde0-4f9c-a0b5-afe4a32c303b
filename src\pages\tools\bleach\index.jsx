import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { createFlowTask, updateFlowTask, getFlowTasks, getFlowTaskDetail, deleteFlowTask,checkUserBalance } from '../../../api/flowtask';
import { uploadFiles } from '../../../api/ossUpload';
import { executeFlow } from '../../../api/flow';
import { Tabs, message, Button, Modal, Input, Row, Col, Divider, Space, Tag, Popover, Tooltip, Typography, Progress, Upload, Spin } from 'antd';
import { CloudUploadOutlined, UploadOutlined, DeleteOutlined, SettingOutlined, InfoCircleOutlined, PlusOutlined, DownloadOutlined, EyeOutlined, SyncOutlined, CopyOutlined, ZoomInOutlined, ZoomOutOutlined, UndoOutlined, RedoOutlined, FullscreenOutlined, ExclamationCircleOutlined, CloseOutlined, LeftOutlined, RightOutlined, CheckOutlined } from '@ant-design/icons';
import { v4 as uuidv4 } from 'uuid';
import JSZip from 'jszip';
import Masonry from 'masonry-layout';
import { MdClose } from 'react-icons/md';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';  

// 组件导入
import './index.css';
import UploadBox from '../../../components/UploadBox';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import TaskPanel from '../../../components/TaskPanel';
import Showcase from '../../../components/Showcase';
import ControlPanel from '../../../components/ControlPanel';
import ResizeHandle from '../../../components/ResizeHandle';
import GenerationArea from '../../../components/GenerationArea';
import RequireLogin from '../../../components/RequireLogin';
import ImageZoomControl from '../../../components/ImageZoomControl';
import ImageNavigator from '../../../components/ImageNavigator';
import ThumbnailList from '../../../components/ThumbnailList';
import RandomSeedSelector from '../../../components/RandomSeedSelector';
import QuantityPanel from '../../../components/QuantityPanel';
import UploadGuideModal from '../../../components/UploadGuideModal';
import SourceImagePanel from '../../../components/SourceImagePanel';
import ImageInfoModal from '../../../components/ImageInfoModal';
import { WORKFLOW_NAME } from '../../../data/workflowName';

// API和工具导入
import request from '../../../api/request';
import { getCurrentUserId } from '../../../api';
import { uploadImage } from '../../../api/upload';
import { 
  getTasks, 
  getTaskById, 
  deleteTask, 
  createTask,
  filterTasksByUser,
  getFakeTasksForUser
} from '../../../api/task';
import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import { handleBatchDownload as downloadHelper } from '../../../utils/downloadHelper';
import { generateId, ID_TYPES } from '../../../utils/idGenerator';
import { getTaskComponent } from '../../../utils/taskAdapters';
import { useTaskContext } from '../../../contexts/TaskContext';

const MemoizedImageDetailsModal = React.memo(ImageDetailsModal);

const BleachPage = ({ isLoggedIn, userId }) => {
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const containerRef = useRef(null);
  const controlPanelRef = useRef(null);
  const handleRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const masonryRef = useRef(null);
  const [showUploadGuide, setShowUploadGuide] = useState(false);
  const [uploadGuideType, setUploadGuideType] = useState('source');
  const [isProcessing, setIsProcessing] = useState(false);
  const [sourceImagePanels, setSourceImagePanels] = useState([]);
  const [currentReuploadSourceImagePanelId, setCurrentReuploadSourceImagePanelId] = useState(null);
  const [showSourceUploadGuide, setShowSourceUploadGuide] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [showImageInfo, setShowImageInfo] = useState(false);
  const [currentImagePanel, setCurrentImagePanel] = useState(null);
  const [imageQuantity, setImageQuantity] = useState(1); // 默认生成一张图片
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [imageDetailsTask, setImageDetailsTask] = useState(null);
  const [showAdvancedText, setShowAdvancedText] = useState(false);

  const [advancedPopupPosition, setAdvancedPopupPosition] = useState({ top: 0, left: 0 });
  
  // 添加拖动状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });
  
  // 添加图片缩放相关状态
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const imageRef = useRef(null);
  
  // 添加随机种子相关状态
  const [useRandomSeed, setUseRandomSeed] = useState(true);
  const [seed, setSeed] = useState(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));
  
  // 添加加载状态
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);
  
  // 任务列表状态
  const [generationTasks, setGenerationTasks] = useState([]);
  const generationAreaRef = useRef(null);
  
  const { updateTask } = useTaskContext();
  
  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);

  // 处理文件上传
  const handleFileUpload = (file) => {
    setShowUploadGuide(false);
  };

  // 处理原始图片上传结果
  const handleSourceUploadResult = (results) => {
    setHasUnsavedChanges(true);
    console.log('原始图片上传结果:', results);
    
    try {
      if (results.type === 'panels') {
        if (currentReuploadSourceImagePanelId) {
          // 如果是重新上传，替换原有面板
          setSourceImagePanels(prevPanels => 
            prevPanels.map(panel => 
              panel.componentId === currentReuploadSourceImagePanelId 
                ? { ...results.panels[0], componentId: currentReuploadSourceImagePanelId, type: 'source', source: results.panels[0].source || 'upload' }
                : panel
            )
          );
          // 重置当前重新上传的面板ID
          setCurrentReuploadSourceImagePanelId(null);
        } else {
          // 如果是新上传，添加新面板
          const panelsWithType = results.panels.map(panel => ({
            ...panel,
            type: 'source',  // 设置业务类型
            source: panel.source || 'upload'  // 保留source属性或设置默认值
          }));
          setSourceImagePanels(prevPanels => [...prevPanels, ...panelsWithType]);
        }
        
        // 移除对显示提示的设置
        // setShowTips(true);
      } else if (results.type === 'update') {
        // 处理面板更新（上传完成或失败的回调）
        console.log('收到面板更新:', results.panel);
        
        if (results.panel) {
          const updatedPanel = results.panel;
          
          // 检查是否包含服务器处理完成的标志
          const isUploadCompleted = updatedPanel.serverFileName || 
                                   (updatedPanel.processInfo && Object.keys(updatedPanel.processInfo).length > 0);
          
          // 强制设置完成状态的标志
          const forceCompleted = isUploadCompleted && (updatedPanel.status === 'uploading' || updatedPanel.status === 'processing');
          
          if (forceCompleted) {
            console.log('检测到上传已完成但状态未更新，强制设置为completed状态');
          }
          
          // 更新对应ID的面板
          setSourceImagePanels(prevPanels => 
            prevPanels.map(panel => {
              if (panel.componentId === updatedPanel.componentId) {
                console.log(`更新面板 ${panel.componentId}:`);
                console.log('- 原始状态:', panel.status);
                console.log('- 服务器返回状态:', updatedPanel.status);
                console.log('- 最终状态:', forceCompleted ? 'completed' : (updatedPanel.status || 'completed'));
                
                // 返回更新后的面板，确保保留所有重要字段
                return {
                  ...panel,
                  // 如果检测到上传完成标志但状态仍为uploading或processing，则强制设为completed
                  status: forceCompleted ? 'completed' : (updatedPanel.status || 'completed'),
                  errorMessage: updatedPanel.errorMessage,
                  processedFile: updatedPanel.processedFile,
                  processedUrl: updatedPanel.processedUrl,
                  processInfo: updatedPanel.processInfo,
                  serverFileName: updatedPanel.serverFileName || panel.serverFileName,
                  // 保留source属性
                  source: updatedPanel.source || panel.source || 'upload'
                };
              }
              return panel;
            })
          );
        }
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        // 移除处理中的面板
        setSourceImagePanels(prevPanels => 
          prevPanels.filter(panel => panel.status !== 'processing'));
        // 重置当前重新上传的面板ID
        setCurrentReuploadSourceImagePanelId(null);
      }
    } catch (error) {
      console.error('上传结果处理失败:', error);
      message.error('上传结果处理失败: ' + (error.message || '未知错误'));
      // 移除处理中的面板
      setSourceImagePanels(prevPanels => 
        prevPanels.filter(panel => panel.status !== 'processing'));
      // 重置当前重新上传的面板ID
      setCurrentReuploadSourceImagePanelId(null);
    }
  };

  // 处理原始图片上传
  const handleSourceFileUpload = async (files) => {
    // const  {fileInfos}=await uploadFiles(files,"bleach"); 
    // if(fileInfos){
    //   const resultData = fileInfos[0];
    //   const imageId = generateId(ID_TYPES.COMPONENT);
    //   const updatedPanel = {
    //     ...sourceImagePanels[0],
    //     fileInfo: {
    //       ...sourceImagePanels[0],
    //       width: resultData.width,
    //       height: resultData.height,
    //       format: resultData.format,
    //       ...resultData
    //     }
    //   };
      
    //   console.log('更新后的面板数据:', JSON.stringify(updatedPanel, null, 2));
      
    //   // 更新面板中的图片信息
    //   setSourceImagePanels(prevPanels => {
    //     const newPanels = prevPanels.map(p => 
    //       p.componentId === imageId ? updatedPanel : p
    //     );
    //     console.log('更新后的sourceImagePanels数组:', JSON.stringify(newPanels, null, 2));
    //     return newPanels;
    //   });
    // }
  };

  // 处理删除原始图片面板
  const handleDeleteSourceImagePanel = (panelId) => {
    setHasUnsavedChanges(true);
    setSourceImagePanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
  };

  // 处理重新上传原始图片
  const handleReuploadSource = (panel) => {
    if (panel && panel.componentId) {
      // 第一步：删除当前面板
      handleDeleteSourceImagePanel(panel.componentId);
      
      // 第二步：延迟一点点时间后触发上传区域点击（确保UI已更新）
      setTimeout(() => {
        // 如果已经没有面板了，上传框应该会显示出来，直接触发其点击事件
        const uploadBox = document.getElementById('source-upload-box');
        if (uploadBox) {
          uploadBox.click();
        }
      }, 50);
    }
  };

  // 处理原始图片状态变化
  const handleSourceImageStatusChange = (panelId, newStatus) => {
    setSourceImagePanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId ? { ...panel, status: newStatus } : panel
      )
    );
  };

  // 处理单个文件上传
  const handleSingleFileUpload = async (file) => {
    try {
      // 为图片创建唯一ID
      const imageId = generateId(ID_TYPES.COMPONENT);
      const imageUrl = URL.createObjectURL(file);
      
                // 创建面板数据，状态为completed
      const panel = {
        componentId: imageId,
        title: '原始图片',
        status: 'completed', // 初始状态直接设为completed，因为不需要上传
        serverFileName: file.name,
        url: imageUrl, // 直接使用本地URL
        fileInfo: {
          name: file.name,
          size: file.size,
          type: file.type
        },
        type: 'source',   // 业务类型：原始图片
        source: 'upload', // 图片来源：用户上传
        file: file        // 保存原始文件对象，供后续上传使用
      };
      
      // 先通知面板已创建，用户体验更好
      handleSourceUploadResult({ type: 'panels', panels: [panel] });
      
      // 获取图片的宽高信息
      const img = new Image();
      img.onload = async () => {
        // 获取到图片的宽高后，更新面板信息
        console.log('图片加载完成，获取到宽高:', {
          width: img.naturalWidth,
          height: img.naturalHeight,
          format: file.type
        });
        
        const updatedPanel = {
          ...panel,
          fileInfo: {
            ...panel.fileInfo,
            width: img.naturalWidth,
            height: img.naturalHeight,
            format: file.type
          }
        };
        
        console.log('更新后的面板数据:', JSON.stringify(updatedPanel, null, 2));
        
        // 更新面板中的图片信息
        setSourceImagePanels(prevPanels => {
          const newPanels = prevPanels.map(p => 
            p.componentId === imageId ? updatedPanel : p
          );
          console.log('更新后的sourceImagePanels数组:', JSON.stringify(newPanels, null, 2));
          return newPanels;
        });
        
        // 不再立即上传图片到服务器，而是在点击生成按钮时再上传
        message.success('图片上传成功');
      };
      
      img.onerror = () => {
        console.error('加载图片失败，无法获取宽高');
        // 更新面板状态为错误
        setSourceImagePanels(prevPanels => 
          prevPanels.map(panel => 
            panel.componentId === imageId ? { ...panel, status: 'error', error: '无法获取图片尺寸' } : panel
          )
        );
      };
      
      // 开始加载图片以获取宽高
      img.src = imageUrl;
    } catch (error) {
      console.error('处理上传图片时出错:', error);
      handleSourceUploadResult({ 
        type: 'error', 
        error: error.message 
      });
    }
  };

  // 处理开始生成按钮点击
  const handleGenerate = async () => {
    console.log('开始生成...');
    setIsProcessing(true);

    setSeed(useRandomSeed? (Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)) : seed);

    let taskData = null;
    try {
      // 检查是否有原始图片面板
      if (sourceImagePanels.length === 0) {
        message.error('请先上传原始图片');
        setIsProcessing(false);
        return;
      }

      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId();
      const balance = await checkUserBalance('服装去色', 'bleach', imageQuantity);
      if(balance.code !== 200){
        message.error(balance.message);
        setIsProcessing(false);
        return;
      }
      // 处理可能的自定义上传的原始图片
      let sourceToUse = sourceImagePanels[0];
      
      // 从TaskPanel拖拽过来的原始图片也需要上传到服务器
      if ((sourceImagePanels[0].file && sourceImagePanels[0].source === 'upload') ||
          (sourceImagePanels[0].source === 'upload' && !sourceImagePanels[0].file)) {
        // 显示上传中提示
        message.loading('正在上传原始图片...', 0);
        
        try {
          let fileToUpload = sourceImagePanels[0].file;
          
          // 如果没有file对象但有URL，需要从URL获取文件
          if (!fileToUpload && sourceImagePanels[0].url) {
            try {
              const response = await fetch(sourceImagePanels[0].url);
              const blob = await response.blob();
              fileToUpload = new File([blob], sourceImagePanels[0].serverFileName || 'source.jpg', {
                type: blob.type || 'image/jpeg'
              });
            } catch (error) {
              console.error('从URL获取原始图片文件失败:', error);
              message.error('原始图片处理失败，请重试');
              setIsProcessing(false);
              return;
            }
          }
          
          // 将文件上传到服务器 - 此时才真正上传图片
          const uploadResult = await uploadFiles([fileToUpload],"trending");
          
          if (uploadResult) {
            const resultData = uploadResult.fileInfos[0];
            
            // 创建新的图片对象，包含服务器URL
            sourceToUse = {
              ...sourceImagePanels[0],
              url: resultData.url,
              serverFileName: sourceImagePanels[0].serverFileName,
              originalImage: sourceImagePanels[0].url,
              originalUrl: resultData.url,
              processInfo: resultData,
              source: 'history', // 更新图片来源为历史记录，表示已在服务器上
              file: undefined,
              fileInfo: {
                ...(sourceImagePanels[0].fileInfo || {}),
                serverFileName: resultData.serverFileName,
                ...resultData
              }
            };
            // 设置宽度和尺寸
            sourceToUse.fileInfo.width = resultData.width;
            sourceToUse.fileInfo.height = resultData.height;
            sourceToUse.fileInfo.format = resultData.format;
            
            message.success('原始图片上传成功');
            
            // 更新面板状态
            setSourceImagePanels(prevPanels => 
              prevPanels.map(panel => 
                panel.componentId === sourceImagePanels[0].componentId ? sourceToUse : panel
              )
            );
          } else {
            message.error('原始图片上传失败');
            setIsProcessing(false);
            message.destroy();
            return;
          }
        } catch (error) {
          console.error('上传图片时出错:', error);
          message.error('图片上传失败: ' + (error.message || '未知错误'));
          setIsProcessing(false);
          message.destroy();
          return;
        } finally {
          // 关闭上传中提示
          message.destroy();
        }
      }

      // 创建一个新的任务ID
      const taskId = generateId(ID_TYPES.TASK);
      
      // 构建任务对象，使用服务器上的图片URL
      taskData = {
        taskId: taskId,
        userId: currentUserId,
        createdAt: new Date(),
        status: 'processing',
        imageCount: imageQuantity, // 使用当前设置的生成数量
        taskType: 'bleach', // 指定任务类型为服装去色
        pageType: 'bleach',
        // 添加顶级字段，避免"任务缺少serverFileName字段"和"任务缺少primaryImageFileName字段"警告
        serverFileName: sourceToUse.serverFileName,
        primaryImageFileName: sourceToUse.serverFileName,
        // 使用组件数组而不是对象
        components: [
          {
            componentType: 'sourceImagePanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            title: '原始图片',
            type: 'source',          // 业务类型：原始图片
            source: sourceToUse.source || 'history', // 保留来源信息
            originalImage: sourceToUse.url,
            url: sourceToUse.url, // 使用更新后的URL（可能是服务器URL）
            serverFileName: sourceToUse.serverFileName,
            fileInfo: {
              ...(sourceToUse.fileInfo || {}),
              serverFileName: sourceToUse.serverFileName
            }
          },
          {
            componentType: 'randomSeedSelector',
            componentId: generateId(ID_TYPES.COMPONENT),
            useRandom: false,
            value: seed  // 随机则值为-1，固定则使用当前种子值
          },
          {
            componentType: 'quantityPanel',
            componentId: generateId(ID_TYPES.COMPONENT),
            value: imageQuantity
          }
        ],
        // 初始状态下的空图像数组
        generatedImages: Array(imageQuantity).fill(null).map((_, index) => ({ 
          imageIndex: index,
          status: 'processing'
        })),
        processInfo:{
          results:[]
        }
      };
      
      setIsProcessing(true);
      
      // 先添加到本地状态，使UI立即响应
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      await createFlowTask(taskData);
      
      const resultData = await executeFlow(WORKFLOW_NAME.BLEACH,{
        "3":{
          "seed":seed
        },
        "93": {
          "url":sourceToUse.url
        },
        "92": {
          "amount":imageQuantity
        },
        "subInfo":{
          "type": "bleach",
          "title":"服装去色",
          "count":imageQuantity
        }
      },taskData.taskId);
      setIsProcessing(false);
      setHasUnsavedChanges(false);
      if( generationAreaRef.current){

        taskData.promptId = resultData.promptId;
        taskData.instanceId = resultData.instanceId;
        taskData.url = resultData.url;
        taskData.newTask = true;
        taskData.netWssUrl=resultData.netWssUrl;
        taskData.clientId=resultData.clientId;
        generationAreaRef.current.setGenerationTasks(taskData);
    }
    } catch (error) {
      console.error('创建任务失败:', error);
      message.error('创建任务失败: ' + error.message);
      taskData.status = 'failed';
      setIsProcessing(false);
      setHasUnsavedChanges(false);
      
      if( generationAreaRef.current){
        generationAreaRef.current.setGenerationTasks(taskData);
      }
    }
  };


  // 处理编辑任务
  const handleEditTask = (task) => {
    try {
      // 控制台记录任务数据
      console.log('编辑任务:', task);
      
      console.log('任务组件:', task.components);
      
      // 使用标准化的组件数组形式
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);
      
      // 直接从组件数组中查找相应组件 - 使用标准组件名称
      const sourceComponent = components.find(c => c.componentType === 'sourceImagePanel');
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      const quantityComponent = components.find(c => c.componentType === 'quantityPanel');
      
      console.log('获取到的编辑组件:', {
        sourceComponent,
        seedComponent,
        quantityComponent
      });
      
      // 恢复原始图片面板
      if (sourceComponent) {
        setSourceImagePanels([
          {
            ...sourceComponent,
            status: 'completed'
          }
        ]);
        console.log('回填原始图片:', sourceComponent);
      } else {
        setSourceImagePanels([]);
        console.warn('未找到原始图片组件(sourceImagePanel)，请检查任务数据');
      }
      
      // 恢复随机种子设置
      if (seedComponent) {
        // 检查是否有实际使用的种子值
        if (seedComponent.value >= 0 && !seedComponent.useRandom) {
          // 如果存在实际使用的种子值，无论原来是否设置为随机，都回填为固定种子
          setUseRandomSeed(false);
          setSeed(Number(seedComponent.value));
          console.log('回填固定种子值:', seedComponent.value);
        } else {
          // 如果没有固定的种子值，则保持原有设置
          setUseRandomSeed(seedComponent.useRandom);
          if (!seedComponent.useRandom && seedComponent.value !== -1) {
            setSeed(Number(seedComponent.value));
          }
          console.log('回填种子设置:', seedComponent);
        }
      } 
      // 回退1：检查任务本身是否有种子值
      else if (task.seed !== undefined) {
        // 如果任务有种子值，回填为固定种子
        setUseRandomSeed(false);
        setSeed(Number(task.seed));
        console.log('使用任务级种子:', task.seed);
      }
      // 回退2：使用默认随机种子
      else {
        setUseRandomSeed(true);
        setSeed(Math.floor(Math.random() * 1000000));
        console.warn('未找到种子组件(randomSeedSelector)，使用默认随机种子');
      }
      
      // 恢复图片数量设置
      if (quantityComponent && (quantityComponent.quantity || quantityComponent.value)) {
        setImageQuantity(quantityComponent.quantity || quantityComponent.value);
        console.log('回填图片数量:', quantityComponent.quantity || quantityComponent.value);
      } 
      // 回退1：使用任务级数量
      else if (task.imageCount) {
        setImageQuantity(task.imageCount);
        console.log('使用任务级数量:', task.imageCount);
      }
      // 回退2：使用默认值
      else {
        setImageQuantity(1); // 默认生成一张图片
        console.warn('未找到数量组件(quantityPanel)，使用默认值');
      }
      
      // 切换到结果标签页
      setActiveTab('result');
      
      // 显示成功消息
      message.success({
        content: '配置已重新导入，可继续进行调整',
        duration: 5
      });
      
      console.log('重新编辑任务完成:', {
        sourceImagePanel: sourceComponent,
        seed: seedComponent,
        imageQuantity: quantityComponent?.value || quantityComponent?.quantity || task.imageCount || 1,
        activeTab: 'result'
      });
    } catch (error) {
      console.error('处理编辑任务时出错:', error);
      message.error('恢复任务设置失败: ' + (error.message || '未知错误'));
    }
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const handleViewDetails =  (image, task) => {
    try {
      console.log('查看图片详情:', image);
      console.log('相关任务数据:', task);
      
      // 使用标准化的组件数组形式
      const components = Array.isArray(task.components) ? task.components : [];
      console.log('处理的组件数据:', components);
      
      // 直接获取各组件
      const sourceComponent = components.find(c => c.componentType === 'sourceImagePanel');
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      const quantityComponent = components.find(c => c.componentType === 'quantityPanel');
      
      console.log('获取到的详情组件:', {
        sourceComponent,
        seedComponent,
        quantityComponent
      });
      
      
      // 准备规范化的组件数据 - 添加回退机制
      const adaptedComponents = {
        sourceImagePanel: {
          ...sourceComponent,
          componentType: 'sourceImagePanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          title: sourceComponent.title || '原始图片',
          type: sourceComponent.type || 'source',
          originalImage: sourceComponent.originalImage,
          url: sourceComponent.url,
        } ,
        
        randomSeedSelector: {
          ...seedComponent,
          componentType: 'randomSeedSelector',
          useRandom: seedComponent.useRandom !== undefined ? seedComponent.useRandom : false,
          value: Number(seedComponent.value !== undefined ? seedComponent.value : 
                 (task.seed !== undefined ? task.seed : 0))
        } ,
        
        quantityPanel: {
          ...quantityComponent,
          componentType: 'quantityPanel',
          value: quantityComponent.value || quantityComponent.quantity || task.imageCount || 1
        } 
      };
      
      console.log('为详情模态框准备的组件数据:', adaptedComponents);
      // 设置任务信息
      return {
        ...task,
        ...image,
        components: adaptedComponents // 使用规范化的组件数据
      }
      
    } catch (error) {
      console.error('处理查看图片详情时出错:', error);
      message.error('无法加载图片详情');
    }
  };

  // 添加关闭弹窗时的处理函数
  const handleCloseImageDetails = () => {
    // 直接关闭弹窗，不使用动画
    setShowImageDetails(false);
    
    // 重置状态，无需延迟
    setSelectedImage(null);
    // 重置任务信息，避免保留旧任务导致重新打开时出错
    setImageDetailsTask(null);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
    lastPosition.current = { x: 0, y: 0 };
    setImageScale(100);
    setInitialScale(100);
    

    
    // 重置文本弹窗状态
    setShowAdvancedText(false);
  };

  // 添加点击外部关闭弹出层的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否发生在文本按钮上
      const isTextButton = event.target.closest('.text-button');
      // 检查点击是否发生在弹窗内部
      const isInsidePopup = event.target.closest('.text-popup');
      
      // 如果点击既不是文本按钮也不是弹窗内部，则关闭所有弹窗
      if (!isTextButton && !isInsidePopup) {
        setShowAdvancedText(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 计算初始缩放比例
  const calculateInitialScale = (img) => {
    if (!img) return 100;
    const container = img.parentElement;
    if (!container) return 100;
    
    // 计算图片在容器中的实际显示尺寸与真实尺寸的比例
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;
    
    // 计算图片适应容器时的尺寸
    const containerRatio = containerWidth / containerHeight;
    const imageRatio = imageNaturalWidth / imageNaturalHeight;
    
    let scale;
    if (imageRatio > containerRatio) {
      // 图片较宽，以容器宽度为基准
      scale = (containerWidth / imageNaturalWidth) * 100;
    } else {
      // 图片较高，以容器高度为基准
      scale = (containerHeight / imageNaturalHeight) * 100;
    }

    // 如果计算出的缩放比例大于100%，说明图片实际尺寸小于容器
    // 这种情况下我们应该将图片显示为其实际大小
    if (scale > 100) {
      scale = 100;
    }
    
    // 返回相对于真实尺寸的缩放百分比，四舍五入到整数
    return Math.round(scale);
  };

  // 处理图片加载完成
  const handleImageLoad = (e) => {
    const img = e.target;
    const initialScaleValue = calculateInitialScale(img);
    setInitialScale(initialScaleValue);  // 设置初始比例
    setImageScale(initialScaleValue);    // 设置当前比例
    imageRef.current = img;
  };

  // 处理缩放变化
  const handleScaleChange = (newScale) => {
    setImageScale(newScale);
  };

  // 处理重置图片位置和缩放
  const handleReset = () => {
    setImageScale(initialScale);
    setImagePosition({ x: 0, y: 0 });
    lastPosition.current = { x: 0, y: 0 };
  };

  // 处理图片上传
  const handleUpload = async (file, fileList) => {
    // ... existing code ...
  };

  // 检查任务状态
  const checkTaskStatus = async (taskId) => {
    try {
      // 获取当前用户ID
      const currentUserId = userId || getCurrentUserId() || 'developer';
      
      // 调用API获取任务状态
      const task = await getTaskById(taskId, currentUserId);
      
      if (task) {
        console.log('任务状态:', task.status);
        
        // 根据任务状态更新UI
        if (task.status === 'completed') {
          // 任务完成，获取生成的图片
          setGenerationTasks(prevTasks => 
            prevTasks.map(t => 
              t.taskId === taskId ? task : t
            )
          );
          
          setIsProcessing(false);
          message.success('图片生成成功');
        } else if (task.status === 'failed') {
          // 任务失败
          setIsProcessing(false);
          message.error('服装去色失败: ' + (task.errorMessage || '未知错误'));
          
          // 更新任务状态
          setGenerationTasks(prevTasks => 
            prevTasks.map(t => 
              t.taskId === taskId ? { ...t, status: 'failed', errorMessage: task.errorMessage } : t
            )
          );
        } else {
          // 任务仍在处理中，继续轮询
          setTimeout(() => checkTaskStatus(taskId), 2000); // 每2秒检查一次
        }
      } else {
        setIsProcessing(false);
        message.error('获取任务状态失败: 任务不存在');
        
        // 更新任务状态为失败
        setGenerationTasks(prevTasks => 
          prevTasks.map(t => 
            t.taskId === taskId ? { ...t, status: 'failed', errorMessage: '任务不存在' } : t
          )
        );
      }
    } catch (error) {
      console.error('获取任务状态失败:', error);
      setIsProcessing(false);
      message.error('获取任务状态失败: ' + (error.message || '未知错误'));
      
      // 修正：补充task变量定义
      const task = generationTasks.find(t => t.taskId === taskId) || {};
      // 更新任务状态为失败
      const failedTask = { ...task, status: 'failed', errorMessage: error.message || '未知错误' };
      setGenerationTasks(prevTasks => 
        prevTasks.map(t => 
          t.taskId === taskId ? failedTask : t
        )
      );
      // 调用updateTask以触发失败提示音
      updateTask(failedTask);
    }
  };

  const [currentTaskId, setCurrentTaskId] = useState(null);

  return (
    <RequireLogin isLoggedIn={isLoggedIn} featureName="服装去色功能">
      <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
      <div className="bleach-page">
        <div className="bleach-container" ref={containerRef}>
          <ControlPanel
            ref={controlPanelRef}
            width={`${controlPanelWidth}%`}
            onGenerate={handleGenerate}
            disabled={isProcessing}
            featureName="bleach"
            quantity={imageQuantity || 1}
          >
                    {/* 原始图片上传区域或原始图片面板 - 位于最上方 */}
                    {sourceImagePanels.length === 0 ? (
                      <UploadBox
                        id="source-upload-box"
                        onUpload={handleSourceFileUpload}
                        onShowGuide={(files, isGalleryMode) => {
                          // 如果传入了文件，直接处理上传
                          if (files && files.length > 0) {
                            handleSingleFileUpload(files[0]);
                          } else {
                            // 否则直接触发文件选择器点击
                            document.getElementById('source-upload-box').click();
                          }
                        }}
                        onUploadResult={handleSourceUploadResult}
                        panels={sourceImagePanels}
                        className="mt-2"
                        showSupportTag={false}
                        pageType="bleach"
                        uploadType="source"
                      />
                    ) : (
                      // 展示原始图片面板
                      sourceImagePanels.map((panel) => (
                        <SourceImagePanel
                          key={panel.componentId}
                          panel={panel}
                          onExpandClick={(panel, position) => {
                            // 创建增强的面板对象，避免警告
                            const enhancedPanel = {
                              ...panel,
                              // 标记为增强的原始图片面板以避免不必要的警告
                              isEnhancedSourceImagePanel: true, // 添加原始图片标记
                              isEnhancedModelPanel: true,
                              processedFile: panel.processedFile || panel.preview || panel.url || panel.image,
                              fileInfo: panel.fileInfo || {
                                size: 500000, // 默认500KB
                                format: 'image/jpeg',
                                type: 'image/jpeg',
                                width: 800,
                                height: 1200
                              }
                            };
                            
                            setOperationsPanel({
                              panel: enhancedPanel,
                              position
                            });
                          }}
                          onDelete={() => handleDeleteSourceImagePanel(panel.componentId)}
                          onReupload={() => handleReuploadSource(panel)}
                          onStatusChange={(newStatus) => handleSourceImageStatusChange(panel.componentId, newStatus)}
                          isActive={panel.status === 'completed'}
                          onPanelsChange={setSourceImagePanels}
                          pageType="bleach"
                        />
                      ))
                    )}

                    {/* 随机种子选择器 */}
                    <RandomSeedSelector
                      onRandomChange={setUseRandomSeed}
                      onSeedChange={setSeed}
                      defaultRandom={useRandomSeed}
                      defaultSeed={seed}
                      // 编辑模式下传递历史种子
                      isEdit={selectedImage !== null}
                      editSeed={selectedImage?.seed || null}
                    />

                    {/* 生成数量选择器 隐藏 */}
{/*                     
                    <QuantityPanel
                      imageQuantity={imageQuantity}
                      onChange={setImageQuantity}
                      min={1}
                      max={4}
                    /> */}
          </ControlPanel>

          <ResizeHandle
            ref={handleRef}
            containerRef={containerRef}
            onResize={setControlPanelWidth}
            minWidth={25}
            maxWidth={50}
          />

                  <GenerationArea
            ref={generationAreaRef}    setIsProcessing={setIsProcessing}

            activeTab={activeTab}
            onTabChange={setActiveTab}
            tasks={Array.isArray(generationTasks) ? generationTasks : []}
            onEditTask={handleEditTask}
            onViewDetails={handleViewDetails}
            pageType="bleach"
          />
        </div>

        {/* 上传指南模态框 */}
        {showUploadGuide && (
          <UploadGuideModal
            type={uploadGuideType}
            pageType="bleach"
            onClose={() => setShowUploadGuide(false)}
            onUpload={(result) => {
              console.log('收到上传结果:', result);
              // 根据结果中的shouldClose字段决定是否关闭弹窗
              if (result.shouldClose !== false) {
                setShowUploadGuide(false);
              }
            }}
          />
        )}

        {/* 原始图片上传指导弹窗 - 服装去色页面不再需要此功能 */}
        {/* 注释：服装去色页面不需要使用原始图片上传指导弹窗 */}

        {/* 添加操作弹窗 */}
        {operationsPanel && (
          <ImageInfoModal
            panel={operationsPanel.panel}
            position={operationsPanel.position}
            onClose={() => setOperationsPanel(null)}
            onDelete={handleDeleteSourceImagePanel}
            onReupload={handleReuploadSource}
            pageType="bleach"
          />
        )}

        {/* 添加 ImageDetailsModal 组件 - 使用懒加载 */}
        {showImageDetails && selectedImage ? (
          <MemoizedImageDetailsModal
            visible={showImageDetails}
            onClose={handleCloseImageDetails}
            selectedImage={selectedImage}
            generationTasks={generationTasks}
            onEditTask={handleEditTask}
            pageType="bleach"
          />
        ) : null}



        {/* 显示生成中的加载状态 */}
        {isProcessing && (
          <div className="generating-status">
            <Spin size="large" />
            <p>正在生成中，请稍候...</p>
          </div>
        )}


      </div>
    </RequireLogin>
  );
};

export default BleachPage; 