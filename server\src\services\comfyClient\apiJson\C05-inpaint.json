{"2": {"inputs": {"expand": 20, "tapered_corners": true, "mask": ["22", 2]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "3": {"inputs": {"positive": ["8", 0], "negative": ["6", 0], "vae": ["11", 2], "pixels": ["14", 0], "mask": ["16", 1]}, "class_type": "INPAINT_VAEEncodeInpaintConditioning", "_meta": {"title": "VAE Encode & Inpaint Conditioning"}}, "4": {"inputs": {"head": "fooocus_inpaint_head.pth", "patch": "inpaint_v26.fooocus.patch"}, "class_type": "INPAINT_LoadFooocusInpaint", "_meta": {"title": "Load Fooocus Inpaint"}}, "5": {"inputs": {"samples": ["13", 0], "vae": ["11", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "6": {"inputs": {"text": "Low image quality,low aesthetics,incorrect body structure,incorrect finger structure,low definition images", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["11", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"model": ["11", 0], "patch": ["4", 0], "latent": ["3", 2]}, "class_type": "INPAINT_ApplyFooocusInpaint", "_meta": {"title": "Apply Fooocus Inpaint"}}, "8": {"inputs": {"text": ["18", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["11", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "9": {"inputs": {"model_name": "MAT_Places512_G_fp16.safetensors"}, "class_type": "INPAINT_LoadInpaintModel", "_meta": {"title": "Load Inpaint Model"}}, "10": {"inputs": {"kernel": 30, "sigma": 30, "inpaint": ["5", 0], "original": ["22", 1], "mask": ["16", 1], "origin": ["16", 2]}, "class_type": "BlendInpaint", "_meta": {"title": "Blend Inpaint"}}, "11": {"inputs": {"ckpt_name": "dreamshaperXL_v21TurboDPMSDE.safetensors", "prompt": "[none]", "example": "[none]"}, "class_type": "CheckpointLoader|pysssss", "_meta": {"title": "Checkpoint Loader 🐍"}}, "12": {"inputs": {"model": "fancyfeast/llama-joycaption-beta-one-hf-llava", "quantization_mode": "nf4", "device": "cuda"}, "class_type": "LayerUtility: LoadJoyCaptionBeta1Model", "_meta": {"title": "LayerUtility: Load JoyCaption Beta One Model (Advance)"}}, "13": {"inputs": {"seed": 628194241558166, "needInput": true, "steps": 15, "cfg": 5, "sampler_name": "dpmpp_2m_sde_gpu", "scheduler": "karras", "denoise": 0.75, "model": ["7", 0], "positive": ["3", 0], "negative": ["3", 1], "latent_image": ["3", 3]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "随机种子"}}, "14": {"inputs": {"seed": 942380601871650, "inpaint_model": ["9", 0], "image": ["16", 0], "mask": ["16", 1]}, "class_type": "INPAINT_InpaintWithModel", "_meta": {"title": "Inpaint (using Model)"}}, "16": {"inputs": {"width": 2048, "height": 2048, "image": ["22", 1], "mask": ["2", 0]}, "class_type": "CutForInpaint", "_meta": {"title": "Cut For Inpaint"}}, "17": {"inputs": {"filename_prefix": "Inpaint", "images": ["23", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "18": {"inputs": {"caption_type": "Descriptive", "caption_length": "any", "max_new_tokens": 512, "top_p": 0.9, "top_k": 0, "temperature": 0.6, "user_prompt": "", "speak_and_recognation": {"__value__": [false, true]}, "image": ["14", 0], "joycaption_beta1_model": ["12", 0]}, "class_type": "LayerUtility: JoyCaptionBeta1", "_meta": {"title": "LayerUtility: JoyCaption Beta One (Advance)"}}, "21": {"inputs": {"url": "https://", "needInput": true, "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "上传图片并涂抹蒙版"}}, "22": {"inputs": {"context_expand_pixels": 20, "context_expand_factor": 1, "fill_mask_holes": false, "blur_mask_pixels": 16, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "ranged size", "force_width": 1024, "force_height": 1024, "rescale_factor": 1, "min_width": 1, "min_height": 1, "max_width": 1536, "max_height": 1536, "padding": 32, "image": ["21", 0], "mask": ["21", 1]}, "class_type": "InpaintCrop", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Crop node)"}}, "23": {"inputs": {"rescale_algorithm": "bislerp", "stitch": ["22", 0], "inpainted_image": ["5", 0]}, "class_type": "InpaintStitch", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Stitch node)"}}}