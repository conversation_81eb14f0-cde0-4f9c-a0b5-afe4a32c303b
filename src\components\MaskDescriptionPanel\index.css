/* 导入统一样式 */
@import '../../styles/theme.css';
@import '../../styles/buttons.css';
@import '../../styles/inputs.css';

/* 复用面板组件的通用样式 */
.panel-component {
  margin-bottom: 10px;
}

/* 蒙版描述面板 */
.mask-description-panel {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  min-height: 88px;
  display: flex;
  margin-bottom: 10px;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  position: relative;
}

.mask-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: stretch;
}

.mask-label {
  width: 88px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--border-light);
  margin: 0;
  padding: 0;
  background: var(--bg-secondary);
  flex-shrink: 0;
}

.mask-label span {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-primary);
}

.mask-area {
  flex: 1;
  padding: 0 16px;
  padding-right: 66px; /* 为右上角的提示按钮留出空间 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.mask-description-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  padding: 12px 0;
}

/* 标签按钮样式 */
.tags-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.tag-option {
  padding: 4px 10px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-normal);
  outline: none;
}

.tag-option:hover {
  background: var(--bg-hover);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.tag-option.active {
  background: var(--brand-primary-light);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

/* 输入框样式 */
.input-container {
  position: relative;
  width: 100%;
}

.mask-description-input {
  width: 100%;
  padding: 6px 10px;
  padding-right: 30px; /* 为清除按钮留出空间 */
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: var(--transition-normal);
}

.mask-description-input:hover {
  border-color: var(--brand-primary);
}

.mask-description-input:focus {
  outline: none;
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 2px var(--brand-primary-light);
}

.mask-description-input::placeholder {
  color: var(--text-tertiary);
}

/* 清除按钮样式 */
.clear-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition-normal);
}

.clear-button:hover {
  background: var(--bg-hover);
  color: var(--text-secondary);
}

.clear-button:active {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

/* 适配暗色主题 */
[data-theme="dark"] .tag-option {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

[data-theme="dark"] .tag-option:hover {
  background: var(--bg-hover);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

[data-theme="dark"] .tag-option.active {
  background: rgba(255, 60, 106, 0.15);
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

/* 更多按钮样式 */
.more-btn {
  width: 28px;
  height: 28px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-normal);
}

.more-btn:hover {
  border-color: var(--brand-primary);
  color: var(--brand-primary);
}

.more-btn span {
  display: block;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: var(--text-secondary);
  position: relative;
  transition: var(--transition-normal);
}

.more-btn span::before,
.more-btn span::after {
  content: '';
  position: absolute;
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: var(--text-secondary);
  transition: var(--transition-normal);
}

.more-btn:hover span,
.more-btn:hover span::before,
.more-btn:hover span::after {
  background: var(--brand-primary);
}

.more-btn span::before {
  left: -5px;
}

.more-btn span::after {
  right: -5px;
}

/* 收起图标样式 */
.more-btn .collapse-icon {
  width: auto !important;
  height: auto !important;
  background: none !important;
  font-size: 18px;
  font-weight: bold;
  color: var(--text-secondary);
  line-height: 1;
}

.more-btn:hover .collapse-icon {
  color: var(--brand-primary);
}

/* 在展开状态下隐藏三个点的伪元素 */
.more-btn.expanded span::before,
.more-btn.expanded span::after {
  display: none !important;
} 