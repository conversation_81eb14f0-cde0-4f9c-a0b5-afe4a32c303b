import React, { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { 
  Table, Button, Modal, Form, Input, Select, DatePicker, 
  message, Card, Space, Upload, Switch, InputNumber 
} from 'antd';
import { 
  PlusOutlined, DeleteOutlined, EditOutlined, 
  EyeOutlined, UploadOutlined 
} from '@ant-design/icons';
import moment from 'moment';
import api from '../../../api';
import './AnnouncementManagement.css';
import { uploadFiles } from '../../../api/ossUpload';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

// 修改 Context 的值
const AnnouncementContext = createContext({
  form: null,
  imageList: [],
  setImageList: () => {},
  handleUpload: () => {},
  handlePreview: () => {},
  editingAnnouncement: null
});

// 修改表单组件
const AnnouncementForm = () => {
  const { 
    form, 
    imageList, 
    setImageList, 
    handleUpload, 
    handlePreview,
    editingAnnouncement 
  } = useContext(AnnouncementContext);

  // 处理表单初始值
  useEffect(() => {
    if (editingAnnouncement) {
      form.setFieldsValue({
        title: editingAnnouncement.title,
        content: editingAnnouncement.content,
        priority: editingAnnouncement.priority || 0,
        status: editingAnnouncement.status === 'active',
        showMask: typeof editingAnnouncement.showMask === 'boolean' ? editingAnnouncement.showMask : true,
        showTitle: typeof editingAnnouncement.showTitle === 'boolean' ? editingAnnouncement.showTitle : true,
        showSubtitle: typeof editingAnnouncement.showSubtitle === 'boolean' ? editingAnnouncement.showSubtitle : true
      });
    } else {
      // 新建公告时重置表单为默认值
      form.setFieldsValue({
        priority: 0,
        status: true,
        showMask: true,
        showTitle: true,
        showSubtitle: true
      });
    }
  }, [editingAnnouncement, form]);

  return (
    <Form
      form={form}
      layout="vertical"
      preserve={false}
      initialValues={{
        priority: 0,
        status: true,
        showMask: true,
        showTitle: true,
        showSubtitle: true
      }}
    >
      <Form.Item
        name="title"
        label="标题"
        rules={[{ required: true, message: '请输入公告标题' }]}
      >
        <Input placeholder="请输入公告标题" />
      </Form.Item>
      <Form.Item
        name="content"
        label="副标题"
        rules={[{ required: true, message: '请输入副标题' }]}
      >
        <TextArea rows={4} placeholder="请输入副标题" />
      </Form.Item> 

      <Form.Item
        label="图片"
        extra="最多上传5张图片"
      >
        <Upload
          listType="picture-card"
          customRequest={handleUpload}
          fileList={imageList}
          onChange={({ fileList }) => setImageList(fileList)}
          onPreview={handlePreview}
          maxCount={5}
        >
          {imageList.length >= 5 ? null : (
            <div>
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>上传</div>
            </div>
          )}
        </Upload>
      </Form.Item>

      <Form.Item
        name="priority"
        label="优先级"
      >
        <InputNumber min={0} max={100} />
      </Form.Item>

      <Form.Item
        name="status"
        label="状态"
        valuePropName="checked"
      >
        <Switch checkedChildren="启用" unCheckedChildren="禁用" />
      </Form.Item>

      <Form.Item
        name="showMask"
        label="图片遮罩"
        valuePropName="checked"
        initialValue={true}
      >
        <Switch checkedChildren="开启" unCheckedChildren="关闭" />
      </Form.Item>

      <Form.Item
        name="showTitle"
        label="显示标题"
        valuePropName="checked"
        initialValue={true}
      >
        <Switch checkedChildren="显示" unCheckedChildren="隐藏" />
      </Form.Item>

      <Form.Item
        name="showSubtitle"
        label="显示副标题"
        valuePropName="checked"
        initialValue={true}
      >
        <Switch checkedChildren="显示" unCheckedChildren="隐藏" />
      </Form.Item>
    </Form>
  );
};

const AnnouncementManagement = () => {
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState(null);
  const [imageList, setImageList] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [queryParams, setQueryParams] = useState({
    page: 1,
    limit: 10
  });

  // 处理编辑公告
  const handleEdit = useCallback((record) => {
    // 先设置编辑状态，这会触发表单组件的 useEffect
    setEditingAnnouncement(record);
    
    // 处理图片列表
    const processedImageList = record.images?.map((image, index) => ({
      uid: `-${index}`,
      name: image.title || `图片${index + 1}`,
      status: 'done',
      url: typeof image === 'string' ? image : image.url,
      thumbUrl: typeof image === 'string' ? image : image.url
    })) || [];
    
    setImageList(processedImageList);
    setModalVisible(true);
  }, []);

  // 处理添加公告
  const handleAdd = () => {
    setEditingAnnouncement(null);
    setImageList([]);
    setModalVisible(true);
    // 表单会在useEffect中自动设置默认值
  };

  // 处理保存公告
  const handleSave = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      const processedImages = imageList.map(file => ({
        url: file.response?.url || file.url,
        title: file.name || '图片',
        description: ''
      }));

      const data = {
        ...values,
        images: processedImages,
        showMask: typeof values.showMask === 'boolean' ? values.showMask : true,
        showTitle: typeof values.showTitle === 'boolean' ? values.showTitle : true,
        showSubtitle: typeof values.showSubtitle === 'boolean' ? values.showSubtitle : true
      };

      if (editingAnnouncement) {
        await api.put(`/admin/announcements/${editingAnnouncement._id}`, data);
        message.success('公告更新成功');
      } else {
        await api.post('/admin/announcements', data);
        message.success('公告创建成功');
      }

      setModalVisible(false);
      form.resetFields();
      setImageList([]);
      setEditingAnnouncement(null);
      fetchAnnouncements(queryParams);

    } catch (error) {
      message.error('保存公告失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 获取公告列表
  const fetchAnnouncements = async (params = queryParams) => {
    setLoading(true);
    try {
      const response = await api.get('/admin/announcements', { params });
      setAnnouncements(response.data || []);
    } catch (error) {
      message.error('获取公告列表失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  // 处理图片上传
  const handleUpload = async ({ file, onSuccess, onError }) => {
    try {
      const { urls } = await uploadFiles([file]);
      // 构造符合 Upload 组件要求的文件对象
      const fileObj = {
        uid: file.uid,
        name: file.name,
        status: 'done',
        url: urls[0],
        thumbUrl: urls[0]
      };
      onSuccess(fileObj);
    } catch (error) {
      message.error('上传图片失败');
      onError(error);
    }
  };

  // 处理取消编辑/添加
  const handleCancel = () => {
    setModalVisible(false);
    form.resetFields();
    setImageList([]);
    setEditingAnnouncement(null);
  };

  // 处理删除公告
  const handleDelete = (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条公告吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          await api.delete(`/admin/announcements/${id}`);
          message.success('公告删除成功');
          fetchAnnouncements();
        } catch (error) {
          message.error('删除公告失败');
          console.error(error);
        }
      }
    });
  };

  // 处理预览图片
  const handlePreview = (file) => {
    setPreviewImage(file.url || file.thumbUrl);
    setPreviewVisible(true);
  };

  // 处理查询
  const handleSearch = async () => {
    const values = await searchForm.validateFields();
    const params = {
      ...queryParams,
      ...values,
      dateRange: values.dateRange ? [
        values.dateRange[0].format('YYYY-MM-DD HH:mm:ss'),
        values.dateRange[1].format('YYYY-MM-DD HH:mm:ss')
      ] : undefined
    };
    setQueryParams(params);
    fetchAnnouncements(params);
  };

  // 重置查询
  const handleReset = () => {
    searchForm.resetFields();
    const params = {
      page: 1,
      limit: 10
    };
    setQueryParams(params);
    fetchAnnouncements(params);
  };

  useEffect(() => {
    if (modalVisible) {
      console.log('Form values:', form.getFieldsValue());
      console.log('Editing announcement:', editingAnnouncement);
      console.log('Image list:', imageList);
    }
  }, [modalVisible, form, editingAnnouncement, imageList]);

  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
    },
    // {
    //   title: '类型',
    //   dataIndex: 'type',
    //   key: 'type',
    //   render: (type) => {
    //     const typeMap = {
    //       info: '信息',
    //       warning: '警告',
    //       success: '成功',
    //       error: '错误'
    //     };
    //     return typeMap[type] || type;
    //   }
    // },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => status === 'active' ? '启用' : '禁用'
    },
    // {
    //   title: '显示时间',
    //   key: 'dateRange',
    //   render: (_, record) => (
    //     <>
    //       {moment(record.startTime).format('YYYY-MM-DD HH:mm')}
    //       <br />
    //       至
    //       <br />
    //       {moment(record.endTime).format('YYYY-MM-DD HH:mm')}
    //     </>
    //   )
    // },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record._id)}
          >
            删除
          </Button>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handlePreview(record)}
          >
            预览
          </Button>
        </Space>
      )
    }
  ];

  return (
    <AnnouncementContext.Provider value={{
      form,
      imageList,
      setImageList,
      handleUpload,
      handlePreview,
      editingAnnouncement
    }}>
      <div className="announcement-management">
        <Card>
          {/* 搜索表单 */}
          <Form
            form={searchForm}
            layout="inline"
            style={{ marginBottom: 24 }}
          >
            <Form.Item name="title">
              <Input placeholder="公告标题" allowClear />
            </Form.Item>
            
            {/* <Form.Item name="type">
              <Select placeholder="公告类型" allowClear style={{ width: 120 }}>
                <Option value="info">信息</Option>
                <Option value="warning">警告</Option>
                <Option value="success">成功</Option>
                <Option value="error">错误</Option>
              </Select>
            </Form.Item> */}
            
            <Form.Item name="status">
              <Select placeholder="状态" allowClear style={{ width: 120 }}>
                <Option value="active">启用</Option>
                <Option value="inactive">禁用</Option>
              </Select>
            </Form.Item>
            
            {/* <Form.Item name="dateRange">
              <RangePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item> */}
            
            <Form.Item>
              <Space>
                <Button type="primary" onClick={handleSearch}>
                  查询
                </Button>
                <Button onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>

          <div className="table-header">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加公告
            </Button>
          </div>

          <Table
            columns={columns}
            dataSource={announcements}
            rowKey="_id"
            loading={loading}
            pagination={{
              total: announcements.length,
              pageSize: queryParams.limit,
              current: queryParams.page,
              onChange: (page) => {
                setQueryParams({ ...queryParams, page });
                fetchAnnouncements({ ...queryParams, page });
              }
            }}
          />
        </Card>

        <Modal
          title={editingAnnouncement ? '编辑公告' : '添加公告'}
          open={modalVisible}
          onOk={handleSave}
          onCancel={handleCancel}
          maskClosable={false}
          destroyOnClose={true}
          width={800}
          confirmLoading={loading}
          okText="保存"
          cancelText="取消"
        >
          <AnnouncementForm />
        </Modal>

        <Modal
          open={previewVisible}
          footer={null}
          onCancel={() => setPreviewVisible(false)}
          destroyOnClose
        >
          <img alt="预览" style={{ width: '100%' }} src={previewImage} />
        </Modal>
      </div>
    </AnnouncementContext.Provider>
  );
};

export default AnnouncementManagement; 