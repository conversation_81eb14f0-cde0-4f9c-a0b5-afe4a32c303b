import React, { useState, useEffect, useRef, useCallback } from 'react';
import PropTypes from 'prop-types';
import { MdOutlineHelpOutline } from 'react-icons/md';
import './index.css';
import TipPopup from '../TipPopup';

// 标签与实际传递给后端内容的映射关系
export const tagMapping = {
  '分体泳衣': 'bra, panties',
  '连体泳衣': 'swimsuit',
  '裙式泳衣': 'bra, skirt',
  '套装': 'clothes',
  '连衣裙款式': 'dress',
  '下装裙子': 'skirt',
  '系绳': 'ties',
  '花朵配饰': 'flowers',
  '贝壳配饰': 'shell',
  '海星配饰': 'stars'
};

/**
 * 蒙版描述组件 - 用于向后端传递蒙版的描述词文本
 * 
 * 此组件包含标签按钮和文本输入框，用于向comfyUI工作流中的特定节点传递文本
 */
const MaskDescriptionPanel = ({
  panel,
  onPanelsChange,
  description,
  onChange,
  placeholder = '请输入对蒙版区域的优化描述，例如"白色蕾丝花边"'
}) => {
  // 默认关键词标签选项
  const defaultTagOptions = ['分体泳衣', '连体泳衣', '裙式泳衣'];
  
  // 更多标签选项
  const moreTagOptions = ['套装', '连衣裙款式', '下装裙子', '系绳', '花朵配饰', '贝壳配饰', '海星配饰'];
  
  // 本地状态，用于存储选中的标签和输入的文本
  const [selectedTags, setSelectedTags] = useState(panel?.selectedTags || []);
  const [customText, setCustomText] = useState(panel?.customText || '');
  
  // 提示弹窗相关状态
  const [isTipVisible, setIsTipVisible] = useState(false);
  const [tipPosition, setTipPosition] = useState({ left: 0, top: 0 });
  const tipButtonRef = useRef(null);
  
  // 更多按钮相关状态
  const [showMoreTags, setShowMoreTags] = useState(false);
  const moreButtonRef = useRef(null);
  
  // 监听panel属性变化，同步更新内部状态
  useEffect(() => {
    // 当panel属性变化时，同步更新内部状态
    if (panel) {
      setSelectedTags(panel.selectedTags || []);
      setCustomText(panel.customText || '');
      console.log('MaskDescriptionPanel状态已同步：', panel);
    }
  }, [panel]);
  
  // 当外部description变化时，更新本地状态
  useEffect(() => {
    if (description) {
      setCustomText(description);
    }
  }, [description]);
  
  // 处理标签点击 - 支持多选
  const handleTagClick = (tag) => {
    let newSelectedTags;
    
    if (selectedTags.includes(tag)) {
      // 如果标签已选中，则移除
      newSelectedTags = selectedTags.filter(t => t !== tag);
    } else {
      // 如果标签未选中，则添加
      newSelectedTags = [...selectedTags, tag];
    }
    
    setSelectedTags(newSelectedTags);
    
    // 生成映射后的内容
    const mappedValues = newSelectedTags.map(tag => tagMapping[tag]).filter(Boolean);
    const mappedText = mappedValues.join(', ');
    
    // 更新输入框内容
    setCustomText(mappedText);
    
    // 更新面板数据
    if (onPanelsChange) {
      onPanelsChange({
        ...panel,
        selectedTags: newSelectedTags,
        customText: mappedText,
        description: mappedText
      });
    }
    
    // 通知父组件文本变化
    if (onChange) {
      onChange(mappedText);
    }
  };
  
  // 处理输入框点击 - 不清空已选标签
  const handleInputClick = () => {
    // 不再清空已选标签，保持多选状态
  };
  
  // 处理文本输入变化
  const handleTextChange = (e) => {
    const newText = e.target.value;
    setCustomText(newText);
    
    // 更新面板数据
    if (onPanelsChange) {
      onPanelsChange({
        ...panel,
        selectedTags: selectedTags,
        customText: newText,
        description: newText
      });
    }
    
    // 通知父组件文本变化
    if (onChange) {
      onChange(newText);
    }
  };

  // 处理提示按钮点击
  const handleShowTip = () => {
    if (tipButtonRef.current) {
      const rect = tipButtonRef.current.getBoundingClientRect();
      setTipPosition({
        left: rect.left + rect.width + 28,
        top: rect.top - 8
      });
    }
    setIsTipVisible(true);
  };

  // 处理关闭提示
  const handleCloseTip = () => {
    setIsTipVisible(false);
  };

  // 处理更多按钮点击
  const handleMoreClick = (e) => {
    e.stopPropagation();
    setShowMoreTags(!showMoreTags);
  };

  // 处理清除按钮点击
  const handleClearText = () => {
    setCustomText('');
    setSelectedTags([]);
    
    // 更新面板数据
    if (onPanelsChange) {
      onPanelsChange({
        ...panel,
        selectedTags: [],
        customText: '',
        description: ''
      });
    }
    
    // 通知父组件文本变化
    if (onChange) {
      onChange('');
    }
  };

  return (
    <>
      <div className="mask-description-panel">
        <div className="mask-content">
          <div className="mask-label">
            <span>款式描述</span>
          </div>
          <div className="mask-area">
            <div className="mask-description-content">
              <div className="tags-options">
                {defaultTagOptions.map((tag) => (
                  <button 
                    key={tag}
                    className={`tag-option ${selectedTags.includes(tag) ? 'active' : ''}`}
                    onClick={() => handleTagClick(tag)}
                  >
                    {tag}
                  </button>
                ))}
                
                {/* 更多标签 */}
                {showMoreTags && (
                  <>
                    {moreTagOptions.map((tag) => (
                      <button 
                        key={tag}
                        className={`tag-option ${selectedTags.includes(tag) ? 'active' : ''}`}
                        onClick={() => handleTagClick(tag)}
                      >
                        {tag}
                      </button>
                    ))}
                  </>
                )}
                
                {/* 更多按钮 */}
                <button 
                  className={`more-btn ${showMoreTags ? 'expanded' : ''}`}
                  onClick={handleMoreClick}
                  title={showMoreTags ? "收起标签" : "更多标签"}
                >
                  {showMoreTags ? (
                    <span className="collapse-icon">×</span>
                  ) : (
                    <span></span>
                  )}
                </button>
              </div>
              
              <div className="input-container">
                <input
                  type="text"
                  value={customText}
                  onChange={handleTextChange}
                  onClick={handleInputClick}
                  placeholder="请选择上方标签或输入自定义描述"
                  className="mask-description-input"
                />
                {customText && (
                  <button 
                    className="clear-button"
                    onClick={handleClearText}
                    title="清除文本"
                  >
                    ×
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* 提示按钮 */}
        <button 
          className="tip-button-common text-description-tip-button"
          onClick={handleShowTip}
          ref={tipButtonRef}
          title="查看使用提示"
        >
          <span className="tip-text">点我</span>
          <MdOutlineHelpOutline />
        </button>
      </div>
      
      {/* 提示弹窗 */}
      <TipPopup 
        type="mask-description"
        position={tipPosition}
        isVisible={isTipVisible}
        onClose={handleCloseTip}
        content={[
          "• 务必填写正确，否则影响生成效果",
          "• 建议使用简洁的英文或中文短语，用逗号分隔",
          "• 可以选择标签上的款式，也可以自己填写款式描述",
          "• 支持多选标签，选中内容会自动填入输入框",
        ].join('\n')}
      />
    </>
  );
};

MaskDescriptionPanel.propTypes = {
  panel: PropTypes.shape({
    componentId: PropTypes.string.isRequired,
    selectedTags: PropTypes.array,
    customText: PropTypes.string,
    description: PropTypes.string
  }),
  onPanelsChange: PropTypes.func,
  description: PropTypes.string,
  onChange: PropTypes.func,
  placeholder: PropTypes.string
};

export default MaskDescriptionPanel; 