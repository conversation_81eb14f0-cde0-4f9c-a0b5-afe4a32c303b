const { format } = require('date-fns');
const ComfyClient = require('./client');
const instanceService = require('../instance/instanceService');
const { createError } = require('../../utils/error');
const Instance = require('../../models/Instance');
const MongoDBCache = require('../../utils/cacheUtils');
const cache = new MongoDBCache({
  collectionName: 'instance_status_cache'
});
class ComfyClientFactory {

  
  /**
   * 创建ComfyClient实例
   * @param {string} userId 用户ID（可选）
   * @param {string} preferredInstanceId 优先使用的实例ID（可选，用于用户实例连续性）
   * @returns {Promise<ComfyClient>} ComfyClient实例
   */
  static async createClient(userId = null, preferredInstanceId = null) {
    let instance = null
    try {
      // 获取可用实例，优先选择用户之前使用过的实例，支持指定优先实例ID
      instance = await instanceService.getAvailableInstance(userId, false, 0, preferredInstanceId);

      // 打印找到的可用实例信息
      console.log("找到的可用实例信息：", JSON.stringify(instance));
      
      if (!instance) {
        throw createError(503, '没有可用的AI实例');
      }
      
      await this.inUse(instance.instanceId);
      const isOpenNew = await cache.get("open_starting_"+instance.instanceId)
      if (isOpenNew){
        await cache.del("open_starting_"+instance.instanceId)
      }

      // 创建ComfyClient实例
      const client = new ComfyClient(
        instance.url,
        instance.instanceId
      );
      await client.initializeWithRetry()


      // 添加实例信息到客户端
      client.instanceInfo = {
        id: instance.instanceId,
        name: instance.name,
        type: instance.type
      };

      return client;
    } catch (error) {
      if (instance!==null) {
        console.log(`ComfyUI实例 ${instance.instanceId} 初始化失败，清理缓存标志...`);

        // 删除启动标志缓存
        await cache.del("open_starting_"+instance.instanceId);
        await cache.del("instance_in_use_"+instance.instanceId);

        // 删除正在使用中的缓存标志
        try {
          await this.finishUse(instance.instanceId);
          console.log(`成功清理实例 ${instance.instanceId} 的使用中缓存`);
        } catch (cacheError) {
          console.error(`清理实例 ${instance.instanceId} 使用中缓存失败:`, cacheError);
        }
      }
      throw createError(error.status || 500, `创建ComfyClient失败: ${error.message}`);
    }
  }
  

  static async inUse(instanceId) {
    // 获取数量加1
    const count = await cache.get("instance_in_use_" +instanceId);
    if (count) {
      await cache.set("instance_in_use_" +instanceId, count + 1, -1);
    }else{
      await cache.set("instance_in_use_" +instanceId, 1, -1);
    }
    if (await cache.get("instance_finished_" +instanceId)) {
      await cache.del("instance_finished_" +instanceId);
    }
  }
  static async finishUse(instanceId) {
    const cacheB = new MongoDBCache({
      collectionName: 'instance_status_cache',
    });
    // 获取数量减1,如果数量为0,则删除实例
    const count = await cacheB.get("instance_in_use_" +instanceId);
    if (count>1) {
      await cacheB.set("instance_in_use_" +instanceId, count - 1, -1);
    }
    if (count == 1) {
      await cacheB.set("instance_finished_" +instanceId,format(new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }), 'yyyy-MM-dd HH:mm:ss'), -1);
      await cacheB.del("instance_in_use_" + instanceId);
    }
  }

  /**
   * 获取指定实例的ComfyClient
   * @param {string} instanceId 实例ID
   * @param {string} userId 用户ID
   */
  static async createClientForInstance(instanceId, userId) {
    try {
      // 检查实例状态
      const status = await instanceService.getInstanceStatus(instanceId);
      
      if (status !== 200 && status !== 300) {
        throw createError(503, '指定的实例不可用');
      }

      const instance = await Instance.findOne({ instanceId });
      if (!instance) {
        throw createError(404, '未找到指定的实例');
      }


      return new ComfyClient(
        instance.url,
        userId,
        {
          apiKey: instance.apiKey,
          instanceId: instance.instanceId
        }
      );
    } catch (error) {
      throw createError(error.status || 500, `创建指定实例的ComfyClient失败: ${error.message}`);
    }
  }

  /**
   * 根据实例ID获取ComfyClient实例
   * @param {string} instanceId 实例ID
   * @returns {Promise<ComfyClient>} ComfyClient实例
   */
  static async getClient(instanceId,url) {
    try {
      
      const client = new ComfyClient(
        url,
        instanceId
      );

      // 添加实例信息到客户端
      client.instanceInfo = {
        id: instanceId,
        name: instanceId,
        type: 'instance'
      };

      return client;
    } catch (error) {
      throw createError(error.status || 500, `获取ComfyClient实例失败: ${error.message}`);
    }
  }
}

module.exports = ComfyClientFactory; 