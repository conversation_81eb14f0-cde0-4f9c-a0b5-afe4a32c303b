import { <PERSON><PERSON>, <PERSON>, message } from 'antd';
import 'antd/dist/reset.css';
import Masonry from 'masonry-layout';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { getCurrentUserId } from '../../../api';
import { executeFlow } from '../../../api/flow';
import { checkUserBalance, createFlowTask, updateFlowTask } from '../../../api/flowtask';
import { uploadFiles } from '../../../api/ossUpload';
import { processMask } from '../../../api/pyApi';
import {
  deleteTask
} from '../../../api/task';
import ClothingMaskPanel from '../../../components/ClothingMaskPanel';
import ClothingPanel from '../../../components/ClothingPanel';
import ControlPanel from '../../../components/ControlPanel';
import GenerationArea from '../../../components/GenerationArea';
import ImageDetailsModal from '../../../components/ImageDetailsModal';
import ImageInfoModal from '../../../components/ImageInfoModal';
import MaskDescriptionPanel, { tagMapping } from '../../../components/MaskDescriptionPanel';
import MaskDrawModal from '../../../components/MaskDrawModal';
import MaskExpander from '../../../components/MaskExpander';
import ModelMaskPanel from '../../../components/ModelMaskPanel';
import PromptIfUnsaved from '../../../components/PromptIfUnsaved';
import QuantityPanel from '../../../components/QuantityPanel';
import RandomSeedSelector from '../../../components/RandomSeedSelector';
import RequireLogin from '../../../components/RequireLogin';
import ResizeHandle from '../../../components/ResizeHandle';
import TaskPanel from '../../../components/TaskPanel';
import TipsPanel from '../../../components/TipsPanel';
import UploadBox from '../../../components/UploadBox';
import UploadBox_Model from '../../../components/UploadBox_Model';
import UploadGuideModal from '../../../components/UploadGuideModal';
import { useTaskContext } from '../../../contexts/TaskContext';
import { WORKFLOW_NAME } from '../../../data/workflowName';
import { handleBatchDownload as downloadHelper } from '../../../utils/downloadHelper';
import { ID_TYPES, generateId } from '../../../utils/idGenerator';
import { showDeleteConfirmModal } from '../../../utils/modalUtils';
import './index.css';

const TRY_ON_TIP = (
  <>若更换的服装比模特原本衣服的覆盖范围更大，<span style={{ color: 'var(--brand-primary)' }}>需手动绘制蒙版</span>。(点击模特原图右侧按钮)</>
);
const MASK_SAVED_TIP = "请确保蒙版区域足够大，以容纳新服装的生成。";



const TryOnPage = ({ isLoggedIn, userId }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const containerRef = useRef(null);
  const controlPanelRef = useRef(null);
  const handleRef = useRef(null);
  const [activeTab, setActiveTab] = useState('result');
  const [controlPanelTab, setControlPanelTab] = useState('front');
  const [controlPanelWidth, setControlPanelWidth] = useState(28);
  const showcaseRef = useRef(null);
  const generationAreaRef = useRef(null);
  const masonryRef = useRef(null);
  const [showUploadGuide, setShowUploadGuide] = useState(false);
  const [processedImages, setProcessedImages] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [clothingPanels, setClothingPanels] = useState([]);
  const [modelPanels, setModelPanels] = useState([]);
  // 为第二个标签页创建独立的状态
  const [otherClothingPanels, setOtherClothingPanels] = useState([]);
  const [otherModelPanels, setOtherModelPanels] = useState([]);
  const [currentReuploadModelPanelId, setCurrentReuploadModelPanelId] = useState(null);
  const [currentReuploadClothingPanelId, setCurrentReuploadClothingPanelId] = useState(null);
  const [showModelUploadGuide, setShowModelUploadGuide] = useState(false);
  const [operationsPanel, setOperationsPanel] = useState(null);
  const [imageQuantity, setImageQuantity] = useState(2);
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageDetails, setShowImageDetails] = useState(false);
  const [imageDetailsTask, setImageDetailsTask] = useState(null);
  const [showAdvancedText, setShowAdvancedText] = useState(false);

  const [advancedPopupPosition, setAdvancedPopupPosition] = useState({ top: 0, left: 0 });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const { updateTask } = useTaskContext();

  // 页面刷新/关闭提示
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）';
        return e.returnValue;
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  // 添加拖动状态管理
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const lastPosition = useRef({ x: 0, y: 0 });

  // 添加图片缩放相关状态
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);
  const imageRef = useRef(null);

  // 添加随机种子相关状态
  const [useRandomSeed, setUseRandomSeed] = useState(true);
  const [seed, setSeed] = useState(Math.floor(Math.random() * Number.MAX_SAFE_INTEGER));

  // 添加蒙版扩张组件状态
  const [maskExpandValue, setMaskExpandValue] = useState(50);

  // 添加状态控制提示组件的显示和提示内容
  const [showTips, setShowTips] = useState(false);
  const [tipContent, setTipContent] = useState(TRY_ON_TIP);

  // 添加加载状态
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);

  // 任务列表状态
  const [generationTasks, setGenerationTasks] = useState([]);

  // 初始化和清理Masonry布局
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      // 销毁已存在的实例
      if (masonryRef.current) {
        masonryRef.current.destroy();
      }

      // 创建新实例
      masonryRef.current = new Masonry(showcaseRef.current, {
        itemSelector: '.showcase-item',
        columnWidth: '.showcase-item',
        percentPosition: true,
        transitionDuration: '0.3s',
        initLayout: true,
        gutter: 16,
        fitWidth: false,
        horizontalOrder: true
      });

      // 监听窗口大小变化
      const handleResize = () => {
        if (masonryRef.current) {
          setTimeout(() => {
            masonryRef.current.layout();
          }, 100);
        }
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (masonryRef.current) {
          masonryRef.current.destroy();
          masonryRef.current = null;
        }
      };
    }
  }, [activeTab]);

  // 监听图片加载
  useEffect(() => {
    if (activeTab === 'showcase' && showcaseRef.current) {
      const images = showcaseRef.current.getElementsByTagName('img');
      let loadedCount = 0;

      const handleImageLoad = () => {
        loadedCount++;
        if (masonryRef.current) {
          masonryRef.current.layout();
        }
      };

      Array.from(images).forEach(img => {
        if (img.complete) {
          handleImageLoad();
        } else {
          img.addEventListener('load', handleImageLoad);
        }
      });

      return () => {
        Array.from(images).forEach(img => {
          img.removeEventListener('load', handleImageLoad);
        });
      };
    }
  }, [activeTab]);

  // 处理文件上传
  const handleFileUpload = (file) => {
    setShowUploadGuide(false);
    setHasUnsavedChanges(true);
  };

  const handleUploadResult = (results) => {
    console.log('服装上传结果处理:', results);
    setHasUnsavedChanges(true);

    if (results.type === 'panels') {
      const panelsWithType = results.panels.map(panel => ({
        ...panel,
        type: 'clothing',
        source: panel.source || 'upload',
        status: 'completed'
      }));
      setClothingPanels(prevPanels => [...prevPanels, ...panelsWithType]);
    } else if (results.type === 'update') {
      // 处理单个面板更新的情况
      console.log(`更新面板 ${results.panelId} 的状态:`, results.data);

      setClothingPanels(prevPanels =>
        prevPanels.map(panel => {
          if (panel.componentId === results.panelId) {
            // 返回更新后的面板数据
            return {
              ...panel,
              ...results.data,
              source: panel.source || 'upload', // 保持source标记
              status: 'completed',  // 更新状态为已完成
              error: null
            };
          }

          // 不是要更新的面板，保持不变
          return panel;
        })
      );
    } else if (results.type === 'error') {
      console.error('上传错误:', results.error);
      message.error('上传失败: ' + results.error);
      // 移除处理中的面板
      setClothingPanels(prevPanels =>
        prevPanels.filter(panel => panel.status !== 'processing'));
    }
  };

  // 处理模特原图上传结果
  const handleModelUploadResult = (results) => {
    console.log('处理模特上传结果:', JSON.stringify(results, null, 2));
    setHasUnsavedChanges(true);

    try {
      if (results.type === 'panels') {
        if (currentReuploadModelPanelId) {
          // 如果是重新上传，替换原有面板
          setModelPanels(prevPanels =>
            prevPanels.map(panel =>
              panel.componentId === currentReuploadModelPanelId
                ? { ...results.panels[0], componentId: currentReuploadModelPanelId, type: 'model', source: 'upload' }
                : panel
            )
          );
          // 重置当前重新上传的面板ID
          setCurrentReuploadModelPanelId(null);
        } else {
          // 如果是新上传，添加新面板
          const panelsWithType = results.panels.map(panel => ({
            ...panel,
            // 确保使用componentId作为标准ID
            componentId: panel.componentId || generateId(ID_TYPES.COMPONENT),
            type: 'model',
            source: panel.source || 'upload' // 添加source属性标记为用户上传
          }));
          setModelPanels(prevPanels => [...prevPanels, ...panelsWithType]);
        }

        // 显示提示，在模特原图上传成功后显示
        setShowTips(true);
        setTipContent(TRY_ON_TIP);
      } else if (results.type === 'update') {
        // 处理面板更新 - 特别是服务器文件名的更新
        console.log('更新模特面板:', results);
        setModelPanels(prevPanels =>
          prevPanels.map(panel => {
            if (panel.componentId === results.panelId) {
              // 更新面板，确保serverFileName和processInfo被正确保存
              return {
                ...panel,
                ...results.data,
                // 确保不丢失原有属性
                type: 'model',
                source: panel.source || 'upload', // 保持source标记
                // 确保processInfo被正确合并
                processInfo: {
                  ...(panel.processInfo || {}),
                  ...(results.data.processInfo || {})
                }
              };
            }
            return panel;
          })
        );
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        // 移除处理中的面板
        setModelPanels(prevPanels =>
          prevPanels.filter(panel => panel.status !== 'processing'));
        // 重置当前重新上传的面板ID
        setCurrentReuploadModelPanelId(null);
      }
    } catch (error) {
      console.error('处理模特上传结果时出错:', error);
      message.error('处理模特上传结果时出错: ' + (error.message || '未知错误'));
    }
  };

  // 处理模特原图上传
  const handleModelFileUpload = (file) => {
    console.log('模特原图上传:', file);
  };

  // 第二个标签页专用的服装上传处理函数
  const handleOtherClothingUploadResult = async (results) => {
    try {
      if (results.type === 'panels') {
        let newPanel;

        if (currentReuploadClothingPanelId) {
          const newComponentId = generateId(ID_TYPES.COMPONENT);
          setOtherClothingPanels(prevPanels =>
            prevPanels.map(panel =>
              panel.componentId === currentReuploadClothingPanelId
                ? {
                  ...results.panels[0],
                  componentId: newComponentId,
                  type: 'clothing',
                  source: 'upload',
                  file: results.panels[0].file
                }
                : panel
            )
          );
          setCurrentReuploadClothingPanelId(null);
          newPanel = {
            ...results.panels[0],
            componentId: newComponentId,
            type: 'clothing',
            source: 'upload',
            file: results.panels[0].file
          };
        } else {
          const panelsWithType = results.panels.map(panel => ({
            ...panel,
            componentId: generateId(ID_TYPES.COMPONENT),
            type: 'clothing',
            source: 'upload',
            file: panel.file
          }));
          setOtherClothingPanels(prevPanels => [...prevPanels, ...panelsWithType]);
          newPanel = panelsWithType[0];
        }

        // 设置未保存更改状态
        setHasUnsavedChanges(true);

        setShowTips(true);
        setTipContent(MASK_SAVED_TIP);

        // 上传成功后自动打开蒙版绘制弹窗
        setTimeout(() => {
          setCurrentMaskPanel(newPanel);
          setShowMaskDrawModal(true);
        }, 300);
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        setOtherClothingPanels(prevPanels =>
          prevPanels.filter(panel => panel.status !== 'processing'));
        setCurrentReuploadClothingPanelId(null);
      }
    } catch (error) {
      console.error('处理手动蒙版服装上传结果时出错:', error);
      message.error('处理手动蒙版服装上传结果时出错: ' + (error.message || '未知错误'));
    }
  };

  // 第二个标签页专用的服装上传函数
  const handleOtherClothingFileUpload = (file) => {
    setShowUploadGuide(false);
    setHasUnsavedChanges(true);
  };

  // 第二个标签页专用的模特上传处理函数
  const handleOtherModelUploadResult = async (results) => {
    try {
      if (results.type === 'panels') {
        let newPanel;

        if (currentReuploadModelPanelId) {
          const newComponentId = generateId(ID_TYPES.COMPONENT);
          setOtherModelPanels(prevPanels =>
            prevPanels.map(panel =>
              panel.componentId === currentReuploadModelPanelId
                ? {
                  ...results.panels[0],
                  componentId: newComponentId,
                  type: 'model',
                  source: 'upload',
                  file: results.panels[0].file
                }
                : panel
            )
          );

          setCurrentReuploadModelPanelId(null);
          newPanel = {
            ...results.panels[0],
            componentId: newComponentId,
            type: 'model',
            source: 'upload',
            file: results.panels[0].file
          };
        } else {
          const panelsWithType = results.panels.map(panel => ({
            ...panel,
            componentId: generateId(ID_TYPES.COMPONENT),
            type: 'model',
            source: 'upload',
            file: panel.file
          }));
          setOtherModelPanels(prevPanels => [...prevPanels, ...panelsWithType]);
          newPanel = panelsWithType[0];
        }

        // 设置未保存更改状态
        setHasUnsavedChanges(true);

        // 显示提示
        setShowTips(true);
        setTipContent(MASK_SAVED_TIP);

        // 上传成功后自动打开蒙版绘制弹窗
        setTimeout(() => {
          setCurrentMaskPanel(newPanel);
          setShowMaskDrawModal(true);
        }, 300);
      } else if (results.type === 'error') {
        console.error('上传错误:', results.error);
        message.error('上传失败: ' + results.error);
        setOtherModelPanels(prevPanels =>
          prevPanels.filter(panel => panel.status !== 'processing'));
        setCurrentReuploadModelPanelId(null);
      }
    } catch (error) {
      console.error('处理手动蒙版模特上传结果时出错:', error);
      message.error('处理手动蒙版模特上传结果时出错: ' + (error.message || '未知错误'));
    }
  };

  // 第二个标签页专用的模特上传函数
  const handleOtherModelFileUpload = (file) => {
    console.log('手动蒙版模特上传:', file);
  };

  // 处理删除模特面板
  const handleDeleteModelPanel = (panelId) => {
    setModelPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
    setHasUnsavedChanges(true);
  };

  // 第二个标签页专用的删除模特面板函数
  const handleDeleteOtherModelPanel = (panelId) => {
    setOtherModelPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
    setHasUnsavedChanges(true);
  };

  // 处理重新上传模特图
  const handleReuploadModel = (panel) => {
    if (panel && panel.componentId) {
      // 记录正在重新上传的面板ID
      setCurrentReuploadModelPanelId(panel.componentId);
      // 打开上传指南
      setShowModelUploadGuide(true);
    }
  };

  // 第二个标签页专用的重新上传模特图函数
  const handleReuploadOtherModel = (panel) => {
    if (panel && panel.componentId) {
      // 记录正在重新上传的面板ID
      setCurrentReuploadModelPanelId(panel.componentId);
      // 打开上传指南
      setShowModelUploadGuide(true);
    }
  };

  // 处理模特图状态变化
  const handleModelStatusChange = (panelId, newStatus) => {
    setModelPanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId ? { ...panel, status: newStatus } : panel
      )
    );
  };

  // 第二个标签页专用的模特状态变化函数
  const handleOtherModelStatusChange = (panelId, newStatus) => {
    setOtherModelPanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId ? { ...panel, status: newStatus } : panel
      )
    );
  };

  // 处理删除服装面板
  const handleDeleteClothingPanel = (panelId) => {
    setClothingPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
    setHasUnsavedChanges(true);
  };

  // 第二个标签页专用的删除服装面板函数
  const handleDeleteOtherClothingPanel = (panelId) => {
    setOtherClothingPanels(prevPanels => prevPanels.filter(panel => panel.componentId !== panelId));
    setHasUnsavedChanges(true);
  };

  // 处理重新上传服装图
  const handleReuploadClothing = (panel) => {
    if (panel && panel.componentId) {
      // 先删除当前面板
      handleDeleteClothingPanel(panel.componentId);
      // 然后打开上传弹窗
      setShowUploadGuide(true);
      setOperationsPanel(null);
    }
  };

  // 第二个标签页专用的重新上传服装图函数
  const handleReuploadOtherClothing = (panel) => {
    if (panel && panel.componentId) {
      // 先删除当前面板
      handleDeleteOtherClothingPanel(panel.componentId);
      // 然后打开上传弹窗
      setShowUploadGuide(true);
      setOperationsPanel(null);
    }
  };

  // 处理服装状态变更
  const handleClothingStatusChange = (panelId, newStatus) => {
    setClothingPanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId
          ? { ...panel, status: newStatus }
          : panel
      )
    );
  };

  // 第二个标签页专用的服装状态变更函数
  const handleOtherClothingStatusChange = (panelId, newStatus) => {
    setOtherClothingPanels(prevPanels =>
      prevPanels.map(panel =>
        panel.componentId === panelId
          ? { ...panel, status: newStatus }
          : panel
      )
    );
  };

  // 添加蒙版数据处理专用函数
  const prepareMaskData = (modelPanel) => {
    if (!modelPanel) return null;

    // 提取标准化的蒙版数据
    return {
      hasMask: modelPanel.hasMask || false,
      maskData: modelPanel.maskData,
      maskPath: modelPanel.maskPath,
      maskFileName: modelPanel.maskFileName
    };
  };

  // 添加蒙版组件格式化函数
  const formatMaskComponent = (panel, type = 'clothing') => {
    // 类型验证 - 参数必须为对象类型
    if (!panel || typeof panel !== 'object') return null;

    // 服装款式描述格式化
    if (type === 'clothing') {
      return {
        componentType: "maskDescriptionPanel",
        componentId: generateId(ID_TYPES.COMPONENT),
        name: "服装款式描述",
        selectedTag: panel.selectedTag || '',
        customText: panel.customText || '',
        description: panel.selectedTag ?
          tagMapping[panel.selectedTag] || panel.selectedTag :
          panel.customText || panel.description || ''
      };
    }

    // 模特款式描述格式化
    if (type === 'model') {
      return {
        componentType: "maskDescriptionPanel",
        componentId: generateId(ID_TYPES.COMPONENT),
        name: "模特款式描述",
        selectedTag: panel.selectedTag || '',
        customText: panel.customText || '',
        description: panel.selectedTag ?
          tagMapping[panel.selectedTag] || panel.selectedTag :
          panel.customText || panel.description || ''
      };
    }

    // 模特蒙版组件格式化 (原有功能保留)
    if (type === 'mask' && panel.hasMask) {
      return {
        componentType: "modelMaskPanel",
        componentId: `mask-${generateId(ID_TYPES.COMPONENT)}`,
        name: "模特蒙版",
        maskData: panel.maskData,
        maskPath: panel.maskPath,
        maskFileName: panel.maskFileName,
        parentComponentId: panel.componentId
      };
    }

    return null;
  };

  // 添加统一的错误处理函数 - 使用标准参数顺序
  // const handleTaskError = (taskId, error) => {
  //   // 统一记录错误
  //   console.error('API调用失败:', error);

  //   // 显示给用户的错误信息
  //   const errorMessage = error?.message || '未知错误';

  //   // 更新任务状态
  //   setGenerationTasks(prev => 
  //     prev.map(task => {
  //       if (task.taskId === taskId) {
  //         return {
  //           ...task,
  //           status: 'failed',
  //           errorMessage: errorMessage
  //         };
  //       }
  //       return task;
  //     })
  //   );

  //   // 关闭加载状态
  //   setIsProcessing(false);

  //   // 显示错误消息
  //   message.error('创建任务失败: ' + (error.message || '未知错误'));

  //   return errorMessage;
  // };

  // 处理API直接返回的结果 - 标准函数
  // const handleDirectResponse = (result) => {
  //   if (!result) return null;

  //   // 处理标准results数组格式
  //   if (result.results && Array.isArray(result.results) && result.results.length > 0) {
  //     return result.results
  //       .filter(item => item && item.url)
  //       .map((item, index) => ({
  //         url: formatImageUrl(item.url),
  //         imageIndex: index,
  //         status: 'completed',
  //         progress: 100
  //       }));
  //   }

  //   // 检查generatedImages字段
  //   if (result.generatedImages && Array.isArray(result.generatedImages) && result.generatedImages.length > 0) {
  //     return result.generatedImages
  //       .filter(img => img && img.url)
  //       .map(img => ({
  //         url: formatImageUrl(img.url),
  //         imageIndex: img.imageIndex || 0,
  //         status: 'completed',
  //         progress: 100
  //       }));
  //   }

  //   return null;
  // };

  // 启动异步检查 - 标准函数
  // const startAsyncCheck = (taskId, apiResult) => {
  //   setGenerationTasks(prev => 
  //     prev.map(task => {
  //       if (task.taskId === taskId) {
  //         return {
  //           ...task,
  //           processInfo: apiResult || {},
  //           progress: {
  //             step: 1,
  //             total: 2,
  //             percentage: 20,
  //             text: '处理中...'
  //           }
  //         };
  //       }
  //       return task;
  //     })
  //   );

  //   // 启动定时检查
  //   setTimeout(() => checkImageResults(taskId), 2000);
  // };

  // 更新任务结果 - 标准函数
  // const updateTaskWithResults = (taskId, apiResult, processedResults) => {
  //   setGenerationTasks(prev => 
  //     prev.map(task => {
  //       if (task.taskId === taskId) {
  //         return {
  //           ...task,
  //           status: 'completed',
  //           processInfo: apiResult || {},
  //           generatedImages: processedResults,
  //           progress: {
  //             step: 2,
  //             total: 2,
  //             percentage: 100,
  //             text: '处理完成'
  //           }
  //         };
  //       }
  //       return task;
  //     })
  //   );

  //   setIsProcessing(false);
  //   message.success(`成功生成 ${processedResults.length} 张图片`);

  //   // 处理生成的图片
  //   handleImageGenerated(processedResults);
  // };

  // 任务完成处理 - 标准函数
  // const handleTaskCompleted = (taskId, taskResult) => {
  //   console.log('任务已完成，处理结果');

  //   // 提取任务中的图片信息
  //   const images = extractImagesFromTask(taskResult);
  //   console.log('从任务中提取的图片:', images);

  //   if (images.length > 0) {
  //     // 更新任务状态
  //     updateTaskWithResults(taskId, taskResult, images);
  //   } else {
  //     // 任务完成但没有图片，可能是数据结构问题
  //     console.warn('任务完成但没有找到图片，尝试使用备用方式检查');

  //     // 检查其他可能的图片位置
  //     const alternativeImages = findAlternativeImages(taskResult);

  //     if (alternativeImages.length > 0) {
  //       updateTaskWithResults(taskId, taskResult, alternativeImages);
  //     } else {
  //       // 如果仍然没有找到图片，更新状态为完成但没有图片
  //       updateTaskStatus(taskId, 'completed');
  //       setIsProcessing(false);
  //       message.info('任务已完成，但未找到图片数据');
  //     }
  //   }
  // };

  // 任务失败处理 - 标准函数
  // const handleTaskFailed = (taskId, taskResult) => {
  //   const errorMessage = taskResult.errorMessage || '未知错误';
  //   console.error(`任务失败: ${errorMessage}`);
  //   updateTaskStatus(taskId, 'failed', null, errorMessage);
  //   setIsProcessing(false);
  //   message.error(`生成失败: ${errorMessage}`);
  // };

  // 添加蒙版描述面板状态
  const [clothingMaskPanel, setClothingMaskPanel] = useState({
    componentId: generateId(ID_TYPES.COMPONENT),
    selectedTag: '',
    customText: '',
    description: ''
  });

  const [modelMaskPanel, setModelMaskPanel] = useState({
    componentId: generateId(ID_TYPES.COMPONENT),
    selectedTag: '',
    customText: '',
    description: ''
  });

  // 处理蒙版描述面板变化
  const handleMaskPanelChange = (panel) => {
    setHasUnsavedChanges(true);
    if (panel.name === '服装款式描述' || panel.componentId === clothingMaskPanel.componentId) {
      setClothingMaskPanel(panel);
    } else {
      setModelMaskPanel(panel);
    }
  };

  // 处理蒙版扩张值变化
  const handleMaskExpandChange = (value) => {
    setMaskExpandValue(value);
    setHasUnsavedChanges(true);
  };
  function generateAlphaMaskFromBrush(maskUrl, outputName = 'alpha-mask.png') {
    return new Promise((resolve, reject) => {
      if (!maskUrl) {
        console.error('必须提供蒙版图 URL');
        reject(new Error('必须提供蒙版图 URL'));
        return;
      }

      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        const width = img.width;
        const height = img.height;

        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');

        ctx.drawImage(img, 0, 0);
        const imageData = ctx.getImageData(0, 0, width, height);
        const pixels = imageData.data;

        for (let i = 0; i < pixels.length; i += 4) {
          const r = pixels[i];
          const g = pixels[i + 1];
          const b = pixels[i + 2];
          const gray = (r + g + b) / 3;

          // 设置 RGB 为 255（白色），Alpha 为 灰度值（白笔刷=255，黑背景=0）
          pixels[i] = 255;
          pixels[i + 1] = 255;
          pixels[i + 2] = 255;
          pixels[i + 3] = gray;
        }

        ctx.putImageData(imageData, 0, 0);

        canvas.toBlob((blob) => {
          if (!blob) {
            console.error('导出失败');
            reject(new Error('导出失败'));
            return;
          }

          // 下载功能
          // const url = URL.createObjectURL(blob);
          // const a = document.createElement('a');
          // a.href = url;
          // a.download = outputName;
          // a.click();
          // URL.revokeObjectURL(url);

          // 返回 File 对象（可选，也可以只返回 Blob）
          const file = new File([blob], outputName, { type: 'image/png' });
          resolve(file);
        }, 'image/png');
      };

      img.onerror = (err) => {
        console.error('蒙版加载失败:', err);
        reject(err);
      };

      img.src = maskUrl;
    });
  }
  // 处理开始生成按钮点击 - 优化错误处理
  const handleGenerate = async () => {
    let taskData;
    setSeed(useRandomSeed ? (Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)) : seed);

    // 根据当前激活的标签页选择对应的面板数据
    let currentClothingPanels, currentModelPanels;
    console.log('controlPanelTab', controlPanelTab);

    if (controlPanelTab === 'front') {
      // 第一个标签页：使用原有的面板数据
      currentClothingPanels = clothingPanels;
      currentModelPanels = modelPanels;
    } else {
      // 第二个标签页：使用手动蒙版的面板数据
      currentClothingPanels = otherClothingPanels;
      currentModelPanels = otherModelPanels;
    }
    // 验证必要的条件 - 模特换装页面需要同时上传服装和模特图片
    if (!currentClothingPanels || currentClothingPanels.length === 0) {
      message.error('请先上传服装图片');
      return;
    }

    if (!currentModelPanels || currentModelPanels.length === 0) {
      message.error('请先上传模特图片');
      return;
    }

    // 判断是否是手动蒙版模式
    const hasManualMask = currentModelPanels[0] && currentModelPanels[0].hasMask;

    // 验证服装蒙版描述是否已填写
    if ((!clothingMaskPanel.selectedTag || clothingMaskPanel.selectedTag.trim() === '') && controlPanelTab === 'front' &&
      (!clothingMaskPanel.customText || clothingMaskPanel.customText.trim() === '') &&
      (!clothingMaskPanel.description || clothingMaskPanel.description.trim() === '')) {
      message.error('请选择服装款式标签或填写自定义描述');
      return;
    }
debugger
    // 自动蒙版模式下验证模特蒙版描述 (手动蒙版模式不需要此验证)
    if (!hasManualMask && controlPanelTab === 'front' &&
      (!modelMaskPanel.selectedTag || modelMaskPanel.selectedTag.trim() === '') &&
      (!modelMaskPanel.customText || modelMaskPanel.customText.trim() === '') &&
      (!modelMaskPanel.description || modelMaskPanel.description.trim() === '')) {
      message.error('请选择模特款式标签或填写自定义描述');
      return;
    }

    // 手动蒙版模式下验证是否有有效的蒙版数据
    if (hasManualMask && controlPanelTab === 'front' &&
      (!currentModelPanels[0].maskData && !currentModelPanels[0].maskPath)) {
      message.error('请先绘制模特蒙版再进行生成');
      return;
    }
    if (controlPanelTab === 'other') {
      if (!currentClothingPanels[0].hasMask) {
        message.error('请先绘制服装蒙版再进行生成');
        return;
      }
      if (!currentModelPanels[0].hasMask) {
        message.error('请先绘制模特蒙版再进行生成');
        return;
      }
    }

    // 记录蒙版扩张值
    let maskExpandAmount = maskExpandValue;

    // 开始处理中状态
    setIsProcessing(true);

    // 获取当前用户ID
    const currentUserId = userId || getCurrentUserId();
    const balance = await checkUserBalance('模特换装', 'try-on', imageQuantity);
    if (balance.code !== 200) {
      message.error(balance.message);
      setIsProcessing(false);
      return;
    }
    // 生成唯一的任务ID
    const generatedTaskId = generateId(ID_TYPES.TASK);

    // 上传模特图片到服务器（如果是本地上传的）
    let serverFileName = null;
    let modelServerUrl = null;

    let modelPanel = currentModelPanels[0];
    // 检查是否有需要上传的模特图片
    if ((modelPanel.file && modelPanel.source === 'upload') ||
      (modelPanel.source === 'upload' && !modelPanel.file)) {
      try {
        message.loading('正在上传模特图片...', 0);

        let fileToUpload = modelPanel.file;

        // 如果没有file对象但有URL，需要从URL获取文件
        if (!fileToUpload && modelPanel.url) {
          try {
            const response = await fetch(modelPanel.url);
            const blob = await response.blob();
            fileToUpload = new File([blob], modelPanel.serverFileName || 'model.jpg', {
              type: blob.type || 'image/jpeg'
            });
          } catch (error) {
            console.error('从URL获取模特文件失败:', error);
            message.error('模特图片处理失败，请重试');
            setIsProcessing(false);
            return;
          }
        }

        // 上传到服务器
        const { urls, fileInfos } = await uploadFiles([fileToUpload], 'try-on');

        if (fileInfos) {
          const resultData = fileInfos[0];
          serverFileName = modelPanel.serverFileName;
          // 更新面板状态
          currentModelPanels[0].url = resultData.url;
          modelPanel = {
            ...modelPanel,
            url: resultData.url,
            serverFileName: modelPanel.serverFileName,
            originalImage: urls[0],
            file: undefined, // 清除file属性，避免重复上传和存储不必要的数据
            fileInfo: {
              ...(modelPanel.fileInfo || {}),
              ...fileInfos[0],
              serverFileName: fileInfos[0].name // 确保在fileInfo中也设置serverFileName
            }
          }

          message.success('模特图片上传成功');
        } else {
          throw new Error('服务器返回的上传结果格式不正确');
        }
      } catch (error) {
        console.error('上传模特图片失败:', error);
        message.error('上传模特图片失败: ' + (error.message || '未知错误'));
        setIsProcessing(false);
        return;
      } finally {
        message.destroy();
      }
    } else {
      // 使用已有的服务器文件名
      serverFileName = modelPanel.serverFileName ||
        modelPanel.fileInfo?.serverFileName ||
        modelPanel.fileInfo?.filename;

      // 如果已有URL则使用，否则构建一个
      modelServerUrl = modelPanel.url;

      if (!serverFileName) {
        message.error('模特图片缺少服务器文件名，无法创建任务');
        setIsProcessing(false);
        return;
      }
    }

    // 上传服装图片到服务器（如果是本地上传的）
    let clothingServerFileName = null;
    let clothingServerUrl = null;
    let clothingPanel = currentClothingPanels[0];

    // 检查是否有需要上传的服装图片
    if ((clothingPanel.file && clothingPanel.source === 'upload') ||
      (clothingPanel.source === 'upload' && !clothingPanel.file)) {
      try {
        message.loading('正在上传服装图片...', 0);

        let fileToUpload = clothingPanel.file;

        // 如果没有file对象但有URL，需要从URL获取文件
        if (!fileToUpload && clothingPanel.url) {
          try {
            const response = await fetch(clothingPanel.url);
            const blob = await response.blob();
            fileToUpload = new File([blob], clothingPanel.serverFileName || 'clothing.jpg', {
              type: blob.type || 'image/jpeg'
            });
          } catch (error) {
            console.error('从URL获取服装文件失败:', error);
            message.error('服装图片处理失败，请重试');
            setIsProcessing(false);
            return;
          }
        }

        const { urls, fileInfos } = await uploadFiles([fileToUpload], 'try-on');

        if (fileInfos) {
          const resultData = fileInfos[0];
          currentClothingPanels[0].url = resultData.url;
          clothingServerFileName = clothingPanel.serverFileName;
          clothingPanel.originalImage = resultData.url;
          // 使用辅助函数构建标准化的服务器URL
          clothingServerUrl = resultData.url;
          clothingPanel = {
            ...clothingPanel,
            url: resultData.url,
            serverFileName: clothingPanel.serverFileName,
            originalImage: urls[0],
            file: undefined, // 清除file属性，避免重复上传和存储不必要的数据
            fileInfo: {
              ...(clothingPanel.fileInfo || {}),
              ...fileInfos[0],
              serverFileName: fileInfos[0].name // 确保在fileInfo中也设置serverFileName
            }
          }
          message.success('服装图片上传成功');
        } else {
          throw new Error('服务器返回的上传结果格式不正确');
        }
      } catch (error) {
        console.error('上传服装图片失败:', error);
        message.error('上传服装图片失败: ' + (error.message || '未知错误'));
        setIsProcessing(false);
        return;
      } finally {
        message.destroy();
      }
    } else {
      // 使用已有的服务器文件名
      clothingServerFileName = clothingPanel.serverFileName ||
        clothingPanel.fileInfo?.serverFileName ||
        clothingPanel.file?.name;

      // 如果已有URL则使用
      clothingServerUrl = clothingPanel.url;

      if (!clothingServerFileName) {
        message.error('服装图片缺少服务器文件名，无法创建任务');
        setIsProcessing(false);
        return;
      }
    }
    // 根据当前标签页选择工作流
    let taskType;
    if (controlPanelTab === 'front') {
      // 第一个标签页：使用原有的模特换装工作流
      taskType = hasManualMask ? 'tryonmanual' : 'tryonauto';
      if (hasManualMask) {
        console.log('使用手动蒙版工作流: tryonmanual');
      } else {
        console.log('使用自动蒙版工作流: tryonauto');
      }
    } else {
      // 第二个标签页：使用手动蒙版的面板数据，原本为'detail-migration'，现统一为'tryonother'
      taskType = 'tryonother';
      console.log('使用手动蒙版工作流: tryonother');
    }

    let maskUrl = null;

    try {
      // 准备页面面板数据 - 将现有面板转换为标准格式
      const formattedClothingPanels = {
        ...currentClothingPanels[0],
        type: 'clothing', // 确保类型正确
        componentType: 'clothingPanel',
        componentId: generateId(ID_TYPES.COMPONENT),
        isMainImage: true, // 服装为主图片
        status: 'completed',
        name: currentClothingPanels[0].title || '服装'
      };

      const formattedModelPanels = {
        ...currentModelPanels[0],
        type: 'model', // 确保类型正确
        componentType: 'modelMaskPanel',
        componentId: generateId(ID_TYPES.COMPONENT),
        isMainImage: false, // 模特不是主图片
        status: 'completed',
        name: currentModelPanels[0].title || '模特原图',
        // 确保有服务器文件名
        serverFileName: serverFileName
      };

      // 格式化蒙版描述信息
      const formattedMaskPanel = formatMaskComponent(clothingMaskPanel);

      // 自动蒙版模式下，添加模特蒙版描述
      const formattedModelMaskPanel = !hasManualMask ? formatMaskComponent(modelMaskPanel, 'model') : null;

      // 如果是手动蒙版模式，添加蒙版数据
      let maskData = null;
      if (hasManualMask) {
        maskData = prepareMaskData(currentModelPanels[0]);

        if (!maskData) {
          message.error('蒙版数据准备失败');
          setIsProcessing(false);
          return;
        }
      }
      // 整合所有面板数据
      const taskComponents = [
        { ...formattedClothingPanels, maskData: undefined },
        { ...formattedModelPanels, maskData: undefined },
        {
          componentType: 'randomSeedSelector',
          componentId: generateId(ID_TYPES.COMPONENT),
          name: '随机种子',
          useRandom: false,
          value: seed
        },
        {
          componentType: 'quantityPanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          name: '生成数量',
          quantity: imageQuantity
        },
        formattedMaskPanel,
      ];

      // 自动蒙版模式下添加模特蒙版描述
      if (formattedModelMaskPanel) {
        taskComponents.push(formattedModelMaskPanel);
      }

      // 添加蒙版扩张组件（在自动蒙版模式下）
      if (!hasManualMask) {
        taskComponents.push({
          componentType: 'maskExpanderPanel',
          componentId: generateId(ID_TYPES.COMPONENT),
          name: '蒙版扩张',
          expandValue: maskExpandAmount,
          status: 'completed'
        });
      }

      // 创建新任务数据
      taskData = {
        taskId: generatedTaskId,
        userId: currentUserId,
        createdAt: new Date(),
        status: 'processing',
        pageType: 'try-on',
        taskType: taskType,
        imageCount: imageQuantity,
        // 添加服务器文件名引用 - 使用模特图片作为主要图片
        serverFileName: serverFileName,
        primaryImageFileName: serverFileName,
        // 添加服装图片引用
        clothingFileName: clothingServerFileName,
        components: taskComponents,
        // maskData: maskData,
        // 初始化生成图片数组
        generatedImages: Array(imageQuantity).fill(null).map((_, index) => ({
          imageIndex: index,
          status: 'processing'
        })),
        processInfo: {
          results: []
        }
      };

      console.log('准备的任务数据:', {
        taskId: taskData.taskId,
        components: taskData.components.map(c => c.componentType),
        maskData: maskData ? '有蒙版数据' : '无蒙版数据'
      });

      // 先添加到本地状态，使UI立即响应
      if (generationAreaRef.current) {
        generationAreaRef.current.setGenerationTasks(taskData);
      }
      await createFlowTask(taskData);

      let result;

      if (controlPanelTab === 'front') {
        // 第一个标签页：使用原有的模特换装工作流
        if (hasManualMask) {
          let output_url;
          if (modelPanel.hasMask && modelPanel.maskPath) {
            // 如果有蒙版，使用蒙版处理
            const img = await processMask(modelPanel.url, modelPanel.maskPath);
            output_url = img.output_url;
            if (output_url == "") {
              message.error('蒙版处理失败，检查图片格式信息是否正确');
              setIsProcessing(false);
              return;
            }
          } else {
            // 如果没有蒙版，直接使用原图URL
            output_url = modelPanel.url;
            console.log('手动蒙版模式但无蒙版：直接使用原图URL', output_url);
          }
          result = await executeFlow(WORKFLOW_NAME.TRYON_MANUAL,
            {
              "27": {
                "seed": seed,
              },
              "28": {
                "amount": imageQuantity
              },
              "76": {
                "url": output_url
              },
              "75": {
                "url": formattedClothingPanels.originalImage
              },
              "70": {
                "prompt": formattedMaskPanel.description
              },
              "subInfo": {
                "type": "try-on",
                "title": "模特换装",
                "count": imageQuantity
              }
            }, taskData.taskId
          );
          setIsProcessing(false);
          setHasUnsavedChanges(false);
        } else {
          result = await executeFlow(WORKFLOW_NAME.TRYON_AUTO,
            {
              // 服装 描述
              "70": {
                "prompt": formattedMaskPanel.description
              },
              // 服装地址
              "75": {
                "url": formattedClothingPanels.originalImage
              },
              // 模特地址
              "76": {
                "url": formattedModelPanels.url
              },
              // 模特 款式描述
              "79": {
                "prompt": formattedModelMaskPanel.description
              },
              "81": {
                "expand": maskExpandAmount
              },
              "27": {
                "seed": seed
              },
              "28": {
                "amount": imageQuantity
              },
              "subInfo": {
                "type": "try-on",
                "title": "模特换装",
                "count": imageQuantity
              }
            }, taskData.taskId
          );
          setIsProcessing(false);
          setHasUnsavedChanges(false);
        }
      } else {
        // 第二个标签页：使用细节还原工作流
        let model_output_url;
        console.log('currentModelPanels', currentModelPanels);
        console.log('currentClothingPanels', currentClothingPanels);
        console.log('modelPanel', modelPanel);
        console.log('clothingPanel', clothingPanel);
        if (currentModelPanels[0].hasMask && currentModelPanels[0].maskPath) {
          // 如果有蒙版，使用蒙版处理
          const img = await processMask(modelPanel.url, currentModelPanels[0].maskPath);
          model_output_url = img.output_url;
          if (model_output_url == "") {
            message.error('模特蒙版处理失败，检查图片格式信息是否正确');
            setIsProcessing(false);
            return;
          }
        } else {
          // 如果没有蒙版，直接使用原图URL
          model_output_url = currentModelPanels[0].url;
          console.log('模特无蒙版模式：直接使用原图URL', model_output_url);
        }

        let clothing_output_url;
        if (currentClothingPanels[0].hasMask && currentClothingPanels[0].maskPath) {
          // 如果有蒙版，使用蒙版处理
          const img1 = await processMask(clothingPanel.url, currentClothingPanels[0].maskPath);
          clothing_output_url = img1.output_url;
          if (clothing_output_url == "") {
            message.error('服装蒙版处理失败，检查图片格式信息是否正确');
            setIsProcessing(false);
            return;
          }
        } else {
          // 如果没有蒙版，直接使用原图URL
          clothing_output_url = currentClothingPanels[0].url;
          console.log('服装无蒙版模式：直接使用原图URL', clothing_output_url);
        }
        result = await executeFlow(WORKFLOW_NAME.TRYON_OTHER,
          {
            "27": {
              "seed": seed
            },
            "28": {
              "amount": imageQuantity
            },
            "76": {
              "url": model_output_url
            },
            "75": {
              "url": clothing_output_url
            },
            "4": {
              "prompt": formattedMaskPanel.description
            },
            "subInfo": {
              "type": "try-on",
              "title": "模特换装",
              "count": imageQuantity
            }
          }, taskData.taskId
        );
        setIsProcessing(false);
        setHasUnsavedChanges(false);
      }
      if (generationAreaRef.current) {
        taskData.promptId = result.promptId;
        taskData.instanceId = result.instanceId;
        taskData.url = result.url;
        taskData.newTask = true;
        taskData.netWssUrl=result.netWssUrl;
        taskData.clientId=result.clientId;
        generationAreaRef.current.setGenerationTasks(taskData);
      }
    } catch (error) {
      console.error('创建任务失败:', error);
      message.error('创建任务失败: ' + error.message);
      taskData.status = 'failed';
      updateFlowTask(taskData.taskId, taskData);
      setIsProcessing(false);

      if (generationAreaRef.current) {
        generationAreaRef.current.setGenerationTasks(taskData);
      }

      // 调用updateTask以触发失败提示音
      updateTask(taskData);
    }

    // 生成成功后重置未保存状态
    setHasUnsavedChanges(false);
  };

  // 处理单张图片下载
  const handleDownloadImage = async (imageUrl, taskId, index) => {
    try {
      message.info('准备下载...');
      const httpsUrl = imageUrl.replace(/^http:/, 'https:');
      const response = await fetch(httpsUrl);
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `${taskId}_${parseInt(index) + 1}.jpg`;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
    } catch (error) {
      message.error('下载失败');
      console.error('下载出错:', error);
    }
  };

  // 处理批量下载
  const handleBatchDownload = async (task) => {
    // 调用通用下载辅助函数
    await downloadHelper(
      task,
      fetch, // 传递fetch函数
      handleDownloadImage // 传递单张图片下载函数
    );
  };

  // 处理删除任务
  const handleDeleteTask = (taskId, skipConfirm = false) => {
    // 定义删除任务的函数
    const deleteTaskFunction = async () => {
      try {
        // 获取当前用户ID，如果未登录则使用开发者ID
        const userId = getCurrentUserId() || 'developer';

        // 显示操作中的消息
        message.loading({ content: '正在删除...', key: `delete-${taskId}` });

        // 先从本地状态中移除，提供即时反馈
        setGenerationTasks(prev => prev.filter(task => task.taskId !== taskId));

        // 调用API进行删除
        const success = await deleteTask(taskId, userId);

        if (success) {
          message.success({ content: '记录已删除', key: `delete-${taskId}` });
        } else {
          // 如果API返回失败但UI已更新，只在控制台记录警告
          console.warn('API删除任务可能失败，但前端已更新状态');
        }
      } catch (error) {
        console.error('删除任务失败:', error);
        message.error({
          content: '删除任务失败: ' + error.message,
          key: `delete-${taskId}`
        });

        // 即使API调用失败，仍从本地状态中移除
        setGenerationTasks(prev => prev.filter(task => task.taskId !== taskId));
      }
    };

    // 如果跳过确认，直接删除
    if (skipConfirm) {
      deleteTaskFunction();
      return;
    }

    // 否则显示确认对话框
    showDeleteConfirmModal({
      title: '确认删除',
      content: '确定要删除这条生成记录吗？删除后将无法恢复。',
      okText: '确认删除',
      cancelText: '取消',
      okButtonProps: {
        danger: true,
      },
      animation: false,
      transitionName: '',
      maskTransitionName: '',
      onOk: deleteTaskFunction
    });
  };

  // 处理重新编辑任务
  const handleEditTask = (task) => {
    try {
      if (!task) {
        message.error('无效的任务数据');
        return;
      }

      console.log('开始编辑任务:', task.taskId);

      // 清空当前所有面板 - 确保从头开始填充
      setClothingPanels([]);
      setModelPanels([]);

      // 1. 提取组件列表 - 仅使用数组格式，不再兼容对象格式
      const components = Array.isArray(task.components) ? task.components : [];

      // 2. 按类型处理各个组件

      // 2.1 处理服装面板组件
      const clothingComponent = components.find(c => c.componentType === 'clothingPanel');

      if (clothingComponent) {
        console.log('获取到服装组件:', clothingComponent);
        // 转换为面板格式
        const newClothingPanel = {
          componentId: generateId(ID_TYPES.COMPONENT),
          title: clothingComponent.name || '服装',
          status: 'completed',
          type: 'clothing',
          // 确保有图片路径
          processedFile: clothingComponent.url || clothingComponent.originalImage,
          originalImage: clothingComponent.originalImage || clothingComponent.url,
          serverFileName: clothingComponent.serverFileName,
          fileInfo: {
            ...(clothingComponent.fileInfo || {}),
            serverFileName: clothingComponent.serverFileName
          }
        };

        // 根据任务类型决定填充到哪个面板
        if (task.taskType === 'tryonother') {
          // 对于 tryonother 类型，先保存数据，稍后填充到 other 组件中
          console.log('tryonother 类型：服装数据将填充到 other 组件');
        } else {
          // 对于其他类型，填充到半自动蒙版组件
          setClothingPanels([newClothingPanel]);
        }
      } else {
        console.warn('未找到服装组件，请检查原始任务格式');
      }

      // 2.2 处理模特面板组件
      const modelComponent = components.find(c => c.componentType === 'modelMaskPanel');
      if (modelComponent) {
        console.log('获取到模特组件:', modelComponent);
        console.log('模特组件类型:', modelComponent.componentType);
        console.log('模特原图URL:', modelComponent.originalImage || modelComponent.url);
        console.log('模特蒙版数据:', modelComponent.maskData || modelComponent.maskPath);

        // 处理蒙版数据
        const maskData = modelComponent.maskData || modelComponent.maskPath;
        const hasMask = !!maskData || !!modelComponent.hasMask;

        // 转换为面板格式
        const newModelPanel = {
          componentId: generateId(ID_TYPES.COMPONENT),
          title: modelComponent.name || '模特',
          status: 'completed',
          type: 'model',
          // 确保有图片路径 - 优先使用originalImage
          url: modelComponent.url || modelComponent.originalImage,
          originalImage: modelComponent.originalImage || modelComponent.url,
          processedFile: modelComponent.processedFile || modelComponent.url || modelComponent.originalImage,
          // 服务器相关字段 - 只使用serverFileName
          serverFileName: modelComponent.serverFileName,
          fileInfo: {
            ...(modelComponent.fileInfo || {}),
            serverFileName: modelComponent.serverFileName
          },
          // 添加蒙版相关信息 - 确保所有字段都存在
          hasMask: hasMask,
          maskPath: modelComponent.maskPath || null,
          maskFile: modelComponent.maskFile || null,
          maskFileName: modelComponent.maskFileName || null,
          ...modelComponent
        };

        console.log('创建的新模特面板:', newModelPanel);

        // 根据任务类型决定填充到哪个面板
        if (task.taskType === 'tryonother') {
          // 对于 tryonother 类型，先保存数据，稍后填充到 other 组件中
          console.log('tryonother 类型：模特数据将填充到 other 组件');
        } else {
          // 对于其他类型，填充到半自动蒙版组件
          setModelPanels([newModelPanel]);
        }
      } else {
        console.warn('未找到模特组件，请检查原始任务格式');
        console.log('可用组件类型:', components.map(c => c.componentType));
      }

      // 2.3 回填随机种子设置
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      if (seedComponent) {
        console.log('获取到种子设置:', seedComponent);
        setUseRandomSeed(false);
        if (!seedComponent.useRandom && seedComponent.value !== undefined) {
          setSeed(seedComponent.value);
        }
      } else if (task.seed !== undefined) {
        // 回退1：检查顶层属性
        console.log('使用顶层seed属性:', task.seed);
        setUseRandomSeed(false);
        setSeed(task.seed);
      } else {
        // 回退2：使用默认值
        console.warn('未找到种子组件，使用默认随机种子');
        setUseRandomSeed(true);
        setSeed(Math.floor(Math.random() * 1000000));
      }

      // 2.4 回填数量设置
      const quantityComponent = components.find(c => c.componentType === 'quantityPanel');

      if (quantityComponent && quantityComponent.quantity) {
        setImageQuantity(quantityComponent.quantity);
      } else if (task.imageCount) {
        // 回退：使用顶层属性
        setImageQuantity(task.imageCount);
      }

      // 2.5 回填蒙版描述数据
      // 查找所有蒙版描述组件
      const maskDescriptionComponents = components.filter(c => c.componentType === 'maskDescriptionPanel');
      console.log('找到的蒙版描述组件:', maskDescriptionComponents);

      if (maskDescriptionComponents.length > 0) {
        // 分别查找服装和模特的描述组件
        const clothingDescComponent = maskDescriptionComponents.find(c =>
          c.name === '款式描述' || c.name === '服装款式描述' || (!c.name || c.name === '模特款式描述'));

        const modelDescComponent = maskDescriptionComponents.find(c =>
          c.name === '模特款式描述');

        // 处理服装描述组件
        if (clothingDescComponent) {
          console.log('获取到服装描述组件:', clothingDescComponent);
          setClothingMaskPanel({
            componentId: generateId(ID_TYPES.COMPONENT),
            selectedTag: clothingDescComponent.selectedTag || '',
            customText: clothingDescComponent.customText || '',
            description: clothingDescComponent.description || ''
          });
        }

        // 处理模特描述组件 - 仅在自动蒙版模式下
        if (modelDescComponent) {
          console.log('获取到模特描述组件:', modelDescComponent);
          setModelMaskPanel({
            componentId: generateId(ID_TYPES.COMPONENT),
            selectedTag: modelDescComponent.selectedTag || '',
            customText: modelDescComponent.customText || '',
            description: modelDescComponent.description || ''
          });
        }
      }
      // 兼容旧格式的数据
      else if (task.maskDescriptions) {
        // 回填服装蒙版描述
        if (task.maskDescriptions.clothing) {
          const clothingDesc = task.maskDescriptions.clothing;
          // 检查是否是预设标签之一
          const isPresetTag = ['分体泳衣', '连体泳衣', '裙式泳衣'].includes(clothingDesc);

          setClothingMaskPanel({
            componentId: generateId(ID_TYPES.COMPONENT),
            selectedTag: isPresetTag ? clothingDesc : '',
            customText: isPresetTag ? '' : clothingDesc,
            description: clothingDesc
          });
        }

        // 回填模特蒙版描述
        if (task.maskDescriptions.model) {
          const modelDesc = task.maskDescriptions.model;
          // 检查是否是预设标签之一
          const isPresetTag = ['分体泳衣', '连体泳衣', '裙式泳衣'].includes(modelDesc);

          setModelMaskPanel({
            componentId: generateId(ID_TYPES.COMPONENT),
            selectedTag: isPresetTag ? modelDesc : '',
            customText: isPresetTag ? '' : modelDesc,
            description: modelDesc
          });
        }
      } else {
        console.warn('未找到蒙版描述组件，使用默认空值');
        // 确保设置为空值
        setClothingMaskPanel({
          componentId: generateId(ID_TYPES.COMPONENT),
          selectedTag: '',
          customText: '',
          description: ''
        });
      }

      // 2.6 回填蒙版扩张值
      // 从组件数组中查找maskExpanderPanel组件
      const maskExpanderComponent = components.find(c => c.componentType === 'maskExpanderPanel');

      if (maskExpanderComponent && maskExpanderComponent.expandValue !== undefined) {
        console.log('获取到蒙版扩张组件:', maskExpanderComponent);
        setMaskExpandValue(maskExpanderComponent.expandValue);
      } else {
        console.warn('未找到蒙版扩张数据，使用默认值50');
        setMaskExpandValue(50);
      }

      // 切换到结果标签页

      if (task.taskType === 'tryonother') {
        setControlPanelTab('other');

        // 对于 tryonother 类型，将数据填充到 other 对应的组件中
        console.log('检测到 tryonother 类型，填充到 other 组件中');

        // 清空 other 面板数据
        setOtherClothingPanels([]);
        setOtherModelPanels([]);

        // 将服装数据填充到 otherClothingPanels
        if (clothingComponent) {
          const newOtherClothingPanel = {
            componentId: generateId(ID_TYPES.COMPONENT),
            title: clothingComponent.name || '服装',
            status: 'completed',
            type: 'clothing',
            processedFile: clothingComponent.url || clothingComponent.originalImage,
            originalImage: clothingComponent.originalImage || clothingComponent.url,
            serverFileName: clothingComponent.serverFileName,
            fileInfo: {
              ...(clothingComponent.fileInfo || {}),
              serverFileName: clothingComponent.serverFileName
            },
            hasMask: false,
            maskPath: clothingComponent.maskPath || null,
            url: clothingComponent.url || clothingComponent.originalImage,
          };
          setOtherClothingPanels([newOtherClothingPanel]);
          console.log('填充到 otherClothingPanels:', newOtherClothingPanel);
        }

        // 将模特数据填充到 otherModelPanels
        if (modelComponent) {
          const maskData = modelComponent.maskData || modelComponent.maskPath;
          const hasMask = !!maskData || !!modelComponent.hasMask;

          const newOtherModelPanel = {
            componentId: generateId(ID_TYPES.COMPONENT),
            title: modelComponent.name || '模特',
            status: 'completed',
            type: 'model',
            url: modelComponent.url || modelComponent.originalImage,
            originalImage: modelComponent.originalImage || modelComponent.url,
            processedFile: modelComponent.processedFile || modelComponent.url || modelComponent.originalImage,
            serverFileName: modelComponent.serverFileName,
            fileInfo: {
              ...(modelComponent.fileInfo || {}),
              serverFileName: modelComponent.serverFileName
            },
            maskPath: modelComponent.maskPath || null,
            maskFile: modelComponent.maskFile || null,
            maskFileName: modelComponent.maskFileName || null,
            ...modelComponent,
            hasMask: false,
          };
          setOtherModelPanels([newOtherModelPanel]);
          console.log('填充到 otherModelPanels:', newOtherModelPanel);
        }

        // 清空半自动蒙版的面板数据
        setClothingPanels([]);
        setModelPanels([]);
      } else {
        setControlPanelTab('front');
        // 对于非 tryonother 类型，保持原有逻辑
        console.log('非 tryonother 类型，使用半自动蒙版组件');
      }

      // 显示成功消息
      message.success('配置已重新导入，可继续进行调整');
    } catch (error) {
      console.error('编辑任务失败:', error);
      message.error('加载任务设置失败');
    }
  };

  // 添加点击外部关闭下拉菜单的处理
  useEffect(() => {
    const handleClickOutside = (event) => {
      const dropdowns = document.querySelectorAll('.dropdown-menu');
      dropdowns.forEach(dropdown => {
        if (!dropdown.parentElement.contains(event.target)) {
          dropdown.classList.remove('show');
        }
      });
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // 处理图片点击，打开详情弹窗
  const handleImageClick = (image, index, taskId) => {
    setSelectedImage({
      ...image,
      index,
      taskId: taskId
    });
    setShowImageDetails(true);
  };

  // 处理查看详情
  const handleViewDetails = (image, task, index) => {
    try {
      console.log('查看图片详情, 任务:', task);
      console.log('当前图片:', image);

      if (!task || !task.taskId) {
        message.error('无法获取任务详情：任务ID无效');
        return;
      }

      // 确保组件数组格式
      const components = Array.isArray(task.components) ? task.components : [];

      // 获取所需组件数据
      const modelComponent = components.find(c => c.componentType === 'modelMaskPanel');
      const clothingComponent = components.find(c => c.componentType === 'clothingPanel');
      const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');
      const quantityComponent = components.find(c => c.componentType === 'quantityPanel');
      const maskDescriptionComponents = components.filter(c => c.componentType === 'maskDescriptionPanel');

      console.log('查看任务详情 - 获取的组件:', {
        modelComponent,
        clothingComponent,
        seedComponent,
        quantityComponent,
        maskDescriptionComponents
      });

      // 获取任务级种子值或组件种子值
      const taskSeed = task.seed !== undefined ? task.seed :
        (seedComponent?.value !== undefined ? seedComponent.value :
          (image.seed || Math.floor(Math.random() * 1000000)));

      // 准备适配后的组件数据 - 使用数组结构
      const adaptedComponents = [];

      // 添加模特组件 - 确保使用modelMaskPanel作为组件类型
      if (modelComponent) {
        adaptedComponents.push({
          ...modelComponent,
          componentId: modelComponent.componentId || generateId(ID_TYPES.COMPONENT),
          componentType: 'modelMaskPanel' // 使用统一的组件类型名
        });
      }

      // 添加服装组件
      if (clothingComponent) {
        adaptedComponents.push({
          ...clothingComponent,
          componentId: clothingComponent.componentId || generateId(ID_TYPES.COMPONENT),
          componentType: 'clothingPanel'
        });
      }

      // 添加蒙版描述组件
      if (maskDescriptionComponents && maskDescriptionComponents.length > 0) {
        maskDescriptionComponents.forEach(component => {
          adaptedComponents.push({
            ...component,
            componentId: component.componentId || generateId(ID_TYPES.COMPONENT),
            componentType: 'maskDescriptionPanel'
          });
        });
      }

      // 添加种子选择器组件
      adaptedComponents.push({
        componentType: 'randomSeedSelector',
        componentId: seedComponent?.componentId || generateId(ID_TYPES.COMPONENT),
        useRandom: seedComponent?.useRandom || false,
        value: taskSeed
      });

      // 添加数量组件
      adaptedComponents.push({
        componentType: 'quantityPanel',
        componentId: quantityComponent?.componentId || generateId(ID_TYPES.COMPONENT),
        quantity: quantityComponent?.quantity || task.imageCount || 4
      });

      // 添加蒙版扩张组件（如果是自动蒙版模式）
      // 只检查modelMaskPanel类型
      const isAutoMask = modelComponent && !modelComponent.hasMask;
      const maskExpanderComponent = components.find(c => c.componentType === 'maskExpanderPanel');

      if (isAutoMask) {
        adaptedComponents.push({
          componentType: 'maskExpanderPanel',
          componentId: maskExpanderComponent?.componentId || generateId(ID_TYPES.COMPONENT),
          expandValue: maskExpanderComponent?.expandValue,
          status: 'completed',
          name: '蒙版扩张'
        });
      }

      return {
        ...task,
        // 确保任务对象也包含适配后的组件数据
        adaptedComponents
      }
    } catch (error) {
      console.error('处理图片详情时出错:', error);
      message.error('加载图片详情失败');
    }
  };

  // 关闭图片详情弹窗
  const handleCloseImageDetails = () => {
    // 关闭弹窗
    setShowImageDetails(false);

    // 重置图片相关状态
    setSelectedImage(null);
    setImageDetailsTask(null);

    // 重置图片位置和缩放状态
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
    lastPosition.current = { x: 0, y: 0 };
    setImageScale(100);
    setInitialScale(100);


    setShowAdvancedText(false);
  };

  // 添加点击外部关闭弹出层的处理函数
  useEffect(() => {
    const handleClickOutside = (event) => {
      // 检查点击是否发生在文本按钮上
      const isTextButton = event.target.closest('.text-button');
      // 检查点击是否发生在弹窗内部
      const isInsidePopup = event.target.closest('.text-popup');

      // 如果点击既不是文本按钮也不是弹窗内部，则关闭所有弹窗
      if (!isTextButton && !isInsidePopup) {
        setShowAdvancedText(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 计算初始缩放比例
  const calculateInitialScale = (img) => {
    if (!img) return 100;
    const container = img.parentElement;
    if (!container) return 100;

    // 计算图片在容器中的实际显示尺寸与真实尺寸的比例
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;

    // 计算图片适应容器时的尺寸
    const containerRatio = containerWidth / containerHeight;
    const imageRatio = imageNaturalWidth / imageNaturalHeight;

    let scale;
    if (imageRatio > containerRatio) {
      // 图片较宽，以容器宽度为基准
      scale = (containerWidth / imageNaturalWidth) * 100;
    } else {
      // 图片较高，以容器高度为基准
      scale = (containerHeight / imageNaturalHeight) * 100;
    }

    // 如果计算出的缩放比例大于100%，说明图片实际尺寸小于容器
    // 这种情况下我们应该将图片显示为其实际大小
    if (scale > 100) {
      scale = 100;
    }

    // 返回相对于真实尺寸的缩放百分比，四舍五入到整数
    return Math.round(scale);
  };

  // 处理图片加载完成
  const handleImageLoad = (e) => {
    const img = e.target;
    const initialScaleValue = calculateInitialScale(img);
    setInitialScale(initialScaleValue);  // 设置初始比例
    setImageScale(initialScaleValue);    // 设置当前比例
    imageRef.current = img;
  };

  // 处理缩放变化
  const handleScaleChange = (newScale) => {
    setImageScale(newScale);
  };

  // 添加复位处理函数
  const handleReset = () => {
    setImageScale(initialScale);
    setImagePosition({ x: 0, y: 0 });
    lastPosition.current = { x: 0, y: 0 };
  };

  // // 检查任务状态 - 标准化实现
  // const checkImageResults = async (taskId) => {
  //   try {
  //     console.log(`检查任务结果: ${taskId}`);

  //     // 在本地状态中找到对应任务
  //     const taskToCheck = generationTasks.find(t => t.taskId === taskId);
  //     if (!taskToCheck) {
  //       console.error(`未找到任务: ${taskId}`);
  //       return;
  //     }

  //     // 获取当前用户ID
  //     const currentUserId = userId || getCurrentUserId() || 'developer';

  //     // 获取任务详情
  //     const taskResult = await getTaskById(taskId, currentUserId);
  //     console.log('任务检查结果:', taskResult);

  //     if (!taskResult) {
  //       // 如果没有找到任务，继续检查
  //       scheduleNextCheck(taskId, taskToCheck.checkCount || 0);
  //       return;
  //     }

  //     // 处理任务状态
  //     if (taskResult.status === 'failed') {
  //       handleTaskFailed(taskId, taskResult);
  //     } else if (taskResult.status === 'completed') {
  //       handleTaskCompleted(taskId, taskResult);
  //     } else {
  //       // 任务仍在处理中，更新进度
  //       updateTaskProgress(taskId, taskResult);
  //       scheduleNextCheck(taskId, taskToCheck.checkCount || 0);
  //     }
  //   } catch (error) {
  //     console.error('检查任务出错:', error);
  //     // 使用统一错误处理
  //     handleTaskError(taskId, error);

  //     const localTask = generationTasks.find(t => t.taskId === taskId);
  //     if (!localTask?.checkCount || localTask.checkCount < 3) {
  //       scheduleNextCheck(taskId, localTask?.checkCount || 0);
  //     }
  //   }
  // };

  // // 更新任务进度 - 标准函数
  // const updateTaskProgress = (taskId, taskResult) => {
  //   const progress = taskResult.progress || { 
  //     step: 1, 
  //     total: 2, 
  //     percentage: 50, 
  //     text: taskResult.progressText || '处理中...' 
  //   };

  //   setGenerationTasks(prev => 
  //     prev.map(task => {
  //       if (task.taskId === taskId) {
  //         return {
  //           ...task,
  //           processInfo: taskResult,
  //           progress
  //         };
  //       }
  //       return task;
  //     })
  //   );
  // };

  // // 安排下一次检查
  // const scheduleNextCheck = (taskId, currentCheckCount = 0) => {
  //   const checkCount = (currentCheckCount || 0) + 1;

  //   // 更新检查次数
  //   setGenerationTasks(prev => 
  //     prev.map(task => {
  //       if (task.taskId === taskId) {
  //         return { ...task, checkCount };
  //       }
  //       return task;
  //     })
  //   );

  //   // 计算下一次检查的延迟，随着检查次数增加而延长间隔
  //   const delay = Math.min(2000 + checkCount * 500, 10000);

  //   // 限制最大检查次数
  //   if (checkCount < 20) {
  //     setTimeout(() => checkImageResults(taskId), delay);
  //   } else {
  //     // 超过最大检查次数，标记为失败
  //     updateTaskStatus(taskId, 'failed', null, '任务检查超时');
  //     setIsProcessing(false);
  //     message.error('生成超时，请稍后重试');
  //   }
  // };

  // 查找备用图片来源
  // const findAlternativeImages = (task) => {
  //   // 简化后只在服务器文件名中查找备用图片
  //   if (task.serverFileName) {
  //     const baseUrl = process.env.REACT_APP_BACKEND_URL;
  //     const generatedImage = {
  //       url: `${baseUrl}/developer/model/try-on/generated/${task.serverFileName}`,
  //       serverFileName: task.serverFileName,
  //       path: `/developer/model/try-on/generated/${task.serverFileName}`
  //     };

  //     console.log('基于serverFileName构建备用图片URL:', generatedImage);
  //     return [generatedImage];
  //   }

  //   // 没有找到备用图片来源
  //   return [];
  // };

  const [uploadedClothingId, setUploadedClothingId] = useState(null);
  const [uploadedClothingUrl, setUploadedClothingUrl] = useState(null);
  const [generatedImages, setGeneratedImages] = useState([]);
  const [showMaskDrawModal, setShowMaskDrawModal] = useState(false);
  const [currentMaskPanel, setCurrentMaskPanel] = useState(null);

  // 处理打开蒙版绘制弹窗
  const handleDrawMask = (panel) => {
    // 确保传递完整的蒙版相关属性
    const enhancedPanel = {
      ...panel,
      // 添加默认值确保蒙版属性完整
      hasMask: panel.hasMask || false,
      maskData: panel.maskData || null,
      maskPath: panel.maskPath || null,
      maskFileName: panel.maskFileName || null
    };

    setCurrentMaskPanel(enhancedPanel);
    setShowMaskDrawModal(true);
  };

  // 获取参考图片信息
  const getReferenceImage = (currentPanel) => {
    if (!currentPanel) return null;
    
    // 根据当前标签页选择对应的面板数组
    const currentModelPanels = controlPanelTab === 'front' ? modelPanels : otherModelPanels;
    const currentClothingPanels = controlPanelTab === 'front' ? clothingPanels : otherClothingPanels;
    
    // 如果当前是服装面板，返回第一个模特图片作为参考
    if (currentPanel.type === 'clothing' && currentModelPanels.length > 0) {
      const firstModelPanel = currentModelPanels[0];
      const imageUrl = firstModelPanel.processedFile || firstModelPanel.url || firstModelPanel.originalImage || firstModelPanel.imageUrl || firstModelPanel.localUrl || firstModelPanel.preview || firstModelPanel.image;
      if (imageUrl) {
        return {
          url: imageUrl,
          alt: '模特参考图片',
          type: '模特原图'
        };
      }
    }
    
    // 如果当前是模特面板，返回第一个服装图片作为参考
    if (currentPanel.type === 'model' && currentClothingPanels.length > 0) {
      const firstClothingPanel = currentClothingPanels[0];
      const imageUrl = firstClothingPanel.processedFile || firstClothingPanel.url || firstClothingPanel.originalImage || firstClothingPanel.imageUrl || firstClothingPanel.localUrl || firstClothingPanel.preview || firstClothingPanel.image;
      if (imageUrl) {
        return {
          url: imageUrl,
          alt: '服装参考图片',
          type: '服装'
        };
      }
    }
    
    return null;
  };

  // 处理保存蒙版
  const handleSaveMask = async (maskData, panelId, savePath) => {
    // 如果maskData为null，表示用户想要删除蒙版
    if (maskData === null) {
      console.log(`删除蒙版数据，面板ID: ${panelId}`);

      // 根据当前标签页和面板类型更新对应的面板数组
      if (controlPanelTab === 'front') {
        // 更新第一个标签页的面板
        setModelPanels(prevPanels =>
          prevPanels.map(panel =>
            panel.componentId === panelId
              ? {
                ...panel,
                hasMask: false,
                maskData: null,
                maskPath: null,
                maskFileName: null
              }
              : panel
          )
        );
      } else {
        // 更新第二个标签页的面板 - 需要判断是模特面板还是服装面板
        // 先检查模特面板
        const modelPanel = otherModelPanels.find(panel => panel.componentId === panelId);
        if (modelPanel) {
          setOtherModelPanels(prevPanels =>
            prevPanels.map(panel =>
              panel.componentId === panelId
                ? {
                  ...panel,
                  hasMask: false,
                  maskData: null,
                  maskPath: null,
                  maskFileName: null
                }
                : panel
            )
          );
        } else {
          // 检查服装面板
          const clothingPanel = otherClothingPanels.find(panel => panel.componentId === panelId);
          if (clothingPanel) {
            setOtherClothingPanels(prevPanels =>
              prevPanels.map(panel =>
                panel.componentId === panelId
                  ? {
                    ...panel,
                    hasMask: false,
                    maskData: null,
                    maskPath: null,
                    maskFileName: null
                  }
                  : panel
              )
            );
          }
        }
      }

      // 恢复默认提示
      setTipContent(TRY_ON_TIP);

      message.success({
        content: '蒙版已删除',
        duration: 2
      });

      return;
    }

    // 以下是正常保存蒙版的逻辑
    // 修正路径格式 - 确保包含server前缀
    const normalizedPath = savePath
      ? savePath.replace(/\\/g, '/')
      : `server/storage/${userId || getCurrentUserId() || 'developer'}/model/try-on/mask`;

    console.log(`保存蒙版数据，面板ID: ${panelId}, 标准化后的保存路径: ${normalizedPath}`);

    // 创建文件名 (使用面板ID + 时间戳，确保唯一性)
    const timestamp = Date.now();
    const fileName = `mask_${panelId}_${timestamp}.png`;
    console.log(`生成的蒙版文件名: ${fileName}`);

    // 使用标准格式更新本地状态，确保UI响应良好
    const temporaryMaskData = {
      hasMask: true,
      maskData: maskData,
      maskPath: `${normalizedPath}/${fileName}`,
      maskFileName: fileName
    };

    // 根据当前标签页和面板类型更新对应的面板数组
    if (controlPanelTab === 'front') {
      setModelPanels(prevPanels =>
        prevPanels.map(panel =>
          panel.componentId === panelId
            ? { ...panel, ...temporaryMaskData }
            : panel
        )
      );
    } else {
      // 更新第二个标签页的面板 - 需要判断是模特面板还是服装面板
      // 先检查模特面板
      const modelPanel = otherModelPanels.find(panel => panel.componentId === panelId);
      if (modelPanel) {
        setOtherModelPanels(prevPanels =>
          prevPanels.map(panel =>
            panel.componentId === panelId
              ? { ...panel, ...temporaryMaskData }
              : panel
          )
        );
      } else {
        // 检查服装面板
        const clothingPanel = otherClothingPanels.find(panel => panel.componentId === panelId);
        if (clothingPanel) {
          setOtherClothingPanels(prevPanels =>
            prevPanels.map(panel =>
              panel.componentId === panelId
                ? { ...panel, ...temporaryMaskData }
                : panel
            )
          );
        }
      }
    }

    // 显示处理中消息
    message.loading({
      content: '正在保存蒙版...',
      key: 'maskSave'
    });

    try {
      const file = await generateAlphaMaskFromBrush(maskData)
      const data = await uploadFiles([file], "optimize");
      if (data) {
        // 保存详细的文件路径信息
        const serverFilePath = data.fileInfos[0].url;


        message.success({
          content: '蒙版保存成功',
          key: 'maskSave',
          duration: 2
        });

        // 使用标准格式更新模特面板的蒙版数据
        const standardMaskData = {
          hasMask: true,
          maskPath: serverFilePath,
          maskFileName: fileName
        };

        // 根据当前标签页和面板类型更新对应的面板数组
        if (controlPanelTab === 'front') {
          setModelPanels(prevPanels =>
            prevPanels.map(panel =>
              panel.componentId === panelId
                ? { ...panel, ...standardMaskData }
                : panel
            )
          );
        } else {
          // 更新第二个标签页的面板 - 需要判断是模特面板还是服装面板
          // 先检查模特面板
          const modelPanel = otherModelPanels.find(panel => panel.componentId === panelId);
          if (modelPanel) {
            setOtherModelPanels(prevPanels =>
              prevPanels.map(panel =>
                panel.componentId === panelId
                  ? { ...panel, ...standardMaskData }
                  : panel
              )
            );
          } else {
            // 检查服装面板
            const clothingPanel = otherClothingPanels.find(panel => panel.componentId === panelId);
            if (clothingPanel) {
              setOtherClothingPanels(prevPanels =>
                prevPanels.map(panel =>
                  panel.componentId === panelId
                    ? { ...panel, ...standardMaskData }
                    : panel
                )
              );
            }
          }
        }

        // 更新提示内容
        setTipContent(MASK_SAVED_TIP);
      } else {
        message.error({
          content: data.message || '服务器拒绝了蒙版保存请求',
          key: 'maskSave',
          duration: 3
        });
        console.error('蒙版上传失败:', data);
      }
    } catch (error) {
      console.error('处理蒙版数据时出错:', error);
      message.error({
        content: '处理蒙版数据时出错',
        key: 'maskSave',
        duration: 3
      });
    }
  };

  // 辅助函数: Base64转Blob
  const base64ToBlob = (base64, mimeType) => {
    const byteCharacters = atob(base64);
    const byteArrays = [];

    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
      const slice = byteCharacters.slice(offset, offset + 512);

      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }

      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }

    return new Blob(byteArrays, { type: mimeType });
  };

  // 当任务成功查询时的处理
  const handleImageGenerated = (images) => {
    if (!images || images.length === 0) {
      console.warn('没有可用的生成图片');
      return;
    }

    console.log('处理生成的图片:', images);

    // 更新生成图片状态
    setGeneratedImages(images);
    setIsProcessing(false);

    // 如果当前没有显示的图片，则选择第一张显示
    if (!selectedImage && images.length > 0) {
      const firstImage = images[0];
      setSelectedImage(firstImage);
    }

    message.success('图片生成成功');
  };

  // 直接从任务对象提取图片信息
  const extractImagesFromTask = (task) => {
    // 基本输入验证
    if (!task) {
      console.warn('提取图片失败：任务对象为空');
      return [];
    }

    try {
      // 只处理标准数据结构：generatedImages字段
      if (task.generatedImages && Array.isArray(task.generatedImages)) {
        const images = task.generatedImages
          .filter(img => img && img.url) // 过滤掉空值和没有URL的项
          .map(img => standardizeImageFormat(img, task));

        if (images.length > 0) {
          console.log(`成功从generatedImages提取了${images.length}张图片`);
          return images;
        }
      }

      // 如果没有找到图片，返回空数组
      console.warn('任务中没有找到有效的图片数据');
      return [];
    } catch (error) {
      console.error('提取图片时发生错误:', error);
      return [];
    }
  };

  // 标准化图片格式，确保包含所有必要字段
  const standardizeImageFormat = (image, task) => {
    // 获取组件数据，只处理数组格式
    const components = Array.isArray(task.components) ? task.components : [];

    // 查找种子组件
    const seedComponent = components.find(c => c.componentType === 'randomSeedSelector');

    // 确定种子值 - 按优先级：图片中的种子 > 任务中的种子 > 组件中的种子 > 默认值
    const seedValue = image.seed !== undefined ? image.seed : (
      task.seed !== undefined ? task.seed : (
        seedComponent?.value || -1
      )
    );

    // 确保URL格式正确
    const imageUrl = formatImageUrl(image.url);

    // 返回标准化的图片对象
    return {
      ...image,
      url: imageUrl,
      taskId: task.taskId,
      pageType: task.pageType || 'try-on',
      createdAt: image.createdAt || task.createdAt || new Date(),
      seed: seedValue,
      // 确保有status字段
      status: image.status || 'completed'
    };
  };

  // 格式化图片URL
  const formatImageUrl = (url) => {
    if (!url) return '';

    // 如果已经是完整URL，直接返回
    if (url.startsWith('http')) return url;

    // 如果是相对路径，构建完整URL
    const baseUrl = process.env.REACT_APP_BACKEND_URL;

    // 确保路径始终以斜杠开头
    const path = url.startsWith('/') ? url : `/${url}`;

    return `${baseUrl}${path}`;
  };

  // 修改任务状态更新用于任务列表显示 - 使用标准模式
  const updateTaskStatus = useCallback((taskId, status, images = null, errorMessage = null) => {
    console.log(`更新任务 ${taskId} 状态为 ${status}`);

    if (!taskId) {
      console.warn('无法更新任务状态: 任务ID无效');
      return;
    }

    setGenerationTasks(prev => {
      // 找到任务
      const taskIndex = prev.findIndex(t => t.taskId === taskId);

      if (taskIndex === -1) {
        console.warn(`未找到任务: ${taskId}`);
        return prev;
      }

      // 创建更新后的任务数组 - 不直接修改原数组
      const updatedTasks = [...prev];

      // 使用标准状态更新模式 - 使用展开操作符构建新对象
      updatedTasks[taskIndex] = {
        ...updatedTasks[taskIndex],
        status,
        // 只在提供错误信息时添加
        ...(errorMessage && { errorMessage }),
        // 只在提供图片时更新
        ...(images && Array.isArray(images) && images.length > 0 && {
          generatedImages: images,
          imageCount: images.length
        }),
        // 使用标准格式的进度信息
        progress: status === 'completed'
          ? { step: 2, total: 2, percentage: 100, text: '处理完成' }
          : status === 'failed'
            ? { step: 2, total: 2, percentage: 0, text: '处理失败' }
            : { step: 1, total: 2, percentage: 50, text: '处理中...' }
      };

      return updatedTasks;
    });
  }, []);

  // 任务面板渲染
  const renderTaskPanel = useMemo(() => {
    // 确保有任务可显示
    if (!generationTasks || generationTasks.length === 0) {
      return (
        <div className="empty-tasks">
          <p>{isLoadingTasks ? "加载中..." : "暂无模特换装历史记录"}</p>
        </div>
      );
    }

    // 返回所有任务的列表
    return (
      <div className="task-list">
        {generationTasks.map(task => (
          <TaskPanel
            key={task.taskId}
            task={task}
            onDelete={() => handleDeleteTask(task.taskId)}
            onEdit={() => handleEditTask(task)}
            onViewDetails={(image, index) => handleViewDetails(image, task, index)}
            onBatchDownload={() => handleBatchDownload(task)}
            pageType="try-on"
            formatTaskItem={(taskItem) => ({
              id: taskItem.taskId,
              taskId: taskItem.taskId,
              status: taskItem.status,
              progress: taskItem.progress || { step: 0, total: 0, percentage: 0, message: '' },
              createdAt: taskItem.createdAt,
              generatedImages: taskItem.generatedImages || [],
              components: Array.isArray(taskItem.components) ? taskItem.components : [],
              imageCount: taskItem.imageCount || 0,
            })}
          />
        ))}
      </div>
    );
  }, [generationTasks, isLoadingTasks]);

  // 使用RequireLogin组件包装整个页面内容
  return (
    <>
      <RequireLogin isLoggedIn={isLoggedIn} featureName="模特换装功能">
        <PromptIfUnsaved when={hasUnsavedChanges} message="操作区的内容将丢失，确定要离开吗？（不影响生成中的任务）" />
        <div className="try-on-page">
          <div className="try-on-container" ref={containerRef}>
            <ControlPanel
              ref={controlPanelRef}
              width={`${controlPanelWidth}%`}
              activeTab={controlPanelTab}
              onTabChange={setControlPanelTab}
              onGenerate={handleGenerate}
              disabled={isProcessing}
              featureName="try-on"
              quantity={imageQuantity}
              tabs={[
                { key: 'front', label: '半自动蒙版' },
                { key: 'other', label: '手动蒙版' }
              ]}
            >
              {/* 半自动蒙版标签页内容 */}
              {controlPanelTab === 'front' && (
                <>
                  {/* 服装上传区域或服装面板 - 始终位于最上方 */}
                  {clothingPanels.length === 0 ? (
                    <UploadBox
                      id="clothing-upload-box"
                      onUpload={handleFileUpload}
                      onShowGuide={() => setShowUploadGuide(true)}
                      onUploadResult={handleUploadResult}
                      panels={clothingPanels}
                      className="mt-2"
                      showSupportTag={false}
                      pageType="try-on"
                      uploadType="clothing"
                    />
                  ) : (
                    // 展示服装面板
                    clothingPanels.map((panel) => (
                      <ClothingPanel
                        key={panel.componentId}
                        panel={panel}
                        onExpandClick={(panel, position) => {
                          // 确保面板对象完整
                          const enhancedPanel = {
                            ...panel,
                            isEnhancedClothingPanel: true,
                            processedFile: panel.processedFile || panel.preview || panel.url || panel.image
                          };

                          setOperationsPanel({
                            panel: enhancedPanel,
                            position
                          });
                        }}
                        onDelete={handleDeleteClothingPanel}
                        onReupload={handleReuploadClothing}
                        onStatusChange={(newStatus) => handleClothingStatusChange(panel.componentId, newStatus)}
                        isActive={panel.status === 'completed'}
                        onPanelsChange={setClothingPanels}
                        pageType="try-on"
                      />
                    ))
                  )}

                  {/* 服装蒙版描述面板 - 位于服装面板下方 */}
                  {clothingPanels.length > 0 && (
                    <MaskDescriptionPanel
                      panel={clothingMaskPanel}
                      onPanelsChange={handleMaskPanelChange}
                    />
                  )}

                  {/* 模特上传区域或模特面板 */}
                  {modelPanels.length === 0 ? (
                    <UploadBox_Model
                      id="model-upload-box"
                      onUpload={handleModelFileUpload}
                      onShowGuide={() => setShowModelUploadGuide(true)}
                      onUploadResult={handleModelUploadResult}
                      modelPanels={modelPanels}
                      className="mt-2"
                      showSupportTag={false}
                    />
                  ) : (
                    // 展示模特面板
                    // 注意：模特面板有两种工作模式：
                    // 1. 自动蒙版模式 - hasMask=false - 不需要手动绘制蒙版，使用tryonauto工作流
                    // 2. 手动蒙版模式 - hasMask=true - 用户手动绘制蒙版，使用tryonmanual工作流
                    modelPanels.map((panel) => (
                      <ModelMaskPanel
                        key={panel.componentId}
                        panel={panel}
                        onExpandClick={(panel, position) => {
                          const enhancedPanel = {
                            ...panel,
                            // 标记为增强的款式图面板以避免不必要的警告
                            isEnhancedModelPanel: true,
                            processedFile: panel.processedFile || panel.preview || panel.url || panel.image,
                            fileInfo: panel.fileInfo ? {
                              ...panel.fileInfo,
                              // 确保size属性是数字类型
                              size: typeof panel.fileInfo.size === 'string' ?
                                parseFloat(panel.fileInfo.size.replace(/[^\d.]/g, '')) :
                                panel.fileInfo.size
                            } : {
                              size: 500000, // 默认500KB
                              format: 'image/jpeg',
                              type: 'image/jpeg'
                            }
                          };


                          setOperationsPanel({
                            panel: enhancedPanel,
                            position
                          });
                        }}
                        onDelete={handleDeleteModelPanel}
                        onReupload={handleReuploadModel}
                        onStatusChange={(newStatus) => handleModelStatusChange(panel.componentId, newStatus)}
                        onDrawMask={handleDrawMask}
                        isActive={panel.status === 'completed'}
                        onPanelsChange={setModelPanels}
                        pageType="try-on"
                      />
                    ))
                  )}

                  {/* 模特蒙版描述面板 - 仅在自动蒙版（无手动蒙版）时显示 */}
                  {modelPanels.length > 0 && (!modelPanels[0].hasMask) && (
                    <MaskDescriptionPanel
                      panel={modelMaskPanel}
                      onPanelsChange={handleMaskPanelChange}
                    />
                  )}

                  {/* 蒙版扩张组件 - 仅在自动蒙版模式时显示 */}
                  {modelPanels.length > 0 && !modelPanels[0].hasMask && (
                    <MaskExpander
                      expandValue={maskExpandValue}
                      onChange={handleMaskExpandChange}
                    />
                  )}

                  {/* 提示信息面板 */}
                  {showTips && (
                    <TipsPanel
                      tipContent={controlPanelTab === 'other' ? MASK_SAVED_TIP : tipContent}
                    />
                  )}

                  {/* 随机种子选择器 */}
                  <RandomSeedSelector
                    onRandomChange={setUseRandomSeed}
                    onSeedChange={setSeed}
                    defaultRandom={useRandomSeed}
                    defaultSeed={seed}
                    // 编辑模式下传递历史种子
                    isEdit={selectedImage !== null}
                    editSeed={selectedImage?.seed || null}
                  />

                  {/* 数量面板 */}
                  <QuantityPanel
                    imageQuantity={imageQuantity}
                    onChange={setImageQuantity}
                  />
                </>
              )}

              {/* 手动蒙版标签页内容 */}
              {controlPanelTab === 'other' && (
                <>
                  {/* 服装上传区域或服装面板 - 始终位于最上方 */}
                  {otherClothingPanels.length === 0 ? (
                    <UploadBox
                      id="clothing-upload-box-other"
                      onUpload={handleOtherClothingFileUpload}
                      onShowGuide={() => setShowUploadGuide(true)}
                      onUploadResult={handleOtherClothingUploadResult}
                      panels={otherClothingPanels}
                      className="mt-2"
                      showSupportTag={false}
                      pageType="try-on"
                      uploadType="clothing"
                    />
                  ) : (
                    // 展示服装面板
                    otherClothingPanels.map((panel) => (
                      <ClothingMaskPanel
                        key={panel.componentId}
                        panel={panel}
                        onExpandClick={(panel, position) => {
                          const enhancedPanel = {
                            ...panel,
                            type: panel.type || 'clothing', // 确保type属性被保留
                            isEnhancedModelPanel: true,
                            processedFile: panel.processedFile || panel.preview || panel.url || panel.image,
                            fileInfo: panel.fileInfo ? {
                              ...panel.fileInfo,
                              size: typeof panel.fileInfo.size === 'string' ?
                                parseFloat(panel.fileInfo.size.replace(/[^\d.]/g, '')) :
                                panel.fileInfo.size
                            } : {
                              size: 500000,
                              format: 'image/jpeg',
                              type: 'image/jpeg'
                            }
                          };
                          setOperationsPanel({
                            panel: enhancedPanel,
                            position
                          });
                        }}
                        onDelete={handleDeleteOtherClothingPanel}
                        onReupload={handleReuploadOtherClothing}
                        onStatusChange={handleOtherClothingStatusChange}
                        onDrawMask={handleDrawMask}
                        onPanelsChange={setOtherClothingPanels}
                        isEnhanced={true}
                        pageType="try-on-other"
                      />
                    ))
                  )}

                  {/* 模特图片上传区域 */}
                  {otherModelPanels.length === 0 ? (
                    <UploadBox_Model
                      id="model-upload-box-other"
                      onUpload={handleOtherModelFileUpload}
                      onShowGuide={() => setShowModelUploadGuide(true)}
                      onUploadResult={handleOtherModelUploadResult}
                      modelPanels={otherModelPanels}
                      className="mt-2"
                      showSupportTag={false}
                    />
                  ) : (
                    otherModelPanels.map((panel) => (
                      <ModelMaskPanel
                        key={panel.componentId}
                        panel={panel}
                        onExpandClick={(panel, position) => {
                          const enhancedPanel = {
                            ...panel,
                            type: panel.type || 'model', // 确保type属性被保留
                            isEnhancedModelPanel: true,
                            processedFile: panel.processedFile || panel.preview || panel.url || panel.image,
                            fileInfo: panel.fileInfo ? {
                              ...panel.fileInfo,
                              size: typeof panel.fileInfo.size === 'string' ?
                                parseFloat(panel.fileInfo.size.replace(/[^\d.]/g, '')) :
                                panel.fileInfo.size
                            } : {
                              size: 500000,
                              format: 'image/jpeg',
                              type: 'image/jpeg'
                            }
                          };
                          setOperationsPanel({
                            panel: enhancedPanel,
                            position
                          });
                        }}
                        onDelete={handleDeleteOtherModelPanel}
                        onReupload={handleReuploadOtherModel}
                        onStatusChange={handleOtherModelStatusChange}
                        onDrawMask={handleDrawMask}
                        onPanelsChange={setOtherModelPanels}
                        isEnhanced={true}
                        pageType="try-on-other"
                      />
                    ))
                  )}

                  {/* 提示信息面板 - 只在上传服装原图后显示 */}
                  {otherModelPanels.length > 0 && (
                    <TipsPanel
                      tipContent={controlPanelTab === 'other' ? MASK_SAVED_TIP : tipContent}
                    />
                  )}

                  {/* 随机种子选择器 */}
                  <RandomSeedSelector
                    onRandomChange={setUseRandomSeed}
                    onSeedChange={setSeed}
                    defaultRandom={useRandomSeed}
                    defaultSeed={seed}
                    // 查看详情时显示历史种子但保持可编辑
                    isEdit={false}
                    editSeed={selectedImage?.seed || null}
                  />

                  {/* 数量面板 */}
                  <QuantityPanel
                    imageQuantity={imageQuantity}
                    onChange={setImageQuantity}
                  />
                </>
              )}
            </ControlPanel>

            <ResizeHandle
              ref={handleRef}
              containerRef={containerRef}
              onResize={setControlPanelWidth}
              minWidth={25}
              maxWidth={50}
            />

            <GenerationArea
              ref={generationAreaRef} setIsProcessing={setIsProcessing}

              activeTab={activeTab}
              onTabChange={setActiveTab}
              tasks={Array.isArray(generationTasks) ? generationTasks : []}
              onEditTask={handleEditTask}
              onDownloadImage={handleDownloadImage}
              onViewDetails={handleViewDetails}
              onBatchDownload={handleBatchDownload}
              onDeleteTask={handleDeleteTask}
              pageType="try-on"
            />
          </div>

          {/* 服装上传指导弹窗 */}
          {showUploadGuide && (
            <UploadGuideModal
              type="clothing"
              pageType="try-on"
              onClose={() => setShowUploadGuide(false)}
              onUpload={(result) => {
                console.log('收到上传结果:', result);

                // 根据当前激活的标签页选择对应的处理函数
                if (controlPanelTab === 'front') {
                  handleUploadResult(result);
                } else {
                  handleOtherClothingUploadResult(result);
                }

                // 根据结果中的shouldClose字段决定是否关闭弹窗
                if (result.shouldClose !== false) {
                  setShowUploadGuide(false);
                }
              }}
            />
          )}

          {/* 模特上传指导弹窗 */}
          {showModelUploadGuide && (
            <UploadGuideModal
              type="model"
              pageType="try-on"
              onClose={() => setShowModelUploadGuide(false)}
              onUpload={(result) => {
                console.log('收到模特上传结果:', result);

                // 根据当前激活的标签页选择对应的处理函数
                if (controlPanelTab === 'front') {
                  handleModelUploadResult(result);
                } else {
                  handleOtherModelUploadResult(result);
                }

                // 根据结果中的shouldClose字段决定是否关闭弹窗
                if (result.shouldClose !== false) {
                  setShowModelUploadGuide(false);
                }
              }}
            />
          )}

          {/* 添加操作弹窗 */}
          {operationsPanel && (
            <ImageInfoModal
              panel={operationsPanel.panel}
              position={operationsPanel.position}
              onClose={() => setOperationsPanel(null)}
              onDelete={
                controlPanelTab === 'front'
                  ? (operationsPanel.panel.type === 'model' ? handleDeleteModelPanel : handleDeleteClothingPanel)
                  : (operationsPanel.panel.type === 'model' ? handleDeleteOtherModelPanel : handleDeleteOtherClothingPanel)
              }
              onReupload={
                controlPanelTab === 'front'
                  ? (operationsPanel.panel.type === 'model' ? handleReuploadModel : handleReuploadClothing)
                  : (operationsPanel.panel.type === 'model' ? handleReuploadOtherModel : handleReuploadOtherClothing)
              }
              onDrawMask={handleDrawMask}
              pageType="try-on"
              activeTab={controlPanelTab}
            />
          )}

          {/* 添加 ImageDetailsModal 组件 - 使用懒加载 */}
          {showImageDetails && selectedImage ? (
            <MemoizedImageDetailsModal
              selectedImage={selectedImage}
              onClose={handleCloseImageDetails}
              generationTasks={generationTasks}
              onEditTask={handleEditTask}
              pageType="try-on"
              imageDetailsTask={imageDetailsTask}
            />
          ) : null}



          {/* 显示上传的服装图片和生成按钮 */}
          {uploadedClothingUrl && !isProcessing && (
            <div className="action-buttons">
              <Button
                type="primary"
                onClick={handleGenerate}
                disabled={isProcessing}
              >
                开始生成
              </Button>
            </div>
          )}

          {/* 显示生成中的加载状态 */}
          {isProcessing && (
            <div className="generating-status">
              <Spin size="large" />
              <p>正在生成中，请稍候...</p>
            </div>
          )}

          {/* 蒙版绘制弹窗 */}
          {showMaskDrawModal && currentMaskPanel && (
            <MaskDrawModal
              isOpen={showMaskDrawModal}
              panel={currentMaskPanel}
              onClose={() => setShowMaskDrawModal(false)}
              onSaveMask={handleSaveMask}
              pageType="try-on"
              savePath={`server/storage/${userId || getCurrentUserId() || 'developer'}/model/try-on/mask`}
              referenceImage={getReferenceImage(currentMaskPanel)}
            />
          )}
        </div>
      </RequireLogin>
    </>
  );
};

// 使用memo包装ImageDetailsModal组件以提高性能
const MemoizedImageDetailsModal = memo(ImageDetailsModal);

export default TryOnPage; 