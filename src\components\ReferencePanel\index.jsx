import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { message } from 'antd';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';

/**
 * 参考图面板组件
 * 用于显示已上传的参考图片
 * 使用统一的panels.css样式
 */
const ReferencePanel = ({ 
  panel, 
  onDelete, 
  onReupload, 
  onExpandClick, 
  isActive,
  onPanelsChange 
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const { componentId, title, status, serverFileName, originalImage, url, fileInfo, error } = panel;

  // 处理拖拽进入
  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  // 处理拖拽离开
  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // 只有当离开整个组件时才重置状态
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
    }
  };

  // 处理拖拽悬停
  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // 确保拖拽悬停时保持状态
    if (!isDragOver) {
      setIsDragOver(true);
    }
  };

  // 处理拖拽放下
  const handleDrop = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
    
    // 首先检查是否有自定义的拖拽数据（从TaskPanel拖拽过来的图片）
    try {
      const customData = e.dataTransfer.getData('application/json');
      if (customData) {
        const dragData = JSON.parse(customData);
        if (dragData.type === 'image-upload') {
          // 处理从TaskPanel拖拽过来的图片
          await handleImageFromTaskPanel(dragData);
          return;
        }
      }
    } catch (error) {
      console.log('解析拖拽数据失败，尝试处理文件:', error);
    }
    
    // 获取拖拽的文件
    const files = Array.from(e.dataTransfer.files);
    
    if (files.length > 0) {
      // 先删除当前面板
      if (panel && panel.componentId) {
        onDelete?.(panel.componentId);
        message.success('已删除参考图面板');
      }
      
      // 延迟一点时间后处理拖拽的文件
      setTimeout(() => {
        // 处理第一个文件
        const file = files[0];
        if (file && file.type.startsWith('image/')) {
          // 直接处理文件上传，创建新的面板
          const imageId = `reference-${Date.now()}`;
          const imageUrl = URL.createObjectURL(file);
          
          const newPanel = {
            componentId: imageId,
            title: '参考图',
            status: 'completed',
            serverFileName: file.name,
            url: imageUrl,
            processedFile: imageUrl, // 添加processedFile属性用于显示图片
            fileInfo: {
              name: file.name,
              size: file.size,
              type: file.type,
              serverFileName: file.name
            },
            type: 'reference',
            file: file,
            processInfo: {
              serverFileName: file.name
            }
          };
          
          // 通知父组件创建新面板
          onPanelsChange([newPanel]);
          message.success('图片上传成功');
        } else {
          message.error('请拖拽图片文件');
        }
      }, 50);
    } else {
      // 如果没有文件，只执行删除操作
      if (panel && panel.componentId) {
        onDelete?.(panel.componentId);
        message.success('已删除参考图面板');
      }
    }
  };

  // 处理从TaskPanel拖拽过来的图片
  const handleImageFromTaskPanel = async (dragData) => {
    try {
      const { imageUrl, fileName, taskId, imageIndex } = dragData;
      
      // 先删除当前面板
      if (panel && panel.componentId) {
        onDelete?.(panel.componentId);
        message.success('已删除参考图面板');
      }
      
      // 延迟一点时间后处理拖拽的图片
      setTimeout(async () => {
        try {
          // 将HTTP转换为HTTPS，解决混合内容问题
          const httpsUrl = imageUrl.replace(/^http:/, 'https:');
          
          // 从URL获取图片数据
          const response = await fetch(httpsUrl);
          if (!response.ok) {
            throw new Error('无法获取图片数据');
          }
          
          const blob = await response.blob();
          
          // 创建一个File对象
          const file = new File([blob], fileName, {
            type: blob.type || 'image/jpeg',
            lastModified: Date.now()
          });
          
          console.log(`处理从任务面板拖拽的图片: ${fileName}, 任务ID: ${taskId}, 图片索引: ${imageIndex}`);
          
          // 创建新的面板
          const imageId = `reference-${Date.now()}`;
          const newImageUrl = URL.createObjectURL(file);
          
          const newPanel = {
            componentId: imageId,
            title: '参考图',
            status: 'completed',
            serverFileName: fileName,
            url: newImageUrl,
            processedFile: newImageUrl, // 添加processedFile属性用于显示图片
            fileInfo: {
              name: fileName,
              size: file.size,
              type: file.type,
              serverFileName: fileName
            },
            type: 'reference',
            file: file,
            processInfo: {
              serverFileName: fileName
            }
          };
          
          // 通知父组件创建新面板
          onPanelsChange([newPanel]);
          message.success('参考图重新上传成功');
          
        } catch (error) {
          console.error('处理拖拽图片时出错:', error);
          message.error('处理图片失败，请重试');
        }
      }, 50);
      
    } catch (error) {
      console.error('处理从TaskPanel拖拽的图片时出错:', error);
      message.error('处理图片失败，请重试');
    }
  };
  
  const handleExpandClick = (e) => {
    if (onExpandClick) {
      const panelWithType = {
        ...panel,
        type: 'reference'
      };
      
      const buttonRect = e.currentTarget.getBoundingClientRect();
      onExpandClick(panelWithType, {
        top: buttonRect.top,
        left: buttonRect.left + buttonRect.width
      });
    }
  };
  
  return (
    <div 
      className={`panel-component ${isDragOver ? 'drag-over' : ''}`}
      key={componentId}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <div className="component-header">
        <div className="component-info">
          <ThumbnailWithPreview
            imageUrl={url}
            alt="参考图片"
            status={status}
            error={error}
            featureName="参考图预览"
            transparentBg={true}
          />
          <div className="component-text">
            <h3>{title || "参考图"}</h3>
            <div className="component-content">
              <p>
                {status === 'completed' && '上传完成'}
                {status === 'processing' && '处理中...'}
                {status === 'error' && error}
              </p>
            </div>
          </div>
        </div>
        <button 
          className={`expand-btn ${isActive ? 'active' : ''}`}
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

ReferencePanel.propTypes = {
  panel: PropTypes.shape({
    componentId: PropTypes.string.isRequired,
    title: PropTypes.string,
    status: PropTypes.oneOf(['processing', 'completed', 'error']),
    serverFileName: PropTypes.string,
    originalImage: PropTypes.string,
    url: PropTypes.string,
    fileInfo: PropTypes.shape({
      size: PropTypes.number,
      width: PropTypes.number,
      height: PropTypes.number,
      format: PropTypes.string,
      serverFileName: PropTypes.string
    }),
    error: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
    type: PropTypes.string
  }).isRequired,
  onDelete: PropTypes.func,
  onReupload: PropTypes.func,
  onExpandClick: PropTypes.func,
  isActive: PropTypes.bool,
  onPanelsChange: PropTypes.func
};

export default ReferencePanel; 