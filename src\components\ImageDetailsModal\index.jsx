import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
// 触发热重载更新 - 修复 isDefaultEmptyImage 函数作用域问题
import { MdClose, MdOutlineZoomOutMap, MdOutlineDescription, MdContentCopy, MdChevronLeft, MdChevronRight } from 'react-icons/md';
import { message, Modal, Row, Col, Button, Divider, Tooltip } from 'antd';
import './index.css';
import '../../styles/close-buttons.css';
import { getTaskComponent } from '../../utils/taskAdapters';

// 添加全局样式，只统一加载指示器的动画效果，保持原有样式
const globalStyles = `
  .spinner {
    animation: spin 1s linear infinite;
  }
`;

import ThumbnailList from '../ThumbnailList';
import ImageZoomControl from '../ImageZoomControl';
import ImageNavigator from '../ImageNavigator';
import TextPopup from '../TextPopup';
import ModelRegistrationModal from '../ModelRegistrationModal';
import ResizeHandle from '../ResizeHandle';
import ReferencePanel from '../ReferencePanel'; // 添加导入ReferencePanel组件
import { formatFileSize } from '../../utils/format';
import { getActualImageInfo } from '../../utils/imageUtils';
import { 
  DownloadOutlined, 
  ShareAltOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  CopyOutlined 
} from '@ant-design/icons';
import ImagePreviewModal from '../common/ImagePreviewModal';

const ImageDetailsModal = ({
  selectedImage: initialSelectedImage,
  onClose,
  generationTasks,
  onEditTask,
  pageType = 'fashion' // 默认为时尚大片页面类型
}) => {
  // 图片详情相关状态
  const [selectedImage, setSelectedImage] = useState(() => {
    // 添加初始加载标记
    if (initialSelectedImage) {
      return {
        ...initialSelectedImage,
        _isInitialRender: true // 添加标记以识别首次渲染
      };
    }
    return initialSelectedImage;
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [imageZoomed, setImageZoomed] = useState(false);
  const [imagesLoaded, setImagesLoaded] = useState({
    original: false,
    result: false
  });
  const [imageUrls, setImageUrls] = useState({
    original: null,
    result: null
  });
  const [dragging, setDragging] = useState(false);
  const [showModelText, setShowModelText] = useState(false);
  const [showSceneText, setShowSceneText] = useState(false);
  const [showAdvancedText, setShowAdvancedText] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  const [previewImages, setPreviewImages] = useState([]);
  const [currentPreviewIndex, setCurrentPreviewIndex] = useState(0);
  
  // 添加真实图片信息状态
  const [actualFileInfo, setActualFileInfo] = useState(null);
  const [isLoadingFileInfo, setIsLoadingFileInfo] = useState(false);
  
  // 模特登记弹窗状态
  const [showModelRegistration, setShowModelRegistration] = useState(false);
  
  // 图片拖动相关状态
  const [isDragging, setIsDragging] = useState(false);
  const dragStart = useRef({ x: 0, y: 0 });
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [imageScale, setImageScale] = useState(100);
  const [initialScale, setInitialScale] = useState(100);

  // 添加图片比例调整相关状态
  const [compareWidthRatio, setCompareWidthRatio] = useState(50); // 默认原图占50%宽度
  const compareContainerRef = useRef(null);
  const resizeHandleRef = useRef(null);

  // 添加检查左侧图片数量的函数
  const checkLeftImagesCount = useCallback(() => {
    if (pageType === 'trending') {
      const patternUrl = getTaskComponent(selectedImage, 'patternReferencePanel.url');
      const patternComponent = getTaskComponent(selectedImage, 'patternReferencePanel');
      const printingUrl = getTaskComponent(selectedImage, 'printingReferencePanel.url');
      const printingComponent = getTaskComponent(selectedImage, 'printingReferencePanel');
      
      // 判断是否为空白图（使用与渲染逻辑相同的判断方式）
      const isPatternEmpty = isDefaultEmptyImage(patternUrl, patternComponent);
      const isPrintingEmpty = isDefaultEmptyImage(printingUrl, printingComponent);
      
      // 确定有效的图片（非空白图）
      const hasValidPattern = patternUrl && !isPatternEmpty;
      const hasValidPrinting = printingUrl && !isPrintingEmpty;
      
      // 如果两张图都是有效的，设置宽度比例为40%（4:6）
      if (hasValidPattern && hasValidPrinting) {
        setCompareWidthRatio(40);
      } else {
        // 如果只有一张图或没有图，保持默认的50%（1:1）
        setCompareWidthRatio(50);
      }
    }
  }, [selectedImage, pageType]);

  // 在图片加载完成后检查左侧图片数量
  useEffect(() => {
    if (imagesLoaded.original) {
      checkLeftImagesCount();
    }
  }, [imagesLoaded.original, checkLeftImagesCount]);

  // Refs
  const imageRef = useRef(null);
  const lastPosition = useRef({ x: 0, y: 0 });
  const modalContentRef = useRef(null);
  const resultContainerRef = useRef(null); // 添加结果图容器引用
  const resultImageRef = useRef(null); // 添加结果图元素引用

  // 新的状态
  const [modelPopupPosition, setModelPopupPosition] = useState({ left: 0, top: 0 });
  const [scenePopupPosition, setScenePopupPosition] = useState({ left: 0, top: 0 });
  const [advancedPopupPosition, setAdvancedPopupPosition] = useState({ left: 0, top: 0 });

  // 状态管理 - 图片比较滑块位置
  const [sliderPosition, setSliderPosition] = useState(50);
  // 图片变换状态
  const [scale, setScale] = useState(1); // 添加缺失的缩放状态
  const [translateX, setTranslateX] = useState(0); // 添加缺失的X轴位移状态
  const [translateY, setTranslateY] = useState(0); // 添加缺失的Y轴位移状态
  // 用于缓存上一次请求的URL，避免重复请求
  const prevCacheKeyRef = useRef(null);

  // 添加缺失的工具函数
  // 获取图片尺寸
  const getImageSize = (image) => {
    if (!image) return '未知';
    const fileInfo = image.fileInfo || {};
    return fileInfo.width && fileInfo.height ? `${fileInfo.width} × ${fileInfo.height}` : '未知';
  };

  // 获取图片种子
  const getImageSeed = (image) => {
    return image?.seed || '未知';
  };

  // 获取任务ID
  const getTaskId = (task) => {
    return task?.taskId || '未知';
  };

  // 获取结果图URL
  const getResultImageUrl = (image) => {
    return image?.url || '';
  };

  // 获取原图URL
  const getOriginalImageUrl = (component) => {
    return component?.originalImage || component?.url || '';
  };

  // 图片加载处理
  const handleImageLoad = (e) => {
    const img = e.target;
    if (!img) return;

    // 计算初始缩放比例
    const container = img.parentElement;
    if (!container) return;

    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;

    // 计算适合容器的初始缩放比例（百分比）
    let initialScaleValue;
    if (imageNaturalWidth > containerWidth || imageNaturalHeight > containerHeight) {
      const widthRatio = containerWidth / imageNaturalWidth;
      const heightRatio = containerHeight / imageNaturalHeight;
      initialScaleValue = Math.round(Math.min(widthRatio, heightRatio) * 100);
    } else {
      initialScaleValue = 100;
    }

    setInitialScale(initialScaleValue);
    setImageScale(initialScaleValue);
  };

  // 防抖函数封装
  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  // 添加图片加载错误处理
  const handleImageError = useCallback((e) => {
    console.error('详情图片加载失败:', e.target.src);

    // 设置为透明占位图，避免显示破碎图标
    e.target.src = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';

    // 添加错误类
    e.target.classList.add('modal-image-error');

    // 添加错误提示（在父容器中添加，确保位置正确）
    const container = e.target.parentNode;
    if (container) {
      // 检查是否已存在错误提示，避免重复添加
      if (!container.querySelector('.modal-error-message')) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'modal-error-message';
        errorDiv.textContent = '图片加载失败 - 请检查图片路径或权限';
        container.appendChild(errorDiv);
      }
    }

    // 确保设置加载完成，这样界面不会一直处于加载状态
    setImagesLoaded(prev => ({ ...prev, result: true, original: true }));
  }, []);

  const handleClose = useCallback((e) => {
    if (e) {
      e.stopPropagation();
    }

    setShowModelText(false);
    setShowSceneText(false);
    setShowAdvancedText(false);
    setPreviewImage(null);
    onClose?.();
  }, [onClose]);

  const handleMouseDown = useCallback((e) => {
    if (e.target.tagName.toLowerCase() === 'img') {
      e.preventDefault();
      setIsDragging(true);
      dragStart.current = {
        x: e.clientX - imagePosition.x,
        y: e.clientY - imagePosition.y
      };
      e.target.classList.add('dragging');
    }
  }, [imagePosition]);

  const handleMouseMove = useCallback((e) => {
    if (isDragging) {
      const newX = e.clientX - dragStart.current.x;
      const newY = e.clientY - dragStart.current.y;

      // 使用requestAnimationFrame确保平滑更新
      requestAnimationFrame(() => {
        setImagePosition({ x: newX, y: newY });
      });
    }
  }, [isDragging]);

  const handleMouseUp = useCallback((e) => {
    if (isDragging) {
      setIsDragging(false);
      const img = e.currentTarget.querySelector('img');
      if (img) {
        img.classList.remove('dragging');
      }
      lastPosition.current = imagePosition;
    }
  }, [isDragging, imagePosition]);

  const handleMouseLeave = useCallback((e) => {
    if (isDragging) {
      setIsDragging(false);
      const img = e.currentTarget.querySelector('img');
      if (img) {
        img.classList.remove('dragging');
      }
      lastPosition.current = imagePosition;
    }
  }, [isDragging, imagePosition]);

  // 计算图片显示比例
  const calculateDisplayScale = useCallback((containerWidth, containerHeight, imageNaturalWidth, imageNaturalHeight) => {
    // 如果图片尺寸不合法，返回默认值
    if (!imageNaturalWidth || !imageNaturalHeight || !containerWidth || !containerHeight) {
      return 100; // 返回百分比，而不是小数
    }

    // 开发环境记录尺寸信息以便调试
    if (process.env.NODE_ENV === 'development') {
      console.log('ImageDetailsModal - Container dimensions:', containerWidth, 'x', containerHeight);
      console.log('ImageDetailsModal - Image natural dimensions:', imageNaturalWidth, 'x', imageNaturalHeight);
    }

    // 判断图片是否大于容器
    const imageExceedsContainer = imageNaturalWidth > containerWidth || imageNaturalHeight > containerHeight;

    if (imageExceedsContainer) {
      // 图片大于容器：计算缩小比例
      const widthScale = containerWidth / imageNaturalWidth;
      const heightScale = containerHeight / imageNaturalHeight;

      // 选择较小的缩放比例，确保图片完全可见
      const scale = Math.min(widthScale, heightScale);

      // 转换为百分比并四舍五入到整数
      const percentageScale = Math.round(scale * 100);

      // 开发环境记录计算结果
      if (process.env.NODE_ENV === 'development') {
        console.log('ImageDetailsModal - Image exceeds container, calculated scale:', scale);
        console.log('ImageDetailsModal - Adjusted displayScale (percentage):', percentageScale);
      }

      return percentageScale; // 返回缩小后的百分比值
    } else {
      // 图片小于或等于容器：按原尺寸显示
      if (process.env.NODE_ENV === 'development') {
        console.log('ImageDetailsModal - Image fits within container, using 100% scale');
      }

      return 100; // 返回100%，按原图尺寸显示
    }
  }, []);

  // 优化计算初始缩放比例的函数
  const calculateInitialScale = useCallback((img) => {
    if (!img) return 100; // 返回百分比，而不是小数
    const container = img.parentElement;
    if (!container) return 100; // 返回百分比，而不是小数

    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imageNaturalWidth = img.naturalWidth;
    const imageNaturalHeight = img.naturalHeight;

    return calculateDisplayScale(containerWidth, containerHeight, imageNaturalWidth, imageNaturalHeight);
  }, [calculateDisplayScale]);

  // 在函数组件内添加一个辅助函数来从components中获取数据
  const getTaskComponent = useCallback((task, componentPath) => {
    if (!task || !task.components) return undefined;

    const paths = componentPath.split('.');
    const componentType = paths[0];

    let component;
    if (Array.isArray(task.components)) {
      component = task.components.find(comp =>
        comp && comp.componentType === componentType
      );
    } else {
      component = task.components[componentType];
    }

    if (!component) return undefined;

    if (paths.length === 1) return component;

    // 处理嵌套属性
    let value = component;
    for (let i = 1; i < paths.length; i++) {
      if (value == null) return undefined;
      value = value[paths[i]];
      if (value === undefined) return undefined;
    }

    return value;
  }, []);

  // 获取服务器URL而非blob URL
  const getServerImageUrl = useCallback((task, componentPath) => {
    const component = getTaskComponent(task, componentPath);
    if (!component) return null;

    // 检查是否在开发环境中
    // 开发环境优先使用相对路径
    if (component.url && !component.url.startsWith('blob:')) {
      return component.url;
    }

    if (component.originalImage && !component.originalImage.startsWith('blob:')) {
      return component.originalImage;
    }

    // 最后才使用serverFileName构建URL
    if (component.serverFileName) {
      const baseUrl = process.env.REACT_APP_BACKEND_URL;
      // 去掉API 
      const apiUrl = baseUrl.replace('/api', '');
      return `${apiUrl}/storage/developer/uploads/${component.serverFileName}`;
    }
  

    return null;
  }, [getTaskComponent]);

  // 判断是否为空白图的辅助函数
  const isDefaultEmptyImage = useCallback((imageUrl, component) => {
    if (!imageUrl) return false;
    
    // 检查URL是否明显是空白图标识
    const urlIndicators = [
      'trending-01.png',
      'file.aibikini.cn/config/workflow/trending-01.png',
      '/images/workflow/trending-01.png' // 相对路径的空白图
    ];
    
    // 只以URL为准判断，URL明显包含空白图标识时才认为是空白图
    const isEmptyByUrl = urlIndicators.some(indicator => imageUrl.includes(indicator));
    
    // 添加调试信息
    console.log('isDefaultEmptyImage 调试:', {
      imageUrl,
      serverFileName: component?.serverFileName,
      isEmptyByUrl,
      result: isEmptyByUrl
    });
    
    return isEmptyByUrl;
  }, []);

  // 预加载图片
  useEffect(() => {
    if (selectedImage) {
      // 检查selectedImage是否有效
      if (!selectedImage || !selectedImage.components) {
        console.warn('ImageDetailsModal - 无效的selectedImage或缺少components属性', selectedImage);
        // 设置图片已加载，避免无限加载状态
        setImagesLoaded({
          original: true,
          result: true
        });
        return;
      }

      // 处理图片加载状态
    // 首次打开弹窗时，重置所有图片的加载状态
    // 切换图片时，只重置结果图的加载状态，保持原图的加载状态不变
    if (selectedImage._isInitialRender) {
      // 首次渲染，重置所有图片的加载状态
      setImagesLoaded({
        original: false,
        result: false
      });
      // 移除标记，后续切换图片时不再触发完全重置
      delete selectedImage._isInitialRender;
    } else {
      // 切换图片时，只重置结果图状态
      setImagesLoaded(prev => ({
        original: prev.original, // 保持原图的加载状态不变
        result: false // 只重置结果图的加载状态
      }));
    }

      // 使用URL获取助手函数简化代码
      const getOriginalImageUrl = () => {
        try {
          let url;
          switch (pageType) {
            case 'background':
              url = getServerImageUrl(selectedImage, 'foregroundPanel') ||
                   getTaskComponent(selectedImage, 'foregroundPanel.originalImage') ||
                   getTaskComponent(selectedImage, 'foregroundPanel.url');
              break;
            case 'matting':
              url = getServerImageUrl(selectedImage, 'sourceImagePanel') ||
                   getTaskComponent(selectedImage, 'sourceImagePanel.originalImage') ||
                   getTaskComponent(selectedImage, 'sourceImagePanel.url');
              break;
            case 'trending':
              // 爆款开发页面 - 使用patternReferencePanel(版型)和printingReferencePanel(印花)
              url = getServerImageUrl(selectedImage, 'patternReferencePanel') ||
                   getTaskComponent(selectedImage, 'patternReferencePanel.url') ||
                   getTaskComponent(selectedImage, 'patternReferencePanel.url') ||
                   getServerImageUrl(selectedImage, 'printingReferencePanel') ||
                   getTaskComponent(selectedImage, 'printingReferencePanel.url') ||
                   getTaskComponent(selectedImage, 'printingReferencePanel.url');
              break;
            case 'try-on':
            case 'hand-fix': // 新增 hand-fix 支持
            case 'recolor':
            case 'fabric':
            case 'change-model':
            case 'change-posture': // 新增 change-posture 支持
            case 'drawing': // 新增 drawing 支持
              url = getServerImageUrl(selectedImage, (pageType === 'change-model' || pageType === 'hand-fix' || pageType === 'change-posture' || pageType === 'divergent') ? 'modelMaskPanel' : 'clothingPanel') ||
                getTaskComponent(selectedImage, (pageType === 'change-model' || pageType === 'hand-fix' || pageType === 'change-posture' || pageType === 'divergent') ? 'modelMaskPanel.originalImage' : 'clothingPanel.originalImage') ||
                getTaskComponent(selectedImage, (pageType === 'change-model' || pageType === 'hand-fix' || pageType === 'change-posture' || pageType === 'divergent') ? 'modelMaskPanel.url' : 'clothingPanel.url');
              break;
            case 'optimize':
            case 'divergent':
              url = getServerImageUrl(selectedImage, 'modelMaskPanel') ||
                   getTaskComponent(selectedImage, 'modelMaskPanel.originalImage') ||
                   getTaskComponent(selectedImage, 'modelMaskPanel.url');
              break;
            case 'upscale':
            case 'extend':
              url = getServerImageUrl(selectedImage, 'sourceImagePanel') ||
                   getTaskComponent(selectedImage, 'sourceImagePanel.originalImage') ||
                   getTaskComponent(selectedImage, 'sourceImagePanel.url');
              break;
            case 'fashion':
            default:
              // 时尚大片页面或默认情况
              // 只查找参考图组件的图片路径
              url = getServerImageUrl(selectedImage, 'referencePanel') ||
                   getTaskComponent(selectedImage, 'referencePanel.originalImage') ||
                   getTaskComponent(selectedImage, 'referencePanel.url');
              break;
            case 'detail-migration':
              // 细节还原页面显示服装原图
              url = getServerImageUrl(selectedImage, 'clothingMaskPanel') ||
                   getTaskComponent(selectedImage, 'clothingMaskPanel.originalImage') ||
                   getTaskComponent(selectedImage, 'clothingMaskPanel.url');
              break;
          }

          // 如果获取到了URL，添加时间戳防止缓存
          if (url) {
            return `${url}`;
          }
          return url;
        } catch (error) {
          console.error('获取原图URL失败:', error);
          return null;
        }
      };

      // 获取图片URL，并添加时间戳防止缓存
      const originalImageUrl = getOriginalImageUrl();
      let resultImageUrl = selectedImage.url;

      // 为结果图片添加时间戳
      if (resultImageUrl) {
        const timestamp = Date.now();
        const separator = resultImageUrl.includes('?') ? '&' : '?';
        resultImageUrl = `${resultImageUrl}`;
      }

      // 记录URL信息用于调试
      setImageUrls({
        original: originalImageUrl,
        result: resultImageUrl
      });

      // 记录URL信息用于调试
      console.log('ImageDetailsModal - 图片加载信息:', {
        pageType,
        originalImageUrl,
        resultImageUrl,
        isOriginalBlob: originalImageUrl?.startsWith('blob:'),
        isResultBlob: resultImageUrl?.startsWith('blob:'),
        serverFileName: selectedImage.serverFileName,
        primaryImageFileName: selectedImage.primaryImageFileName
      });

      // 预加载原图 - 只有在原图加载状态为false时才重新加载
      if (originalImageUrl && !imagesLoaded.original) {
        const img = new Image();
        img.onload = () => setImagesLoaded(prev => ({ ...prev, original: true }));
        img.onerror = (e) => {
          console.error('原图预加载失败:', originalImageUrl, e);
          setImagesLoaded(prev => ({ ...prev, original: true }));
        };
        img.src = originalImageUrl;
      } else if (!originalImageUrl) {
        console.warn('原图URL不存在，跳过加载');
        setImagesLoaded(prev => ({ ...prev, original: true }));
      }

      // 预加载结果图
      if (resultImageUrl) {
        const img = new Image();
        img.onload = () => setImagesLoaded(prev => ({ ...prev, result: true }));
        img.onerror = () => setImagesLoaded(prev => ({ ...prev, result: true }));
        img.src = resultImageUrl;
      } else {
        setImagesLoaded(prev => ({ ...prev, result: true }));
      }
    }
  }, [selectedImage?.taskId, selectedImage?.imageIndex, pageType, getTaskComponent, getServerImageUrl, imagesLoaded.original]);

  const handleOriginalImageLoad = useCallback(() => {
    // 添加50ms延迟，确保加载指示器可以显示一小段时间
    // 这样用户能更清晰地看到加载过程，特别是对于加载很快的图片
    setTimeout(() => {
      setImagesLoaded(prev => ({ ...prev, original: true }));
    }, 50);
  }, []);

  // 处理结果图片加载完成
  const handleResultImageLoad = useCallback(() => {
    // 如果不是第一次加载，直接更新状态
    if (imagesLoaded.result) return;

    // 获取图片容器尺寸
    if (resultContainerRef.current) {
      const containerWidth = resultContainerRef.current.clientWidth;
      const containerHeight = resultContainerRef.current.clientHeight;

      // 获取图片实际尺寸
      if (resultImageRef.current) {
        const img = resultImageRef.current;
        const imageNaturalWidth = img.naturalWidth;
        const imageNaturalHeight = img.naturalHeight;

        // 计算初始缩放值（百分比）
        const initialScaleValue = calculateDisplayScale(
          containerWidth,
          containerHeight,
          imageNaturalWidth,
          imageNaturalHeight
        );

        // 开发环境记录日志
        if (process.env.NODE_ENV === 'development') {
          console.log('ImageDetailsModal - handleResultImageLoad - initialScaleValue:', initialScaleValue);
        }

        // 更新缩放状态 - 现在initialScaleValue已经是百分比值
        setInitialScale(initialScaleValue);  // 设置初始缩放比例
        setImageScale(initialScaleValue);    // 设置当前缩放比例
        setScale(initialScaleValue / 100);   // setScale仍然使用小数值
        setTranslateX(0);
        setTranslateY(0);

        // 开发环境记录日志
        if (process.env.NODE_ENV === 'development') {
          console.log('ImageDetailsModal - Updating state with initialScaleValue:', initialScaleValue);
        }
      }
    }

    // 添加50ms延迟，确保加载指示器可以显示一小段时间
    // 与原图加载保持一致的用户体验
    setTimeout(() => {
      // 标记结果图片已加载
      setImagesLoaded(prev => ({
        ...prev,
        result: true
      }));
    }, 50);
  }, [imagesLoaded.result, calculateDisplayScale]);

  const handleScaleChange = useCallback((newScale) => {
    setImageScale(newScale);
  }, []);

  const handleReset = useCallback(() => {
    setImageScale(initialScale);
    setImagePosition({ x: 0, y: 0 });
    lastPosition.current = { x: 0, y: 0 };
    
    // 根据页面类型和左侧图片数量决定重置后的宽度比例
    if (pageType === 'trending') {
      // 爆款开发页面：检查左侧图片数量
      const patternUrl = getTaskComponent(selectedImage, 'patternReferencePanel.url');
      const patternComponent = getTaskComponent(selectedImage, 'patternReferencePanel');
      const printingUrl = getTaskComponent(selectedImage, 'printingReferencePanel.url');
      const printingComponent = getTaskComponent(selectedImage, 'printingReferencePanel');
      
      // 判断是否为空白图
      const isPatternEmpty = isDefaultEmptyImage(patternUrl, patternComponent);
      const isPrintingEmpty = isDefaultEmptyImage(printingUrl, printingComponent);
      
      // 确定有效的图片（非空白图）
      const hasValidPattern = patternUrl && !isPatternEmpty;
      const hasValidPrinting = printingUrl && !isPrintingEmpty;
      
      // 如果两张图都是有效的，设置宽度比例为40%（4:6）
      if (hasValidPattern && hasValidPrinting) {
        setCompareWidthRatio(40);
      } else {
        // 如果只有一张图或没有图，保持默认的50%（1:1）
        setCompareWidthRatio(50);
      }
    } else {
      // 其他页面重置为默认的50%
      setCompareWidthRatio(50);
    }
  }, [initialScale, pageType, selectedImage, getTaskComponent, isDefaultEmptyImage]);

  // 使用useMemo优化图片样式计算
  const imageStyle = useMemo(() => {
    return {
      transform: imageScale === initialScale
        ? `translate(${imagePosition.x}px, ${imagePosition.y}px)`
        : `translate(${imagePosition.x}px, ${imagePosition.y}px) scale(${imageScale / initialScale})`,
      transition: isDragging ? 'none' : 'transform 0.2s ease-out',
      transformOrigin: 'center center',
      maxWidth: '100%',
      maxHeight: '100%',
      width: 'auto',
      height: 'auto',
      objectFit: 'contain'
    };
  }, [imagePosition.x, imagePosition.y, imageScale, initialScale, isDragging]);

  // 处理图片下载
  const handleDownloadImage = useCallback((imageUrl, taskId, index) => {
    const downloadImage = async () => {
      try {
        message.info('准备下载...');
        const httpsUrl = imageUrl.replace(/^http:/, 'https:');
        const response = await fetch(httpsUrl);
        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);
        
        // 优先从Content-Type响应头获取文件扩展名
        let originalExtension = 'jpg'; // 默认值
        const contentType = response.headers.get('content-type');
        if (contentType) {
          if (contentType.includes('image/png')) {
            originalExtension = 'png';
          } else if (contentType.includes('image/jpeg') || contentType.includes('image/jpg')) {
            originalExtension = 'jpg';
          } else if (contentType.includes('image/webp')) {
            originalExtension = 'webp';
          } else if (contentType.includes('image/gif')) {
            originalExtension = 'gif';
          }
        } else {
          // 如果无法从响应头获取，则从URL中提取
          const urlParts = imageUrl.split('.');
          originalExtension = urlParts.length > 1 ? urlParts[urlParts.length - 1].split('?')[0] : 'jpg';
        }
        
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = `${taskId}_${parseInt(index) + 1}.${originalExtension}`;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(blobUrl);
      } catch (error) {
        message.error('下载失败');
        console.error('下载出错:', error);
      }
    };
    
    downloadImage();
  }, []);

  // 处理编辑任务
  const handleEditTask = useCallback(() => {
    // 从generationTasks中获取完整的任务数据
    const fullTask = generationTasks?.find(task => task.taskId === selectedImage.taskId);

    if (fullTask) {
      // 直接调用父组件的编辑函数，与TaskPanel的行为保持一致
      console.log('使用完整任务数据进行编辑:', fullTask);
      onEditTask?.(fullTask);

      // 关闭详情弹窗
      onClose?.();
    }
  }, [selectedImage, onEditTask, onClose, generationTasks]);

  // 点击外部关闭弹出层
  useEffect(() => {
    const handleClickOutside = (event) => {
      const isTextButton = event.target.closest('.text-button');
      const isInsidePopup = event.target.closest('.text-popup');

      if (!isTextButton && !isInsidePopup) {
        setShowModelText(false);
        setShowSceneText(false);
        setShowAdvancedText(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 优化弹窗打开和关闭的效果
  useEffect(() => {
    if (modalContentRef.current) {
      // 添加一个小延迟，确保DOM已经渲染
      const timer = setTimeout(() => {
        modalContentRef.current?.classList.add('show-content');
      }, 50);

      return () => clearTimeout(timer);
    }
  }, []);

  // 使用防抖函数处理窗口大小变化
  useEffect(() => {
    const handleResize = debounce(() => {
      const button = document.querySelector('.text-button');
      if (button) {
        const rect = button.getBoundingClientRect();
        const popup = document.querySelector('.text-popup');
        if (popup) {
          popup.style.left = `${rect.right + 12}px`;
          popup.style.top = `${rect.top}px`;
        }
      }
    }, 100);

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 修改图片容器部分
  const renderImages = useMemo(() => {
    if (!selectedImage) return null;

    // 虚拟模特页面和灵感探索页面只显示生成图区域
    if (pageType === 'virtual' || pageType === 'inspiration' || pageType === 'matting') {
      return (
        <div className="image-compare single-image">
          {/* 生成图区域 */}
          <div className="result-image full-width">
            <div className="image-title">生成图</div>
            <div
              className={`image-container ${isDragging ? 'dragging' : ''} transparent-bg`}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseLeave}
              style={{ position: 'relative' }}
            >
              {!imagesLoaded.result && (
                <div className="image-skeleton" style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1 }}>
                  <div className="spinner"></div>
                </div>
              )}
              {selectedImage.url && (
                <img
                  ref={resultImageRef}
                  src={selectedImage.url}
                  alt="生成图"
                  style={{
                    ...imageStyle,
                    opacity: imagesLoaded.result ? 1 : 0,
                    visibility: imagesLoaded.result ? 'visible' : 'hidden',
                    position: 'relative',
                    zIndex: 0,
                    transition: isDragging ? 'none' : 'transform 0.2s ease-out, opacity 0.2s ease-out'
                  }}
                  onLoad={handleResultImageLoad}
                  onError={handleImageError}
                />
              )}
            </div>
          </div>
        </div>
      );
    }

    // 其它页面显示原图和生成图对比
    return (
      <div className="image-compare" ref={compareContainerRef}>
        {/* 原图区域 */}
        <div className="original-image" style={{ width: `${compareWidthRatio}%` }}>
          {/* 针对爆款开发页面的原图标题和显示逻辑 */}
          {pageType === 'trending' ? (
            <>
              { /* 判断是否同时有版型参考图和面料印花参考图 */}

              {(() => {
                // 先获取两个图片的URL和组件信息
                const patternUrl = getTaskComponent(selectedImage, 'patternReferencePanel.url');
                const patternComponent = getTaskComponent(selectedImage, 'patternReferencePanel');
                const printingUrl = getTaskComponent(selectedImage, 'printingReferencePanel.url');
                const printingComponent = getTaskComponent(selectedImage, 'printingReferencePanel');
                
                // 判断是否为空白图
                const isPatternEmpty = isDefaultEmptyImage(patternUrl, patternComponent);
                const isPrintingEmpty = isDefaultEmptyImage(printingUrl, printingComponent);
                
                // 添加调试信息
                console.log('ImageDetailsModal - 空白图判断调试:', {
                  patternUrl,
                  printingUrl,
                  patternServerFileName: patternComponent?.serverFileName,
                  printingServerFileName: printingComponent?.serverFileName,
                  isPatternEmpty,
                  isPrintingEmpty
                });
                
                // 确定有效的图片（非空白图）
                const hasValidPattern = patternUrl && !isPatternEmpty;
                const hasValidPrinting = printingUrl && !isPrintingEmpty;
                
                console.log('ImageDetailsModal - 有效图片判断:', {
                  hasValidPattern,
                  hasValidPrinting
                });
                
                // 根据有效图片数量决定显示模式
                if (hasValidPattern && hasValidPrinting) {
                  // 两张都是有效图片，使用双图容器布局
                  return (
                    <div className="references-container">
                      {/* 版型参考图 */}
                      <div className="reference-item">
                        <div className="image-title">版型参考图</div>
                        <div className="image-container transparent-bg" style={{ position: 'relative' }}>
                          {!imagesLoaded.original && (
                            <div className="image-skeleton" style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1 }}>
                              <div className="spinner"></div>
                            </div>
                          )}
                          <img
                            src={patternUrl}
                            alt="版型参考图"
                            style={{
                              opacity: imagesLoaded.original ? 1 : 0,
                              visibility: imagesLoaded.original ? 'visible' : 'hidden',
                              position: 'relative',
                              zIndex: 0,
                              transition: 'opacity 0.3s ease-out'
                            }}
                            onLoad={handleOriginalImageLoad}
                          />
                        </div>
                      </div>

                      {/* 面料印花参考图 */}
                      <div className="reference-item">
                        <div className="image-title">面料印花参考图</div>
                        <div className="image-container transparent-bg" style={{ position: 'relative' }}>
                          {!imagesLoaded.original && (
                            <div className="image-skeleton" style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1 }}>
                              <div className="spinner"></div>
                            </div>
                          )}
                          <img
                            src={printingUrl}
                            alt="面料印花参考图"
                            style={{
                              opacity: imagesLoaded.original ? 1 : 0,
                              visibility: imagesLoaded.original ? 'visible' : 'hidden',
                              position: 'relative',
                              zIndex: 0,
                              transition: 'opacity 0.3s ease-out'
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  );
                } else if (hasValidPattern) {
                  // 只有版型参考图是有效的
                  return (
                    <>
                      <div className="image-title">版型参考图</div>
                      <div className="image-container transparent-bg" style={{ position: 'relative' }}>
                        {!imagesLoaded.original && (
                          <div className="image-skeleton" style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1 }}>
                            <div className="spinner"></div>
                          </div>
                        )}
                        <img
                          src={patternUrl}
                          alt="版型参考图"
                          style={{
                            opacity: imagesLoaded.original ? 1 : 0,
                            visibility: imagesLoaded.original ? 'visible' : 'hidden',
                            position: 'relative',
                            zIndex: 0,
                            transition: 'opacity 0.3s ease-out'
                          }}
                          onLoad={handleOriginalImageLoad}
                        />
                      </div>
                    </>
                  );
                } else if (hasValidPrinting) {
                  // 只有面料印花参考图是有效的
                  return (
                    <>
                      <div className="image-title">面料印花参考图</div>
                      <div className="image-container transparent-bg" style={{ position: 'relative' }}>
                        {!imagesLoaded.original && (
                          <div className="image-skeleton" style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1 }}>
                            <div className="spinner"></div>
                          </div>
                        )}
                        <img
                          src={printingUrl}
                          alt="面料印花参考图"
                          style={{
                            opacity: imagesLoaded.original ? 1 : 0,
                            visibility: imagesLoaded.original ? 'visible' : 'hidden',
                            position: 'relative',
                            zIndex: 0,
                            transition: 'opacity 0.3s ease-out'
                          }}
                          onLoad={handleOriginalImageLoad}
                        />
                      </div>
                    </>
                  );
                } else {
                  // 两张都是空白图，不显示任何内容
                  return null;
                }
              })()}
            </>
              ) : (
                <>
              <div className="image-title">原图</div>
              <div className="image-container transparent-bg" style={{ position: 'relative' }}>
                {!imagesLoaded.original && (
                  <div className="image-skeleton" style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1 }}>
                    <div className="spinner"></div>
                  </div>
                )}
                {/* 根据页面类型选择正确的原图路径 */}
                {(() => {
                  // 根据页面类型确定要使用的组件类型和路径
                  let imageUrl;
                  switch (pageType) {
                    case 'background':
                      imageUrl = getServerImageUrl(selectedImage, 'foregroundPanel') ||
                        getTaskComponent(selectedImage, 'foregroundPanel.originalImage') ||
                        getTaskComponent(selectedImage, 'foregroundPanel.url');
                      break;
                    case 'matting':
                      imageUrl = getServerImageUrl(selectedImage, 'sourceImagePanel') ||
                        getTaskComponent(selectedImage, 'sourceImagePanel.originalImage') ||
                        getTaskComponent(selectedImage, 'sourceImagePanel.url');
                      break;
                    case 'try-on':
                    case 'hand-fix': // 新增 hand-fix 支持
                    case 'recolor':
                    case 'fabric':
                    case 'change-model':
                    case 'change-posture': // 新增 change-posture 支持
                    case 'drawing': // 新增 drawing 支持
                      imageUrl = getServerImageUrl(selectedImage, (pageType === 'change-model' || pageType === 'hand-fix' || pageType === 'change-posture' || pageType === 'divergent') ? 'modelMaskPanel' : 'clothingPanel') ||
                        getTaskComponent(selectedImage, (pageType === 'change-model' || pageType === 'hand-fix' || pageType === 'change-posture' || pageType === 'divergent') ? 'modelMaskPanel.originalImage' : 'clothingPanel.originalImage') ||
                        getTaskComponent(selectedImage, (pageType === 'change-model' || pageType === 'hand-fix' || pageType === 'change-posture' || pageType === 'divergent') ? 'modelMaskPanel.url' : 'clothingPanel.url');
                      break;
                    case 'optimize':
                    case 'divergent':
                      imageUrl = getServerImageUrl(selectedImage, 'modelMaskPanel') ||
                        getTaskComponent(selectedImage, 'modelMaskPanel.originalImage') ||
                        getTaskComponent(selectedImage, 'modelMaskPanel.url');
                      break;
                    case 'upscale':
                    case 'extend':
                      imageUrl = getServerImageUrl(selectedImage, 'sourceImagePanel') ||
                        getTaskComponent(selectedImage, 'sourceImagePanel.originalImage') ||
                        getTaskComponent(selectedImage, 'sourceImagePanel.url');
                      break;
                    case 'inpaint':
                      // 消除笔页面显示模特原图
                      imageUrl = getServerImageUrl(selectedImage, 'modelMaskPanel') ||
                        getTaskComponent(selectedImage, 'modelMaskPanel.originalImage') ||
                        getTaskComponent(selectedImage, 'modelMaskPanel.url');
                      break;
                    case 'fashion':
                    default:
                      // 时尚大片页面或默认情况
                      // 只查找参考图组件的图片路径
                      imageUrl = getServerImageUrl(selectedImage, 'referencePanel') ||
                                 getTaskComponent(selectedImage, 'referencePanel.originalImage') ||
                                 getTaskComponent(selectedImage, 'referencePanel.url');
                      break;
                    case 'detail-migration':
                      // 细节还原页面显示服装原图
                      imageUrl = getServerImageUrl(selectedImage, 'clothingMaskPanel') ||
                        getTaskComponent(selectedImage, 'clothingMaskPanel.originalImage') ||
                        getTaskComponent(selectedImage, 'clothingMaskPanel.url');
                      break;
                  }

                  // 如果找到图片URL，则显示图片
                  return imageUrl && (
                    <img
                      src={imageUrl}
                      alt="原始图片"
                      style={{
                        opacity: imagesLoaded.original ? 1 : 0,
                        visibility: imagesLoaded.original ? 'visible' : 'hidden',
                        position: 'relative',
                        zIndex: 0,
                        transition: 'opacity 0.3s ease-out'
                      }}
                      onLoad={handleOriginalImageLoad}
                      onError={(e) => {
                        console.error('原始图片加载失败:', e.target.src);

                        // 移除硬编码路径，采用与其他组件一致的错误处理方式
                        e.target.onerror = null; // 防止无限循环

                        // 确保设置状态为已加载，避免无限加载状态
                        handleOriginalImageLoad();
                      }}
                    />
                  );
                })()}
              </div>
            </>
          )}
        </div>

        {/* 添加可调整宽度的分割线 */}
        <ResizeHandle
          ref={resizeHandleRef}
          containerRef={compareContainerRef}
          onResize={setCompareWidthRatio}
          minWidth={20}
          maxWidth={80}
        />

        {/* 生成图区域 */}
        <div className="result-image" style={{ width: `${100 - compareWidthRatio}%` }}>
          <div className="image-title">生成图</div>
          <div
            ref={resultContainerRef}
            className={`image-container ${isDragging ? 'dragging' : ''} transparent-bg`}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseLeave}
            style={{ position: 'relative' }}
          >
            {!imagesLoaded.result && (
              <div className="image-skeleton" style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1 }}>
                <div className="spinner"></div>
              </div>
            )}
            {selectedImage.url && (
              <img
                ref={resultImageRef}
                src={selectedImage.url}
                alt="生成图"
                style={{
                  ...imageStyle,
                  opacity: imagesLoaded.result ? 1 : 0,
                  visibility: imagesLoaded.result ? 'visible' : 'hidden',
                  position: 'relative',
                  zIndex: 0,
                  transition: isDragging ? 'none' : 'transform 0.2s ease-out, opacity 0.2s ease-out'
                }}
                onLoad={handleResultImageLoad}
                onError={handleImageError}
              />
            )}
          </div>
        </div>
      </div>
    );
  }, [
    selectedImage,
    imagesLoaded.original,
    imagesLoaded.result,
    isDragging,
    imageStyle,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleMouseLeave,
    handleOriginalImageLoad,
    handleResultImageLoad,
    pageType,
    compareWidthRatio
  ]);


  // 使用useMemo优化图片信息获取逻辑
  useEffect(() => {
    // 创建一个立即执行的异步函数，处理图片信息获取
    const fetchImageInfo = async () => {
      if (!selectedImage?.url) return;

      setIsLoadingFileInfo(true);
      try {
        console.log('开始获取生成图的准确信息:', selectedImage.url);
        
        // 使用 getActualImageInfo 获取生成图的准确信息
        const actualInfo = await getActualImageInfo(selectedImage.url);
        
        if (actualInfo) {
          console.log('成功获取生成图信息:', actualInfo);
          setActualFileInfo(actualInfo);
          
          // 更新 selectedImage 的 fileInfo，但保持其他属性不变
          setSelectedImage(prev => {
            return {
              ...prev,
              fileInfo: {
                ...prev.fileInfo,
                // 使用获取到的准确信息更新
                size: actualInfo.size,
                width: actualInfo.width,
                height: actualInfo.height,
                format: actualInfo.format,
                type: actualInfo.format
              }
            };
          });
        } else {
          console.warn('无法获取生成图的准确信息，使用现有信息');
          // 如果获取失败，至少清空 actualFileInfo
          setActualFileInfo(null);
        }
      } catch (error) {
        console.error('获取生成图信息失败:', error.message);
        setActualFileInfo(null);
      } finally {
        setIsLoadingFileInfo(false);
      }
    };

    const cacheKey = `${selectedImage?.url}-${selectedImage?.taskId}-${selectedImage?.imageIndex}`;
    if (cacheKey !== prevCacheKeyRef.current) {
      prevCacheKeyRef.current = cacheKey;
      fetchImageInfo();
    }
  },[selectedImage]);

  // 处理预览翻页 - 上一张
  const handlePrevImage = (e) => {
    if (e) e.stopPropagation();
    if (!previewImages || previewImages.length <= 1) return;

    const newIndex = (currentPreviewIndex - 1 + previewImages.length) % previewImages.length;
    setCurrentPreviewIndex(newIndex);
    setPreviewImage(previewImages[newIndex]);
  };

  // 处理预览翻页 - 下一张
  const handleNextImage = (e) => {
    if (e) e.stopPropagation();
    if (!previewImages || previewImages.length <= 1) return;

    const newIndex = (currentPreviewIndex + 1) % previewImages.length;
    setCurrentPreviewIndex(newIndex);
    setPreviewImage(previewImages[newIndex]);
  };

  // 处理键盘方向键事件
  const handleKeyDown = useCallback((e) => {
    if (!previewImages) return;

    if (e.key === 'ArrowLeft') {
      handlePrevImage();
    } else if (e.key === 'ArrowRight') {
      handleNextImage();
    } else if (e.key === 'Escape') {
      setShowPreviewModal(false);
    }
  }, [previewImages, currentPreviewIndex]);

  // 添加键盘事件监听器
  useEffect(() => {
    if (showPreviewModal) {
      window.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [showPreviewModal, handleKeyDown]);

  // 获取组件ID显示
  const getComponentId = (component) => {
    return component?.componentId || '未知';
  };

  // 辅助函数 - 用于获取组件ID
  const getComponentIdFromTask = (selectedImage, path) => {
    const component = getTaskComponent(selectedImage, path);
    return component?.componentId || '未知';
  };

  // 辅助函数 - 用于复制ID
  const copyComponentId = (selectedImage, path) => {
    const component = getTaskComponent(selectedImage, path);
    return component?.componentId || '';
  };

  // 获取图片ID - 只使用componentId作为标准ID
  const getImageIdFromFileName = (component) => {
    // 只使用componentId作为标准图片ID
    if (component?.componentId) {
      return component.componentId;
    }

    // 如果没有componentId，直接返回未知ID
    return '未知ID';
  };

  // 从任务中获取图片ID
  const getImageIdFromTask = (selectedImage, path) => {
    // 获取组件对象
    const component = getTaskComponent(selectedImage, path);

    // 优先使用组件的componentId
    if (component?.componentId) {
      return component.componentId;
    }

    // 如果找不到组件或组件没有componentId，返回未知ID
    return '未知ID';
  };

  // 根据比较方式更新图片显示样式
  useEffect(() => {
    // ... existing code ...
  }, [sliderPosition, imagesLoaded.result]);

  // 获取安全的预览URL（避免使用blob URL）
  const getSafePreviewUrl = useCallback((task, componentPath, urlPath = '') => {
    // 优先使用服务器URL
    const serverUrl = getServerImageUrl(task, componentPath);
    if (serverUrl) return serverUrl;

    // 根据环境设置基础URL
    const baseUrl = process.env.REACT_APP_BACKEND_URL;

    // 如果指定了特定路径，优先使用
    if (urlPath) {
      const component = getTaskComponent(task, componentPath);
      if (!component) return null;

      // 使用指定路径获取URL
      const urlValue = component[urlPath];
      if (urlValue && typeof urlValue === 'string') {
        // 检查是否为blob URL
        if (urlValue.startsWith('blob:')) {
          console.warn(`预览URL是blob URL，可能无法加载: ${componentPath}.${urlPath}`);

          // 开发环境和生产环境的逻辑区分处理
          if (process.env.NODE_ENV === 'development') {
            // 开发环境优先尝试找相对路径
            for (const field of ['url', 'originalImage']) {
              if (component[field] && !component[field].startsWith('blob:')) {
                return component[field];
              }
            }

            // 如果没有找到相对路径，再尝试使用任务级别的URL
            if (task.primaryImageFileName) {
              return `${baseUrl}/storage/developer/uploads/${task.primaryImageFileName}`;
            } else if (task.serverFileName) {
              return `${baseUrl}/storage/developer/uploads/${task.serverFileName}`;
            }
          } else {
            // 生产环境优先使用服务器路径
            if (task.serverFileName) {
              return `/storage/developer/uploads/${task.serverFileName}`;
            } else if (task.primaryImageFileName) {
              return `/storage/developer/uploads/${task.primaryImageFileName}`;
            }

            // 如果没有服务器路径，尝试找相对路径
            for (const field of ['url', 'originalImage']) {
              if (component[field] && !component[field].startsWith('blob:')) {
                return component[field];
              }
            }
          }

          // 如果没有可用替代，返回null
          return null;
        }

        // 返回非blob URL
        return urlValue;
      }
    }

    // 获取组件
    const component = getTaskComponent(task, componentPath);
    if (!component) return null;

    // 按环境不同优先尝试不同的字段
    if (process.env.NODE_ENV === 'development') {
      // 开发环境优先尝试相对路径
      const possibleUrlFields = ['url', 'originalImage', 'preview', 'maskPath', 'maskData'];

      for (const field of possibleUrlFields) {
        const url = component[field];
        if (url && typeof url === 'string' && !url.startsWith('blob:')) {
          return url;
        }
      }

      // 其次尝试使用serverFileName
      if (component.serverFileName) {
        return `${baseUrl}/storage/developer/uploads/${component.serverFileName}`;
      }
    } else {
      // 生产环境优先使用serverFileName
      if (component.serverFileName) {
        return `/storage/developer/uploads/${component.serverFileName}`;
      }

      // 其次尝试相对路径
      const possibleUrlFields = ['url', 'originalImage', 'preview', 'maskPath', 'maskData'];

      for (const field of possibleUrlFields) {
        const url = component[field];
        if (url && typeof url === 'string' && !url.startsWith('blob:')) {
          return url;
        }
      }
    }

    // 如果任务有primaryImageFileName或serverFileName，尝试构建URL
    if (process.env.NODE_ENV === 'development') {
      if (task.primaryImageFileName) {
        return `${baseUrl}/storage/developer/uploads/${task.primaryImageFileName}`;
      }

      if (task.serverFileName) {
        return `${baseUrl}/storage/developer/uploads/${task.serverFileName}`;
      }
    } else {
      if (task.serverFileName) {
        return `/storage/developer/uploads/${task.serverFileName}`;
      }

      if (task.primaryImageFileName) {
        return `/storage/developer/uploads/${task.primaryImageFileName}`;
      }
    }

    return null;
  }, [getServerImageUrl, getTaskComponent]);

  // 安全地打开预览
  const openPreview = useCallback((task, componentPath, urlPath = '') => {
    const safeUrl = getSafePreviewUrl(task, componentPath, urlPath);
    if (!safeUrl) {
      console.error(`无法获取有效的预览URL: ${componentPath}.${urlPath}`);
      message.error('无法加载预览 - 找不到有效的图片路径');
      return;
    }

    console.log(`打开预览: ${componentPath}.${urlPath} -> ${safeUrl}`);
    setPreviewImages([safeUrl]);
    setCurrentPreviewIndex(0);
    setPreviewImage(safeUrl);
    setShowPreviewModal(true);
  }, [getSafePreviewUrl]);

  useEffect(() => {
      // setCurrentPreviewIndex(selectedImage.imageIndex);
    console.log('ImageDetailsModal渲染时的selectedImage:',
      selectedImage ? {
        url: selectedImage.url,
        components: selectedImage.components ? Object.keys(selectedImage.components) : 'none',
        hasSourceImagePanel: selectedImage.components?.sourceImagePanel ? true : false,
        sourceImagePanel: selectedImage.components?.sourceImagePanel ? {
          url: selectedImage.components.sourceImagePanel.url,
          serverFileName: selectedImage.components.sourceImagePanel.serverFileName,
          isBlob: selectedImage.components.sourceImagePanel.url?.startsWith('blob:')
        } : null
      } : 'none'
    );
  }, [selectedImage]);

  // 记录selectedImage信息以便调试
  useEffect(() => {
    const timestamp = Date.now();
    // 首先，执行数据修复
    if (selectedImage) {
      // 检查是否需要修复数据
      if (!selectedImage.serverFileName && selectedImage.components) {
        // 尝试从组件中获取serverFileName
        let sourceComponent = null;

        // 根据页面类型查找主要组件
        if (pageType === 'matting') {
          sourceComponent = getTaskComponent(selectedImage, 'sourceImagePanel');
        } else if (pageType === 'upscale' || pageType === 'extend' || pageType === 'bleach') {
          sourceComponent = getTaskComponent(selectedImage, 'sourceImagePanel');
        } else if (pageType === 'try-on' || pageType === 'recolor' || pageType === 'fabric') {
          sourceComponent = getTaskComponent(selectedImage, 'clothingPanel');
        } else if (pageType === 'background') {
          sourceComponent = getTaskComponent(selectedImage, 'foregroundPanel');
        }

        // 如果找到组件并且有serverFileName，同步到selectedImage
        if (sourceComponent && sourceComponent.serverFileName) {
          // console.log(`【ImageDetailsModal数据修复】从${pageType}页面组件同步serverFileName: ${sourceComponent.serverFileName}`);
          selectedImage.serverFileName = sourceComponent.serverFileName;
          // 同步更新primaryImageFileName
          if (!selectedImage.primaryImageFileName) {
            selectedImage.primaryImageFileName = sourceComponent.serverFileName;
            // console.log(`【ImageDetailsModal数据修复】同步更新primaryImageFileName: ${sourceComponent.serverFileName}`);
          }
        }
        // 尝试使用primaryImageFileName
        else if (selectedImage.primaryImageFileName) {
          // console.log(`【ImageDetailsModal数据修复】使用primaryImageFileName作为serverFileName: ${selectedImage.primaryImageFileName}`);
          selectedImage.serverFileName = selectedImage.primaryImageFileName;
        }
      }

      // 确保primaryImageFileName和serverFileName保持一致
      if (!selectedImage.primaryImageFileName && selectedImage.serverFileName) {
        // console.log(`【ImageDetailsModal数据修复】从serverFileName同步primaryImageFileName: ${selectedImage.serverFileName}`);
        selectedImage.primaryImageFileName = selectedImage.serverFileName;
      } else if (!selectedImage.serverFileName && selectedImage.primaryImageFileName) {
        // console.log(`【ImageDetailsModal数据修复】从primaryImageFileName同步serverFileName: ${selectedImage.primaryImageFileName}`);
        selectedImage.serverFileName = selectedImage.primaryImageFileName;
      }

      // 确保sourceImagePanel组件存在并被正确标记
      if (pageType === 'matting' || pageType === 'bleach') {
        // 检查是否存在sourceImagePanel
        const sourceImagePanel = getTaskComponent(selectedImage, 'sourceImagePanel');

        // 确保selectedImage有一个hasSourceImagePanel属性用于快速检查
        selectedImage.hasSourceImagePanel = !!sourceImagePanel;

        // 如果不存在但我们有serverFileName，尝试创建一个
        if (!sourceImagePanel && (selectedImage.serverFileName || selectedImage.primaryImageFileName)) {
          console.log(`【ImageDetailsModal数据修复】创建缺失的sourceImagePanel组件`);

          // 文件名优先级：serverFileName > primaryImageFileName
          const fileName = selectedImage.serverFileName || selectedImage.primaryImageFileName;
          // 使用条件判断区分开发环境和生产环境
          const baseUrl = process.env.REACT_APP_BACKEND_URL;

          // 创建新的sourceImagePanel组件对象
          const newSourceImagePanel = {
            componentType: 'sourceImagePanel',
            componentId: `generated_${Date.now()}`,
            isMainImage: true,
            serverFileName: fileName,
            url: process.env.NODE_ENV === 'development' ?
                 `${baseUrl}/storage/developer/uploads/${fileName}` :
                 `/storage/developer/uploads/${fileName}`,
            status: 'completed'
          };

          // 安全地创建或更新组件存储结构
          if (!selectedImage.components) {
            // 如果没有组件结构，创建为数组 (新格式)
            selectedImage.components = [newSourceImagePanel];
          } else if (Array.isArray(selectedImage.components)) {
            // 如果是数组，添加到数组中
            selectedImage.components.push(newSourceImagePanel);
          } else {
            // 如果是对象，添加为属性
            selectedImage.components.sourceImagePanel = newSourceImagePanel;
          }

          // 更新快速检查标志和引用
          selectedImage.hasSourceImagePanel = true;
          selectedImage.sourceImagePanel = newSourceImagePanel;

          console.log(`【ImageDetailsModal数据修复】创建了sourceImagePanel组件，使用fileName: ${fileName}`);
        }

        // 如果sourceImagePanel存在但我们有不同的文件名，同步它们
        if (sourceImagePanel && selectedImage.serverFileName &&
            sourceImagePanel.serverFileName !== selectedImage.serverFileName) {
          // console.log(`【ImageDetailsModal数据修复】同步sourceImagePanel的serverFileName`);
          // console.log(`从: ${sourceImagePanel.serverFileName || '未设置'} 到: ${selectedImage.serverFileName}`);

          // 创建对sourceImagePanel的引用，以便可以直接修改它
          let sourceImagePanelRef = sourceImagePanel;

          // 如果selectedImage.components是数组，确保对正确的对象进行修改
          if (Array.isArray(selectedImage.components)) {
            // 在数组中查找sourceImagePanel组件
            const index = selectedImage.components.findIndex(comp =>
              comp && (comp.componentType === 'sourceImagePanel' || comp.type === 'sourceImagePanel')
            );
            if (index !== -1) {
              // 直接修改数组中的对象
              selectedImage.components[index].serverFileName = selectedImage.serverFileName;
              sourceImagePanelRef = selectedImage.components[index];
            }
          } else if (selectedImage.components && typeof selectedImage.components === 'object') {
            // 对象格式 - 直接修改对象属性
            if (selectedImage.components.sourceImagePanel) {
              selectedImage.components.sourceImagePanel.serverFileName = selectedImage.serverFileName;
              sourceImagePanelRef = selectedImage.components.sourceImagePanel;
            }
          }

          // 同步sourceImagePanel引用 (如果存在)
          if (selectedImage.sourceImagePanel) {
            selectedImage.sourceImagePanel.serverFileName = selectedImage.serverFileName;
          } else {
            // 如果不存在，创建引用
            selectedImage.sourceImagePanel = sourceImagePanelRef;
          }
        }
      }
    }

    // 记录最终的数据结构
    console.log('ImageDetailsModal渲染时的selectedImage:',
      selectedImage ? {
        url: selectedImage.url,
        components: selectedImage.components ? (Array.isArray(selectedImage.components) ?
          'array[' + selectedImage.components.length + ']' :
          Object.keys(selectedImage.components)) : 'none',
        serverFileName: selectedImage.serverFileName || '未设置',
        primaryImageFileName: selectedImage.primaryImageFileName || '未设置',
        hasSourceImagePanel: selectedImage.hasSourceImagePanel || false,
        sourceImagePanel: selectedImage.components?.sourceImagePanel ? {
          url: selectedImage.components.sourceImagePanel.url,
          serverFileName: selectedImage.components.sourceImagePanel.serverFileName,
          isBlob: selectedImage.components.sourceImagePanel.url?.startsWith('blob:')
        } : null
      } : 'none'
    );
  }, [selectedImage, pageType, getTaskComponent]);

  // 确保任务和图片数据准备好
  useEffect(() => {
    if (initialSelectedImage && generationTasks && generationTasks.length > 0) {
      const task = generationTasks.find(task => task.taskId === initialSelectedImage.taskId);

      if (task) {
        // 确保任务包含images或generatedImages
        if (!task.images && task.generatedImages) {
          console.log('将任务generatedImages复制到images属性', task.taskId);
          // 创建一个新的任务对象，包含images属性
          const updatedTasks = generationTasks.map(t => {
            if (t.taskId === task.taskId) {
              return { ...t, images: t.generatedImages };
            }
            return t;
          });

          // 更新任务列表（如果您有方法可以更新generationTasks，请在此处调用）
          // 由于我们不能直接修改props，这里只修改选中的图片
          setSelectedImage(prev => ({
            ...prev,
            // 确保包含图片数组
            images: task.generatedImages
          }));
        }
      }
    }
  }, [initialSelectedImage, generationTasks]);

  // 尝试使用taskId从结果中构建图片数组
  const getImagesFromTaskResults = useCallback((taskId) => {
    if (!taskId || !generationTasks) return [];

    const task = generationTasks.find(t => t.taskId === taskId);
    if (!task) return [];

    if (task.images?.length > 0) {
      return task.images;
    }

    // processInfo
    if (task.processInfo.results?.length > 0) {
      return task.processInfo.results;
    }

    if (task.generatedImages?.length > 0) {
      return task.generatedImages;
    }

    if (selectedImage?.url) {
      return [{
        url: selectedImage.url,
        taskId: selectedImage.taskId,
        imageIndex: 0
      }];
    }

    return [];
  }, [generationTasks, selectedImage]);

  // 在初始化和taskId变更时，尝试获取所有相关图片
  const [taskImages, setTaskImages] = useState([]);

  useEffect(() => {
    if (selectedImage?.taskId) {
      const images = getImagesFromTaskResults(selectedImage.taskId);
      console.log(`为任务 ${selectedImage.taskId} 找到图片:`, images.length);
      setTaskImages(images);
    }
  }, [selectedImage?.taskId, getImagesFromTaskResults]);

  // 获取所有MaskDescriptionPanel组件的函数
  const getAllMaskDescriptionPanels = useCallback((task) => {
    if (!task || !task.components) return [];
    
    if (Array.isArray(task.components)) {
      return task.components.filter(comp => 
        comp && comp.componentType === 'maskDescriptionPanel'
      );
    } else {
      // 如果components是对象，查找所有maskDescriptionPanel类型的组件
      const maskPanels = [];
      for (const key in task.components) {
        const component = task.components[key];
        if (component && component.componentType === 'maskDescriptionPanel') {
          maskPanels.push(component);
        }
      }
      return maskPanels;
    }
  }, []);

  return (
    <React.Fragment>
      {/* 添加全局样式 */}
      <style dangerouslySetInnerHTML={{ __html: globalStyles }} />

      <Modal
        open={true}
        footer={null}
        onCancel={handleClose}
        width={pageType === 'virtual' ? "55%" : "95%"}
        style={{
          maxWidth: pageType === 'virtual' ? '1800px' : '2800px',
          transform: 'none',
          overflow: 'hidden'
        }}
        centered
        className={`image-details-modal ${pageType === 'virtual' ? 'virtual-page-modal' : ''}`}
        closeIcon={null}
        destroyOnClose={true}
        maskClosable={false}
        transitionName=""
        maskTransitionName=""
      >


        {selectedImage && (
          <div className="image-details-modal-content" ref={modalContentRef}>
            {/* 添加大型关闭按钮 */}
            <button
              className="large-close-button"
              onClick={handleClose}
            >
              <MdClose />
            </button>

            {/* 左侧信息栏 */}
            <div className="details-sidebar">
              {/* 基本信息组 */}
              <div className="info-group">
                <div className="info-item">
                  <span className="label">生成时间：</span>
                  <span className="value">{new Date(selectedImage.createdAt).toLocaleString()}</span>
                </div>
                <div className="info-item">
                  <span className="label">任务ID：</span>
                  <div className="value-with-copy">
                    <span className="value">{selectedImage.taskId}</span>
                    <button
                      className="copy-id-btn"
                      onClick={() => {
                        navigator.clipboard.writeText(selectedImage.taskId);
                        message.success('已复制任务ID');
                      }}
                      title="复制任务ID"
                    >
                      <MdContentCopy />
                    </button>
                  </div>
                </div>
                <div className="info-item">
                  <span className="label">生成图ID：</span>
                  <div className="value-with-copy">
                    <span className="value">{`${selectedImage.taskId}_${selectedImage.imageIndex + 1}`}</span>
                    <button
                      className="copy-id-btn"
                      onClick={() => {
                        navigator.clipboard.writeText(`${selectedImage.taskId}_${selectedImage.imageIndex + 1}`);
                        message.success('已复制图片ID');
                      }}
                      title="复制图片ID"
                    >
                      <MdContentCopy />
                    </button>
                  </div>
                </div>
                <div className="info-item">
                  <span className="label">功能模块：</span>
                  <span className="value">
                    {pageType === 'try-on' ? '模特图-模特换装' :
                     pageType === 'recolor' ? '模特图-服装复色' :
                     pageType === 'background' ? '模特图-换背景' :
                     pageType === 'change-posture' ? '模特图-换姿势' :
                     pageType === 'virtual' ? '模特图-虚拟模特' :
                     pageType === 'fabric' ? '模特图-换面料' :
                     pageType === 'change-model' ? '模特图-换模特' :
                     pageType === 'detail-migration' ? '模特图-细节还原' :
                     pageType === 'hand-fix' ? '模特图-手部修复' :
                     pageType === 'drawing' ? '款式设计-线稿生成' :
                     pageType === 'optimize' ? '款式设计-款式优化' :
                     pageType === 'inspiration' ? '款式设计-灵感探索' :
                     pageType === 'trending' ? '款式设计-爆款开发' :
                     pageType === 'divergent' ? '款式设计-爆款延伸' :
                     pageType === 'upscale' ? '快捷工具-高清放大' :
                     pageType === 'extend' ? '快捷工具-智能扩图' :
                     pageType === 'matting' ? '快捷工具-自动抠图' :
                     pageType === 'extract' ? '快捷工具-图片取词' :
                     pageType === 'inpaint' ? '快捷工具-消除笔' :
                     pageType === 'bleach' ? '快捷工具-服装去色' :
                     pageType === 'change-face' ? '模特图-模特换脸' :
                     pageType === 'imgtextvideo' ? '视频工具-图文成片' :
                     pageType === 'mulimgvideo' ? '视频工具-多图成片' :
                     '模特图-时尚大片'}
                  </span>
                </div>
              </div>

              {/* 服装信息组 - 适用于try-on、recolor、fabric、change-model、detail-migration、drawing和change-face页面 */}
              {(pageType === 'try-on' || pageType === 'recolor' || pageType === 'fabric' || pageType === 'change-model' || pageType === 'detail-migration' || pageType === 'drawing' || pageType === 'change-face') && 
               (getTaskComponent(selectedImage, (pageType === 'detail-migration' || pageType === 'change-face') ? 'clothingMaskPanel' : 'clothingPanel') || getTaskComponent(selectedImage, 'modelMaskPanel')) && (
                <div className="info-group">
                  <h3 className="group-title">{pageType === 'detail-migration' ? '服装原图' : pageType === 'change-face' ? '脸部原图' : '服装'}</h3>
                  <div className="info-item with-thumbnail">
                    <div
                      className="component-thumbnail-wrapper"
                      onClick={() => {
                        // 根据页面类型选择正确的组件
                        const componentType = pageType === 'change-model' ? 'modelMaskPanel' : 
                          (pageType === 'detail-migration' || pageType === 'change-face') ? 'clothingMaskPanel' : 'clothingPanel';
                        openPreview(selectedImage, componentType);
                      }}
                    >
                      <div className="component-thumbnail">
                        <img
                          src={getServerImageUrl(selectedImage, pageType === 'change-model' ? 'modelMaskPanel' : 
                            (pageType === 'detail-migration' || pageType === 'change-face') ? 'clothingMaskPanel' : 'clothingPanel') ||
                             getTaskComponent(selectedImage, pageType === 'change-model' ? 'modelMaskPanel.originalImage' : 
                               (pageType === 'detail-migration' || pageType === 'change-face') ? 'clothingMaskPanel.originalImage' : 'clothingPanel.originalImage') ||
                             getTaskComponent(selectedImage, pageType === 'change-model' ? 'modelMaskPanel.url' : 
                               (pageType === 'detail-migration' || pageType === 'change-face') ? 'clothingMaskPanel.url' : 'clothingPanel.url')}
                          alt="服装图片缩略图"
                          onError={(e) => {
                            console.error('服装缩略图加载失败:', e.target.src);
                            e.target.src = '/images/icons/clothing.png';
                          }}
                        />
                      </div>
                      <div className="thumbnail-overlay">
                        <MdOutlineZoomOutMap />
                      </div>
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">图片ID：</span>
                        <div className="value-with-copy">
                          <span className="value">
                            {getTaskComponent(selectedImage, (pageType === 'change-model' || pageType === 'detail-migration' || pageType === 'change-face') ? 'modelMaskPanel.componentId' : 'clothingPanel.componentId') || '未知'}
                          </span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getTaskComponent(selectedImage, (pageType === 'change-model' || pageType === 'detail-migration' || pageType === 'change-face') ? 'modelMaskPanel.componentId' : 'clothingPanel.componentId') || '');
                              message.success('已复制图片ID');
                            }}
                            title="复制图片ID"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                      <div className="info-row">
                        <span className="label">文件名：</span>
                        <div className="value-with-copy">
                          <span className="value">
                            {getTaskComponent(selectedImage, (pageType === 'change-model' || pageType === 'detail-migration' || pageType === 'change-face') ? 'modelMaskPanel.serverFileName' : 'clothingPanel.serverFileName') || '未知'}
                          </span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getTaskComponent(selectedImage, (pageType === 'change-model' || pageType === 'detail-migration' || pageType === 'change-face') ? 'modelMaskPanel.serverFileName' : 'clothingPanel.serverFileName') || '');
                              message.success('已复制文件名');
                            }}
                            title="复制文件名"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* 服装款式描述 - 只有当款式描述组件有实际内容时才显示 */}
                  {(pageType === 'try-on' || pageType === 'fabric' || pageType === 'recolor' || pageType === 'matting' || pageType === 'change-model') && (() => {
                    // 获取第一个款式描述组件
                    const allMaskPanels = getAllMaskDescriptionPanels(selectedImage);
                    const clothingMaskPanel = allMaskPanels[0];
                    
                    // 只有当组件存在且有实际内容时才显示
                    if (clothingMaskPanel && (clothingMaskPanel.selectedTag || clothingMaskPanel.customText)) {
                      return (
                        <div className="info-item" style={{ marginTop: '-8px', paddingTop: '8px', borderTop: '1px solid var(--border-light)' }}>
                          <div className="info-content" style={{ width: '100%' }}>
                            <div className="info-row" style={{ marginBottom: '8px' }}>
                              <span className="label" style={{ fontWeight: '500' }}>款式描述：</span>
                            </div>
                            {clothingMaskPanel.selectedTag ? (
                              <div className="info-row">
                                <span className="label">已选择标签:</span>
                                <div className="tag-display">
                                  <span className="tag-item">
                                    {clothingMaskPanel.selectedTag}
                                  </span>
                                </div>
                              </div>
                            ) : null}

                            {clothingMaskPanel.customText && (
                              <div className="info-row">
                                <span className="label">自定义描述:</span>
                                <div className="value-with-copy">
                                  <span className="value">
                                    {clothingMaskPanel.customText}
                                  </span>
                                  <button
                                    className="copy-id-btn"
                                    onClick={() => {
                                      navigator.clipboard.writeText(clothingMaskPanel.customText || '');
                                      message.success('已复制自定义描述');
                                    }}
                                    title="复制描述文本"
                                  >
                                    <MdContentCopy />
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    }
                    
                    return null;
                  })()}

                  {/* 服装原图蒙版信息 - try-on页面下只要有蒙版（hasMask为true）就显示 */}
                  {(pageType === 'detail-migration' || (pageType === 'try-on' && ((getTaskComponent(selectedImage, 'clothingMaskPanel.hasMask') === true) || (getTaskComponent(selectedImage, 'clothingPanel.hasMask') === true)))) && (getTaskComponent(selectedImage, 'clothingMaskPanel') || getTaskComponent(selectedImage, 'clothingPanel')) && (() => {
                    // 优先取 clothingMaskPanel，没有就取 clothingPanel
                    const getClothingMaskValue = (field) => getTaskComponent(selectedImage, `clothingMaskPanel.${field}`) ?? getTaskComponent(selectedImage, `clothingPanel.${field}`);
                    const hasMask = getClothingMaskValue('hasMask');
                    const maskData = getClothingMaskValue('maskData');
                    const maskPath = getClothingMaskValue('maskPath');
                    return (
                      <div className="info-item with-thumbnail" style={{ marginTop: '-8px', paddingTop: '8px', borderTop: '1px solid var(--border-light)' }}>
                        <div
                          className="component-thumbnail-wrapper"
                          onClick={() => {
                            if (maskData || maskPath) {
                              const previewUrl = maskData || maskPath;
                              setPreviewImages([previewUrl]);
                              setCurrentPreviewIndex(0);
                              setPreviewImage(previewUrl);
                              setShowPreviewModal(true);
                            }
                          }}
                        >
                          <div className="component-thumbnail transparent-bg">
                            <img
                              src={maskData || maskPath || '/images/icons/mask.png'}
                              alt="服装原图蒙版缩略图"
                              style={{ objectFit: 'contain', width: '100%', height: '100%' }}
                            />
                          </div>
                          {(maskData || maskPath) && (
                            <div className="thumbnail-overlay">
                              <MdOutlineZoomOutMap />
                            </div>
                          )}
                        </div>
                        <div className="info-content">
                          <div className="info-row" style={{ marginBottom: '8px' }}>
                            <span className="label" style={{ fontWeight: '500' }}>蒙版：</span>
                          </div>
                          {hasMask ? (
                            <div className="info-row">
                              <span className="label">蒙版类型：</span>
                              <span className="value">手动绘制</span>
                            </div>
                          ) : (
                            <div className="info-row">
                              <span className="label">蒙版类型：</span>
                              <span className="value">自动生成</span>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}

              {/* 原始图片组件 - 在upscale、extend、matting、bleach等页面显示 */}
              {(pageType === 'upscale' || pageType === 'extend' || pageType === 'matting' || pageType === 'extract' || pageType === 'bleach') && selectedImage && (
                <div className="info-group">
                  <h3 className="group-title">原始图片</h3>
                  <div className="info-item with-thumbnail">
                    <div
                      className="component-thumbnail-wrapper"
                      onClick={() => {
                        // 根据页面类型和数据结构选择正确的源
                        let imgSrc = '';

                        // 优先使用新的components结构
                        if (pageType === 'matting' && getTaskComponent(selectedImage, 'sourceImagePanel')) {
                          // 使用安全的预览函数
                          openPreview(selectedImage, 'sourceImagePanel');
                          return;
                        }
                        // 使用source组件
                        else if (getTaskComponent(selectedImage, 'sourceImagePanel')) {
                          openPreview(selectedImage, 'sourceImagePanel');
                          return;
                        }

                        // 如果没有找到有效的图片路径，显示错误
                        console.error('无法加载图片：找不到有效的图片路径');
                        message.error('无法加载图片：找不到有效的图片路径');
                      }}
                    >
                      <div className="component-thumbnail">
                        {/* 根据页面类型和数据结构选择正确的图片源 */}
                        {pageType === 'matting' && getTaskComponent(selectedImage, 'sourceImagePanel') ? (
                          // 针对matting页面使用components结构
                          <img
                            src={getServerImageUrl(selectedImage, 'sourceImagePanel') ||
                                getTaskComponent(selectedImage, 'sourceImagePanel.originalImage') ||
                                getTaskComponent(selectedImage, 'sourceImagePanel.url')}
                            alt="原始图片缩略图"
                            onError={(e) => {
                              console.error('原始图片缩略图加载失败:', e.target.src);
                              // 使用与其他组件一致的错误处理方式，移除硬编码路径
                              e.target.onerror = null; // 防止无限循环
                            }}
                          />
                        ) : (
                          // 使用sourceImagePanel组件结构
                          <img
                            src={getServerImageUrl(selectedImage, 'sourceImagePanel') ||
                                getTaskComponent(selectedImage, 'sourceImagePanel.originalImage') ||
                                getTaskComponent(selectedImage, 'sourceImagePanel.url') ||
                                ''}
                            alt="原始图片缩略图"
                            style={{ display: 'block', width: '100%', height: '100%', objectFit: 'cover' }}
                            onLoad={(e) => {
                              console.log('原始图片缩略图加载成功');
                            }}
                            onError={(e) => {
                              console.error('原始图片缩略图加载失败:', e.target.src);
                              // 移除使用图标的设计，防止无限循环
                              e.target.onerror = null; // 防止无限循环
                            }}
                          />
                        )}
                      </div>
                      <div className="thumbnail-overlay">
                        <MdOutlineZoomOutMap />
                      </div>
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">图片ID：</span>
                        <div className="value-with-copy">
                          <span className="value">
                            {pageType === 'matting' ?
                              getImageIdFromTask(selectedImage, 'sourceImagePanel') :
                              getImageIdFromTask(selectedImage, 'sourceImagePanel')
                            }
                          </span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              const idToCopy = pageType === 'matting' ?
                                getImageIdFromTask(selectedImage, 'sourceImagePanel') :
                                getImageIdFromTask(selectedImage, 'sourceImagePanel');

                              navigator.clipboard.writeText(idToCopy);
                              message.success('ID已复制到剪贴板');
                            }}
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                      <div className="info-row">
                        <span className="label">文件名：</span>
                        <div className="value-with-copy">
                          <span className="value">
                            {pageType === 'matting' ?
                              getTaskComponent(selectedImage, 'sourceImagePanel.fileInfo.name') ||
                              getTaskComponent(selectedImage, 'sourceImagePanel.serverFileName') || '未知' :
                              getTaskComponent(selectedImage, 'sourceImagePanel.serverFileName') || '未知'}
                          </span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              const fileName = pageType === 'matting' ?
                                getTaskComponent(selectedImage, 'sourceImagePanel.fileInfo.name') ||
                                getTaskComponent(selectedImage, 'sourceImagePanel.serverFileName') || '' :
                                getTaskComponent(selectedImage, 'sourceImagePanel.serverFileName') || '';

                              navigator.clipboard.writeText(fileName);
                              message.success('已复制文件名');
                            }}
                            title="复制文件名"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* 款式描述 - 在matting页面显示，作为原始图片组件的一部分 */}
                  {pageType === 'matting' && (() => {
                    const maskPanels = getAllMaskDescriptionPanels(selectedImage);
                    const maskPanel = maskPanels[0];
                    if (maskPanel) {
                      return (
                        <div className="info-item" style={{ marginTop: '-8px', paddingTop: '8px', borderTop: '1px solid var(--border-light)' }}>
                          <div className="info-content" style={{ width: '100%' }}>
                            <div className="info-row" style={{ marginBottom: '8px' }}>
                              <span className="label" style={{ fontWeight: '500' }}>款式描述：</span>
                            </div>
                            {maskPanel.selectedTag ? (
                              <div className="info-row">
                                <span className="label">已选择标签:</span>
                                <div className="tag-display">
                                  <span className="tag-item">{maskPanel.selectedTag}</span>
                                </div>
                              </div>
                            ) : (
                              <div className="info-row">
                                <span className="value">未设置</span>
                              </div>
                            )}
                            {maskPanel.customText && (
                              <div className="info-row">
                                <span className="label">自定义描述:</span>
                                <div className="value-with-copy">
                                  <span className="value">{maskPanel.customText}</span>
                                  <button
                                    className="copy-id-btn"
                                    onClick={() => {
                                      navigator.clipboard.writeText(maskPanel.customText || '');
                                      message.success('已复制自定义描述');
                                    }}
                                    title="复制描述文本"
                                  >
                                    <MdContentCopy />
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    }
                    return null;
                  })()}
                </div>
              )}

              {/* 消除笔页面原始图片组件 */}
              {pageType === 'inpaint' && selectedImage && getTaskComponent(selectedImage, 'modelMaskPanel') && (
                <div className="info-group">
                  <h3 className="group-title">原始图片</h3>
                  <div className="info-item with-thumbnail">
                    <div
                      className="component-thumbnail-wrapper"
                      onClick={() => {
                        openPreview(selectedImage, 'modelMaskPanel');
                      }}
                    >
                      <div className="component-thumbnail">
                        <img
                          src={getServerImageUrl(selectedImage, 'modelMaskPanel') ||
                              getTaskComponent(selectedImage, 'modelMaskPanel.originalImage') ||
                              getTaskComponent(selectedImage, 'modelMaskPanel.url') ||
                              ''}
                          alt="原始图片缩略图"
                          style={{ display: 'block', width: '100%', height: '100%', objectFit: 'cover' }}
                          onLoad={(e) => {
                            console.log('消除笔原始图片缩略图加载成功');
                          }}
                          onError={(e) => {
                            console.error('消除笔原始图片缩略图加载失败:', e.target.src);
                            e.target.onerror = null; // 防止无限循环
                          }}
                        />
                      </div>
                      <div className="thumbnail-overlay">
                        <MdOutlineZoomOutMap />
                      </div>
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">图片ID：</span>
                        <div className="value-with-copy">
                          <span className="value">
                            {getImageIdFromTask(selectedImage, 'modelMaskPanel')}
                          </span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              const idToCopy = getImageIdFromTask(selectedImage, 'modelMaskPanel');
                              navigator.clipboard.writeText(idToCopy);
                              message.success('ID已复制到剪贴板');
                            }}
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                      <div className="info-row">
                        <span className="label">文件名：</span>
                        <div className="value-with-copy">
                          <span className="value">
                            {getTaskComponent(selectedImage, 'modelMaskPanel.serverFileName') || '未知'}
                          </span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getTaskComponent(selectedImage, 'modelMaskPanel.serverFileName') || '');
                              message.success('已复制文件名');
                            }}
                            title="复制文件名"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    {/* 消除笔蒙版信息 */}
                    {(() => {
                      const hasMask = getTaskComponent(selectedImage, 'modelMaskPanel.hasMask');
                      return (
                        <>
                        <div className="info-item with-thumbnail" style={{ marginTop: '-8px', paddingTop: '8px', borderTop: '1px solid var(--border-light)' }}>
                          <div
                            className="component-thumbnail-wrapper"
                            onClick={() => {
                              if (getTaskComponent(selectedImage, 'modelMaskPanel.maskData') || getTaskComponent(selectedImage, 'modelMaskPanel.maskPath')) {
                                const previewUrl = getTaskComponent(selectedImage, 'modelMaskPanel.maskData') || getTaskComponent(selectedImage, 'modelMaskPanel.maskPath');
                                setPreviewImages([previewUrl]);
                                setCurrentPreviewIndex(0);
                                setPreviewImage(previewUrl);
                                setShowPreviewModal(true);
                              }
                            }}
                          >
                            <div className="component-thumbnail">
                              <img
                                src={getTaskComponent(selectedImage, 'modelMaskPanel.maskData') || getTaskComponent(selectedImage, 'modelMaskPanel.maskPath') || 'https://file.aibikini.cn/config/icons/auto-mask.png'}
                                alt="消除笔蒙版缩略图"
                              />
                            </div>
                            {(getTaskComponent(selectedImage, 'modelMaskPanel.maskData') || getTaskComponent(selectedImage, 'modelMaskPanel.maskPath')) && (
                              <div className="thumbnail-overlay">
                                <MdOutlineZoomOutMap />
                              </div>
                            )}
                          </div>
                          <div className="info-content">
                            <div className="info-row" style={{ marginBottom: '8px' }}>
                              <span className="label" style={{ fontWeight: '500' }}>蒙版：</span>
                            </div>
                            {hasMask ? (
                              <div className="info-row">
                                <span className="label">蒙版类型：</span>
                                <span className="value">手动绘制</span>
                              </div>
                            ) : (
                              <div className="info-row">
                                <span className="label">蒙版类型：</span>
                                <span className="value">自动生成</span>
                              </div>
                            )}
                          </div>
                        </div>
                        </>
                      );
                    })()}
                  </div>
                </div>
              )}

              {/* 面料信息组 - 仅在fabric页面显示 */}
              {pageType === 'fabric' && getTaskComponent(selectedImage, 'fabricPanel') && (
                <div className="info-group">
                  <h3 className="group-title">面料</h3>
                  <div className="info-item with-thumbnail">
                    <div
                      className="component-thumbnail-wrapper"
                      onClick={() => {
                        const previewUrl =  getTaskComponent(selectedImage, 'fabricPanel.url') ||
                        getTaskComponent(selectedImage, 'fabricPanel.originalImage')
                                   ;
                        if (previewUrl) {
                          setPreviewImages([previewUrl]);
                          setCurrentPreviewIndex(0);
                          setPreviewImage(previewUrl);
                          setShowPreviewModal(true);
                        }
                      }}
                    >
                      <div className="component-thumbnail">
                        <img
                          src={getTaskComponent(selectedImage, 'fabricPanel.url') ||
                            getTaskComponent(selectedImage, 'fabricPanel.originalImage') ||
                              '/images/icons/fabric.png'}
                          alt="面料缩略图"
                        />
                      </div>
                      <div className="thumbnail-overlay">
                        <MdOutlineZoomOutMap />
                      </div>
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">图片ID：</span>
                        <div className="value-with-copy">
                          <span className="value">
                            {getImageIdFromTask(selectedImage, 'fabricPanel')}
                          </span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getImageIdFromTask(selectedImage, 'fabricPanel'));
                              message.success('已复制图片ID');
                            }}
                            title="复制图片ID"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                      <div className="info-row">
                        <span className="label">文件名：</span>
                        <div className="value-with-copy">
                                                      <span className="value">
                              {getTaskComponent(selectedImage, 'fabricPanel.serverFileName') || '未知'}
                            </span>
                            <button
                              className="copy-id-btn"
                              onClick={() => {
                                navigator.clipboard.writeText(getTaskComponent(selectedImage, 'fabricPanel.serverFileName') || '');
                                message.success('已复制文件名');
                              }}
                              title="复制文件名"
                            >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 参考图信息组 - 仅在时尚大片页面显示 */}
              {pageType === 'fashion' && getTaskComponent(selectedImage, 'referencePanel') && (
                <div className="info-group">
                  <h3 className="group-title">参考图</h3>
                  <div className="info-item with-thumbnail">
                    <div
                      className="component-thumbnail-wrapper"
                      onClick={() => {
                        const previewUrl = getTaskComponent(selectedImage, 'referencePanel.originalImage') || getTaskComponent(selectedImage, 'referencePanel.url');
                        if (previewUrl) {
                          setPreviewImages([previewUrl]);
                          setCurrentPreviewIndex(0);
                          setPreviewImage(previewUrl);
                          setShowPreviewModal(true);
                        }
                      }}
                    >
                      <div className="component-thumbnail">
                        <img
                          src={getTaskComponent(selectedImage, 'referencePanel.originalImage') || getTaskComponent(selectedImage, 'referencePanel.url')}
                          alt="参考图缩略图"
                        />
                      </div>
                      <div className="thumbnail-overlay">
                        <MdOutlineZoomOutMap />
                      </div>
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">图片ID：</span>
                        <div className="value-with-copy">
                          <span className="value">
                            {getImageIdFromTask(selectedImage, 'referencePanel')}
                          </span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getImageIdFromTask(selectedImage, 'referencePanel'));
                              message.success('已复制图片ID');
                            }}
                            title="复制图片ID"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                      <div className="info-row">
                        <span className="label">文件名：</span>
                        <div className="value-with-copy">
                                                      <span className="value">
                              {getTaskComponent(selectedImage, 'referencePanel.serverFileName') || '未知'}
                            </span>
                            <button
                              className="copy-id-btn"
                              onClick={() => {
                                navigator.clipboard.writeText(getTaskComponent(selectedImage, 'referencePanel.serverFileName') || '');
                                message.success('已复制文件名');
                              }}
                              title="复制文件名"
                            >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 爆款开发页面的版型参考信息 */}
              {pageType === 'trending' && getTaskComponent(selectedImage, 'patternReferencePanel.url') && (() => {
                const patternUrl = getTaskComponent(selectedImage, 'patternReferencePanel.url');
                const patternComponent = getTaskComponent(selectedImage, 'patternReferencePanel');
                const isPatternEmpty = isDefaultEmptyImage(patternUrl, patternComponent);
                
                return !isPatternEmpty;
              })() && (
                <div className="info-group">
                  <h3 className="group-title">版型参考</h3>
                  <div className="info-item with-thumbnail">
                    <div
                      className="component-thumbnail-wrapper"
                      onClick={() => {
                        const previewUrl = getTaskComponent(selectedImage, 'patternReferencePanel.url') || getTaskComponent(selectedImage, 'patternReferencePanel.url');
                        if (previewUrl) {
                          setPreviewImages([previewUrl]);
                          setCurrentPreviewIndex(0);
                          setPreviewImage(previewUrl);
                          setShowPreviewModal(true);
                        }
                      }}
                    >
                      <div className="component-thumbnail">
                        <img
                          src={getTaskComponent(selectedImage, 'patternReferencePanel.url') || getTaskComponent(selectedImage, 'patternReferencePanel.url')}
                          alt="版型参考图缩略图"
                        />
                      </div>
                      <div className="thumbnail-overlay">
                        <MdOutlineZoomOutMap />
                      </div>
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">图片ID：</span>
                        <div className="value-with-copy">
                          <span className="value">
                            {getImageIdFromTask(selectedImage, 'patternReferencePanel')}
                          </span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getImageIdFromTask(selectedImage, 'patternReferencePanel'));
                              message.success('已复制图片ID');
                            }}
                            title="复制图片ID"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                      <div className="info-row">
                        <span className="label">文件名：</span>
                        <div className="value-with-copy">
                                                      <span className="value">
                              {getTaskComponent(selectedImage, 'patternReferencePanel.serverFileName') || '未知'}
                            </span>
                            <button
                              className="copy-id-btn"
                              onClick={() => {
                                navigator.clipboard.writeText(getTaskComponent(selectedImage, 'patternReferencePanel.serverFileName') || '');
                                message.success('已复制文件名');
                              }}
                              title="复制文件名"
                            >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 爆款开发页面的印花参考信息 */}
              {pageType === 'trending' && getTaskComponent(selectedImage, 'printingReferencePanel.url') && (() => {
                const printingUrl = getTaskComponent(selectedImage, 'printingReferencePanel.url');
                const printingComponent = getTaskComponent(selectedImage, 'printingReferencePanel');
                const isPrintingEmpty = isDefaultEmptyImage(printingUrl, printingComponent);
                
                return !isPrintingEmpty;
              })() && (
                <div className="info-group">
                  <h3 className="group-title">面料印花参考</h3>
                  <div className="info-item with-thumbnail">
                    <div
                      className="component-thumbnail-wrapper"
                      onClick={() => {
                        const previewUrl = getTaskComponent(selectedImage, 'printingReferencePanel.url') || getTaskComponent(selectedImage, 'printingReferencePanel.url');
                        if (previewUrl) {
                          setPreviewImages([previewUrl]);
                          setCurrentPreviewIndex(0);
                          setPreviewImage(previewUrl);
                          setShowPreviewModal(true);
                        }
                      }}
                    >
                      <div className="component-thumbnail">
                        <img
                          src={getTaskComponent(selectedImage, 'printingReferencePanel.url') || getTaskComponent(selectedImage, 'printingReferencePanel.url')}
                          alt="印花参考图缩略图"
                        />
                      </div>
                      <div className="thumbnail-overlay">
                        <MdOutlineZoomOutMap />
                      </div>
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">图片ID：</span>
                        <div className="value-with-copy">
                          <span className="value">
                            {getImageIdFromTask(selectedImage, 'printingReferencePanel')}
                          </span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getImageIdFromTask(selectedImage, 'printingReferencePanel'));
                              message.success('已复制图片ID');
                            }}
                            title="复制图片ID"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                      <div className="info-row">
                        <span className="label">文件名：</span>
                        <div className="value-with-copy">
                                                      <span className="value">
                              {getTaskComponent(selectedImage, 'printingReferencePanel.serverFileName') || '未知'}
                            </span>
                            <button
                              className="copy-id-btn"
                              onClick={() => {
                                navigator.clipboard.writeText(getTaskComponent(selectedImage, 'printingReferencePanel.serverFileName') || '');
                                message.success('已复制文件名');
                              }}
                              title="复制文件名"
                            >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 前景图信息组 - 仅在背景替换页面显示 */}
              {pageType === 'background' && getTaskComponent(selectedImage, 'foregroundPanel.originalImage') && (
                <div className="info-group">
                  <h3 className="group-title">前景图</h3>
                  <div className="info-item with-thumbnail">
                    <div
                      className="component-thumbnail-wrapper"
                      onClick={() => {
                        // 使用安全的预览函数
                        openPreview(selectedImage, 'foregroundPanel');
                      }}
                    >
                      <div className="component-thumbnail">
                        <img
                          src={getServerImageUrl(selectedImage, 'foregroundPanel') ||
                              getTaskComponent(selectedImage, 'foregroundPanel.originalImage') ||
                              getTaskComponent(selectedImage, 'foregroundPanel.url')}
                          alt="前景图缩略图"
                          onError={(e) => {
                            console.error('缩略图加载失败:', e.target.src);
                            // 防止无限循环错误
                            e.target.onerror = null;
                          }}
                        />
                      </div>
                      <div className="thumbnail-overlay">
                        <MdOutlineZoomOutMap />
                      </div>
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">图片ID：</span>
                        <div className="value-with-copy">
                          <span className="value">{getImageIdFromTask(selectedImage, 'foregroundPanel')}</span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getImageIdFromTask(selectedImage, 'foregroundPanel'));
                              message.success('已复制图片ID');
                            }}
                            title="复制图片ID"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                      <div className="info-row">
                        <span className="label">文件名：</span>
                        <div className="value-with-copy">
                          <span className="value">{getTaskComponent(selectedImage, 'foregroundPanel.serverFileName') || '未知'}</span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getTaskComponent(selectedImage, 'foregroundPanel.serverFileName') || '');
                              message.success('已复制文件名');
                            }}
                            title="复制文件名"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 模特原图组件 - 仅在模特换装页面、手部修复页面、换姿势页面和换脸页面显示 */}
              {(pageType === 'try-on' || pageType === 'detail-migration' || pageType === 'hand-fix' || pageType === 'change-posture' || pageType === 'change-face') && getTaskComponent(selectedImage, 'modelMaskPanel') && (
                <div className="info-group">
                  <h3 className="group-title">{pageType === 'detail-migration' ? '模特图片' : pageType === 'change-face' ? '模特图片' : '模特原图'}</h3>
                  <div className="info-item with-thumbnail">
                    <div
                      className="component-thumbnail-wrapper"
                      onClick={() => {
                        const previewUrl = getServerImageUrl(selectedImage, 'modelMaskPanel') ||
                            getTaskComponent(selectedImage, 'modelMaskPanel.originalImage') ||
                            getTaskComponent(selectedImage, 'modelMaskPanel.url');
                        if (previewUrl) {
                          setPreviewImages([previewUrl]);
                          setCurrentPreviewIndex(0);
                          setPreviewImage(previewUrl);
                          setShowPreviewModal(true);
                        }
                      }}
                    >
                      <div className="component-thumbnail">
                        <img
                          src={getServerImageUrl(selectedImage, 'modelMaskPanel') ||
                              getTaskComponent(selectedImage, 'modelMaskPanel.originalImage') ||
                              getTaskComponent(selectedImage, 'modelMaskPanel.url') ||
                              '/images/icons/model.png'}
                          alt="模特原图缩略图"
                          onError={(e) => {
                            console.error('模特原图缩略图加载失败:', e.target.src);
                            e.target.onerror = null; // 防止无限循环
                          }}
                        />
                      </div>
                      <div className="thumbnail-overlay">
                        <MdOutlineZoomOutMap />
                      </div>
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">图片ID：</span>
                        <div className="value-with-copy">
                          <span className="value">{getImageIdFromTask(selectedImage, 'modelMaskPanel')}</span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getImageIdFromTask(selectedImage, 'modelMaskPanel'));
                              message.success('已复制图片ID');
                            }}
                            title="复制图片ID"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                      <div className="info-row">
                        <span className="label">文件名：</span>
                        <div className="value-with-copy">
                          <span className="value">{getTaskComponent(selectedImage, 'modelMaskPanel.serverFileName') || '未知'}</span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getTaskComponent(selectedImage, 'modelMaskPanel.serverFileName') || '');
                              message.success('已复制文件名');
                            }}
                            title="复制文件名"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* 模特款式描述 - 仅在模特换装页面且自动蒙版模式下显示 */}
                  {pageType === 'try-on' && !getTaskComponent(selectedImage, 'modelMaskPanel.hasMask') && (() => {
                    const modelMaskPanels = getAllMaskDescriptionPanels(selectedImage).filter(panel => 
                      panel.name === '模特款式描述' || panel.name === 'Model Style Description'
                    );
                    let modelMaskPanel = modelMaskPanels[0];
                    
                    // 如果找不到特定名称的组件，就显示第二个找到的款式描述组件（如果有的话）
                    if (!modelMaskPanel) {
                      const allMaskPanels = getAllMaskDescriptionPanels(selectedImage);
                      modelMaskPanel = allMaskPanels[1]; // 第二个组件通常是模特款式描述
                    }
                    
                    if (modelMaskPanel) {
                      return (
                        <div className="info-item" style={{ marginTop: '-8px', paddingTop: '8px', borderTop: '1px solid var(--border-light)' }}>
                          <div className="info-content" style={{ width: '100%' }}>
                            <div className="info-row" style={{ marginBottom: '8px' }}>
                              <span className="label" style={{ fontWeight: '500' }}>款式描述：</span>
                            </div>
                            {modelMaskPanel.selectedTag ? (
                              <div className="info-row">
                                <span className="label">已选择标签:</span>
                                <div className="tag-display">
                                  <span className="tag-item">
                                    {modelMaskPanel.selectedTag}
                                  </span>
                                </div>
                              </div>
                            ) : (
                              <div className="info-row">
                                <span className="value">未设置</span>
                              </div>
                            )}

                            {modelMaskPanel.customText && (
                              <div className="info-row">
                                <span className="label">自定义描述:</span>
                                <div className="value-with-copy">
                                  <span className="value">
                                    {modelMaskPanel.customText}
                                  </span>
                                  <button
                                    className="copy-id-btn"
                                    onClick={() => {
                                      navigator.clipboard.writeText(modelMaskPanel.customText || '');
                                      message.success('已复制自定义描述');
                                    }}
                                    title="复制描述文本"
                                  >
                                    <MdContentCopy />
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    }
                    return null;
                  })()}

                  {/* 模特图片蒙版信息 - 在模特换装页面、细节迁移页面和手部修复页面显示 */}
                  {(pageType === 'try-on' || pageType === 'detail-migration' || pageType === 'hand-fix') && getTaskComponent(selectedImage, 'modelMaskPanel') && (() => {
                    const hasMask = getTaskComponent(selectedImage, 'modelMaskPanel.hasMask');
                    return (
                      <>
                      <div className="info-item with-thumbnail" style={{ marginTop: '-8px', paddingTop: '8px', borderTop: '1px solid var(--border-light)' }}>
                        <div
                          className="component-thumbnail-wrapper"
                          onClick={() => {
                            if (getTaskComponent(selectedImage, 'modelMaskPanel.maskData') || getTaskComponent(selectedImage, 'modelMaskPanel.maskPath')) {
                              const previewUrl = getTaskComponent(selectedImage, 'modelMaskPanel.maskData') || getTaskComponent(selectedImage, 'modelMaskPanel.maskPath');
                              setPreviewImages([previewUrl]);
                              setCurrentPreviewIndex(0);
                              setPreviewImage(previewUrl);
                              setShowPreviewModal(true);
                            }
                          }}
                        >
                          <div className="component-thumbnail">
                            <img
                              src={getTaskComponent(selectedImage, 'modelMaskPanel.maskData') || getTaskComponent(selectedImage, 'modelMaskPanel.maskPath') || 'https://file.aibikini.cn/config/icons/auto-mask.png'}
                              alt="模特图片蒙版缩略图"
                            />
                          </div>
                          {(getTaskComponent(selectedImage, 'modelMaskPanel.maskData') || getTaskComponent(selectedImage, 'modelMaskPanel.maskPath')) && (
                            <div className="thumbnail-overlay">
                              <MdOutlineZoomOutMap />
                            </div>
                          )}
                        </div>
                        <div className="info-content">
                          <div className="info-row" style={{ marginBottom: '8px' }}>
                            <span className="label" style={{ fontWeight: '500' }}>蒙版：</span>
                          </div>
                          {hasMask ? (
                            <div className="info-row">
                              <span className="label">蒙版类型：</span>
                              <span className="value">手动绘制</span>
                            </div>
                          ) : (
                            <div className="info-row">
                              <span className="label">蒙版类型：</span>
                              <span className="value">自动生成</span>
                            </div>
                          )}
                        </div>
                      </div>
                      {/* 蒙版扩张，仅自动蒙版时显示 */}
                      {pageType === 'try-on' && !hasMask && (
                        <div className="info-item" style={{ marginTop: '-8px', paddingTop: '8px', borderTop: '1px solid var(--border-light)' }}>
                          <div className="mask-expander-info">
                            <div className="weight-slider-row">
                              <div className="weight-item-label">扩张值</div>
                              <div className="weight-value">
                                {(() => {
                                  const expandValue = getTaskComponent(selectedImage, 'maskExpanderPanel.expandValue');
                                  return expandValue ? `${expandValue}px` : '未设置';
                                })()}
                              </div>
                            </div>
                            <div className="slider-container">
                              <div className="slider-track">
                                <div
                                  className="slider-fill"
                                  style={{
                                    width: getTaskComponent(selectedImage, 'maskExpanderPanel.expandValue') ?
                                      `${(getTaskComponent(selectedImage, 'maskExpanderPanel.expandValue') / 100) * 100}%` :
                                      '0%'
                                  }}
                                ></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      </>
                    );
                  })()}
                </div>
              )}



              {/* 时尚大片页面的模特信息组 */}
              {(pageType === 'virtual') && getTaskComponent(selectedImage, 'modelPanel') && (
                <div className="info-group">
                  <h3 className="group-title">模特</h3>
                  <div className="info-item with-thumbnail">
                    <div
                      className={`component-thumbnail-wrapper ${getTaskComponent(selectedImage, 'modelPanel.type') === 'custom' ? 'custom-type' : ''}`}
                      onClick={() => {
                        // 虚拟模特页面不支持放大显示
                        if (pageType !== 'virtual' && getTaskComponent(selectedImage, 'modelPanel.type') !== 'custom') {
                          // 使用服务器URL
                          const previewUrl = getServerImageUrl(selectedImage, 'modelPanel') ||
                                         getTaskComponent(selectedImage, 'modelPanel.preview') ||
                                         '/images/icons/model.png';
                          setPreviewImages([previewUrl]);
                          setCurrentPreviewIndex(0);
                          setPreviewImage(previewUrl);
                          setShowPreviewModal(true);
                        }
                      }}
                    >
                      <div className="component-thumbnail">
                        <img
                          src={pageType === 'virtual' ?
                               '/images/icons/model-custom.png' : // 虚拟模特页面始终使用模特自定义图标
                               (getTaskComponent(selectedImage, 'modelPanel.type') === 'custom' ?
                                '/images/icons/model-custom.png' :
                                (getTaskComponent(selectedImage, 'modelPanel.preview') || '/images/icons/model.png'))
                          }
                          alt="模特缩略图"
                        />
                      </div>
                      {/* 虚拟模特页面不显示放大按钮，其他页面根据类型决定 */}
                      {pageType !== 'virtual' && getTaskComponent(selectedImage, 'modelPanel.type') !== 'custom' && (
                        <div className="thumbnail-overlay">
                          <MdOutlineZoomOutMap />
                        </div>
                      )}
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="value">
                          {getTaskComponent(selectedImage, 'modelPanel.type') === 'custom' ?
                            '自定义模特' :
                            (pageType === 'virtual' ? '自定义要求' :
                            `#${getTaskComponent(selectedImage, 'modelPanel.componentId') || '未知'} ${getTaskComponent(selectedImage, 'modelPanel.name') || ''}`)
                          }
                        </span>
                        <button
                          className="text-button"
                          onClick={(e) => {
                            e.stopPropagation();
                            const buttonRect = e.currentTarget.getBoundingClientRect();
                            setModelPopupPosition({
                              left: buttonRect.right + 12,
                              top: buttonRect.top
                            });
                            setShowModelText(true);
                            setShowSceneText(false);
                            setShowAdvancedText(false);
                          }}
                        >
                          <MdOutlineDescription />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 灵感探索页面的服装属性信息组 */}
              {pageType === 'inspiration' && getTaskComponent(selectedImage, 'clothingAttributesPanel') && (
                <div className="info-group">
                  <h3 className="group-title">灵感探索方向</h3>
                  <div className="info-item with-thumbnail">
                    <div className="component-thumbnail-wrapper">
                      <div className="component-thumbnail">
                        <img
                          src="https://file.aibikini.cn/config/icons/inspiration-direction-active.png"
                          alt="灵感探索方向图标"
                        />
                      </div>
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="value">
                          已设置
                        </span>
                        <button
                          className="text-button"
                          onClick={(e) => {
                            e.stopPropagation();
                            const buttonRect = e.currentTarget.getBoundingClientRect();
                            setAdvancedPopupPosition({
                              left: buttonRect.right + 12,
                              top: buttonRect.top
                            });
                            setShowAdvancedText(true);
                            setShowModelText(false);
                            setShowSceneText(false);
                          }}
                        >
                          <MdOutlineDescription />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 文本弹窗组件 - 模特描述 */}
              {showModelText && pageType !== 'try-on' && (
                <TextPopup
                  title="模特描述"
                  position={modelPopupPosition}
                  onClose={() => setShowModelText(false)}
                  positivePrompt={
                    (pageType === 'virtual' && getTaskComponent(selectedImage, 'modelPanel.prompt')) ||
                    getTaskComponent(selectedImage, 'modelPanel.prompt') ||
                    ''
                  }
                  negativePrompt={
                    (pageType === 'virtual' && getTaskComponent(selectedImage, 'modelPanel.negative_prompt')) ||
                    getTaskComponent(selectedImage, 'modelPanel.negativePrompt') ||
                    ''
                  }
                  modelTags={
                    // 虚拟模特页面不传递标签数组
                    pageType === 'virtual' ?
                    [] :
                    (getTaskComponent(selectedImage, 'modelPanel.tags') || [])
                  }
                  isVisible={showModelText}
                />
              )}



              {/* 灵感探索页面的服装属性文本弹窗 */}
              {showAdvancedText && pageType === 'inspiration' && getTaskComponent(selectedImage, 'clothingAttributesPanel') && (
                <TextPopup
                  title="灵感探索方向"
                  position={advancedPopupPosition}
                  onClose={() => setShowAdvancedText(false)}
                  positivePrompt={getTaskComponent(selectedImage, 'clothingAttributesPanel.attributes.description')}
                  negativePrompt={getTaskComponent(selectedImage, 'clothingAttributesPanel.attributes.negativeDescription')}
                  modelTags={getTaskComponent(selectedImage, 'clothingAttributesPanel.attributes.tags') || []}
                  isVisible={showAdvancedText}
                />
              )}

              {/* 场景信息 - 仅在背景页面显示 */}
              {(pageType === 'background') && getTaskComponent(selectedImage, 'scenePanel') && (
                <div className="info-group">
                  <h3 className="group-title">场景</h3>
                  <div className="info-item with-thumbnail">
                    <div
                      className="component-thumbnail-wrapper"
                      onClick={() => {
                        // 仅当不是自定义场景时才允许预览
                        if (getTaskComponent(selectedImage, 'scenePanel.type') !== 'custom') {
                          const previewUrl = getTaskComponent(selectedImage, 'scenePanel.image');
                          if (previewUrl) {
                            setPreviewImages([previewUrl]);
                            setCurrentPreviewIndex(0);
                            setPreviewImage(previewUrl);
                            setShowPreviewModal(true);
                          }
                        }
                      }}
                    >
                      <div className="component-thumbnail">
                        <img
                          src={
                            getTaskComponent(selectedImage, 'scenePanel.type') === 'custom' ?
                            '/images/icons/scene-custom.png' :
                            getTaskComponent(selectedImage, 'scenePanel.type') === 'reference' ?
                            getTaskComponent(selectedImage, 'scenePanel.image') :
                            getTaskComponent(selectedImage, 'scenePanel.image')
                          }
                          alt="场景缩略图"
                        />
                      </div>
                      {getTaskComponent(selectedImage, 'scenePanel.type') !== 'custom' && (
                        <div className="thumbnail-overlay">
                          <MdOutlineZoomOutMap />
                        </div>
                      )}
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="value scene-value">
                          {
                            getTaskComponent(selectedImage, 'scenePanel.name') === '场景参考图'
                              ? '已上传 场景参考图'
                              : (
                                getTaskComponent(selectedImage, 'scenePanel.type') === 'custom'
                                  ? '自定义场景'
                                  : `${getTaskComponent(selectedImage, 'scenePanel.sceneReferenceId') ? `#${getTaskComponent(selectedImage, 'scenePanel.sceneReferenceId')} ` : ''}${getTaskComponent(selectedImage, 'scenePanel.name') || '未知场景'}`
                              )
                          }
                        </span>
                        {/* 仅当场景类型为自定义场景时才显示描述按钮 */}
                        {getTaskComponent(selectedImage, 'scenePanel.type') === 'custom' && (
                          <button
                            className="text-button"
                            onClick={(e) => {
                              e.stopPropagation();
                              const buttonRect = e.currentTarget.getBoundingClientRect();
                              setScenePopupPosition({
                                left: buttonRect.right + 12,
                                top: buttonRect.top
                              });
                              setShowSceneText(true);
                              setShowModelText(false);
                              setShowAdvancedText(false);
                            }}
                          >
                            <MdOutlineDescription />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {(pageType === 'background') && showSceneText && getTaskComponent(selectedImage, 'scenePanel.type') === 'custom' && (
                <TextPopup
                  title="场景描述"
                  position={scenePopupPosition}
                  onClose={() => setShowSceneText(false)}
                  positivePrompt={getTaskComponent(selectedImage, 'scenePanel.prompt') || ''}
                  negativePrompt={getTaskComponent(selectedImage, 'scenePanel.negative_prompt') || ''}
                  isVisible={showSceneText}
                />
              )}

              {/* 款式图信息组 - 仅在款式优化页面和爆款延伸页面显示 */}
              {(pageType === 'optimize' || pageType === 'divergent') && getTaskComponent(selectedImage, 'modelMaskPanel') && (
                <div className="info-group">
                  <h3 className="group-title">款式图</h3>
                  <div className="info-item with-thumbnail">
                    <div
                      className="component-thumbnail-wrapper"
                      onClick={() => {
                        const previewUrl = getTaskComponent(selectedImage, 'modelMaskPanel.originalImage') || getTaskComponent(selectedImage, 'modelMaskPanel.url');
                        setPreviewImages([previewUrl]);
                        setCurrentPreviewIndex(0);
                        setPreviewImage(previewUrl);
                        setShowPreviewModal(true);
                      }}
                    >
                      <div className="component-thumbnail">
                        <img
                          src={getTaskComponent(selectedImage, 'modelMaskPanel.originalImage') || getTaskComponent(selectedImage, 'modelMaskPanel.url')}
                          alt="款式图缩略图"
                        />
                      </div>
                      <div className="thumbnail-overlay">
                        <MdOutlineZoomOutMap />
                      </div>
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">图片ID：</span>
                        <div className="value-with-copy">
                          <span className="value">{getTaskComponent(selectedImage, 'modelMaskPanel.componentId') || '未知'}</span>
                          <button 
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getTaskComponent(selectedImage, 'modelMaskPanel.componentId') || '');
                              message.success('已复制图片ID');
                            }}
                            title="复制图片ID"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                      <div className="info-row">
                        <span className="label">文件名：</span>
                        <div className="value-with-copy">
                          <span className="value">{getTaskComponent(selectedImage, 'modelMaskPanel.serverFileName') || '未知'}</span>
                          <button 
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getTaskComponent(selectedImage, 'modelMaskPanel.serverFileName') || '');
                              message.success('已复制文件名');
                            }}
                            title="复制文件名"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 款式图蒙版信息 - 仅在款式优化页面显示 */}
                  {getTaskComponent(selectedImage, 'modelMaskPanel.hasMask') && (() => {
                    return (
                      <div className="info-item with-thumbnail" style={{ marginTop: '-8px', paddingTop: '8px', borderTop: '1px solid var(--border-light)' }}>
                        <div
                          className="component-thumbnail-wrapper"
                          onClick={() => {
                            if (getTaskComponent(selectedImage, 'modelMaskPanel.maskData') || getTaskComponent(selectedImage, 'modelMaskPanel.maskPath')) {
                              const previewUrl = getTaskComponent(selectedImage, 'modelMaskPanel.maskData') || getTaskComponent(selectedImage, 'modelMaskPanel.maskPath');
                              setPreviewImages([previewUrl]);
                              setCurrentPreviewIndex(0);
                              setPreviewImage(previewUrl);
                              setShowPreviewModal(true);
                            }
                          }}
                        >
                          <div className="component-thumbnail">
                            <img
                              src={getTaskComponent(selectedImage, 'modelMaskPanel.maskData') || getTaskComponent(selectedImage, 'modelMaskPanel.maskPath') || '/images/icons/mask.png'}
                              alt="款式图蒙版缩略图"
                            />
                          </div>
                          {(getTaskComponent(selectedImage, 'modelMaskPanel.maskData') || getTaskComponent(selectedImage, 'modelMaskPanel.maskPath')) && (
                            <div className="thumbnail-overlay">
                              <MdOutlineZoomOutMap />
                            </div>
                          )}
                        </div>
                        <div className="info-content">
                          <div className="info-row" style={{ marginBottom: '8px' }}>
                            <span className="label" style={{ fontWeight: '500' }}>蒙版：</span>
                          </div>
                          <div className="info-row">
                            <span className="label">蒙版类型：</span>
                            <span className="value">手动绘制</span>
                          </div>
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}

                {/* 添加类型选择信息组 - 仅在服装复色页面、换姿势页面、爆款延伸显示 */}
                {pageType === 'change-posture' && getTaskComponent(selectedImage, 'typeSelector') && (
                <div className="info-group">
                  <h3 className="group-title">类型</h3>
                  <div className="info-item">
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">改变的内容：</span>
                        <span className="value">
                          {getTaskComponent(selectedImage, 'typeSelector.options')?.find(
                            opt => opt.value === getTaskComponent(selectedImage, 'typeSelector.value')
                          )?.label || '未知'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {pageType === 'recolor' && getTaskComponent(selectedImage, 'typeSelector') && (
                <div className="info-group">
                  <h3 className="group-title">类型</h3>
                  <div className="info-item">
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">服装色系：</span>
                        <span className="value">
                          {getTaskComponent(selectedImage, 'typeSelector.value') === 1 ? '浅色服装' :
                           getTaskComponent(selectedImage, 'typeSelector.value') === 2 ? '中性色服装' :
                           getTaskComponent(selectedImage, 'typeSelector.value') === 3 ? '深色服装' :
                           '未知类型'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {pageType === 'divergent' && getTaskComponent(selectedImage, 'typeSelector') && (
                <div className="info-group">
                  <h3 className="group-title">类型</h3>
                  <div className="info-item">
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">生成模式：</span>
                        <span className="value">
                          {getTaskComponent(selectedImage, 'typeSelector.value') === 1 ? '自动生成' :
                           getTaskComponent(selectedImage, 'typeSelector.value') === 2 ? '自定义描述' :
                           '未知类型'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}


              {/* 添加服装复色特有的颜色信息组 - 始终在recolor页面显示 */}
              {pageType === 'recolor' && getTaskComponent(selectedImage, 'colorPanel') && (
                <div className="info-group">
                  <h3 className="group-title">颜色</h3>
                  <div className="info-item with-thumbnail">
                    <div className="component-thumbnail">
                      {getTaskComponent(selectedImage, 'colorPanel.selectedColor') || getTaskComponent(selectedImage, 'colorPanel.hex') ? (
                        <div className="color-swatch-large" style={{
                          backgroundColor: getTaskComponent(selectedImage, 'colorPanel.selectedColor') ||
                                          getTaskComponent(selectedImage, 'colorPanel.hex') ||
                                          '#FF3C6A'
                        }}></div>
                      ) : (
                        <div className="color-swatch-large" style={{ backgroundColor: '#FF3C6A' }}></div>
                      )}
                    </div>
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">色号：</span>
                        <div className="value-with-copy">
                          <span className="value">
                            {getTaskComponent(selectedImage, 'colorPanel.selectedColor') ||
                             getTaskComponent(selectedImage, 'colorPanel.hex') ||
                             '#FF3C6A'}
                          </span>
                          <button
                            className="copy-id-btn"
                            onClick={() => {
                              navigator.clipboard.writeText(getTaskComponent(selectedImage, 'colorPanel.selectedColor') || getTaskComponent(selectedImage, 'colorPanel.hex') || '#FF3C6A');
                              message.success('已复制色号');
                            }}
                            title="复制色号"
                          >
                            <MdContentCopy />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {/* 文本描述词信息组 - 同时支持时尚大片、爆款开发、款式优化、虚拟模特、换模特、换姿势和爆款延伸页面（爆款延伸页面仅在自定义描述模式下显示） */}
              {(pageType === 'fashion' || pageType === 'trending' || pageType === 'optimize' || pageType === 'virtual' || pageType === 'change-model' || pageType === 'change-posture' || 
                (pageType === 'divergent' && getTaskComponent(selectedImage, 'typeSelector.value') === 2)) && getTaskComponent(selectedImage, 'textDescriptionPanel') && (
                <div className="info-group">
                  <div className="title-with-button">
                    <h3 className="group-title">描述词</h3>
                    <button 
                      className="copy-id-btn"
                      onClick={() => {
                        navigator.clipboard.writeText(getTaskComponent(selectedImage, 'textDescriptionPanel.prompt') || '未设置描述词');
                        message.success('已复制描述词');
                      }}
                      title="复制描述词"
                    >
                      <MdContentCopy />
                    </button>
                  </div>
                  <div className="info-item">
                    <div className="info-content">
                      <div className="info-row description-text-container">
                        <div className="description-text-wrapper">
                          <textarea 
                            className="description-text-area" 
                            value={getTaskComponent(selectedImage, 'textDescriptionPanel.prompt') || '未设置描述词'}
                            readOnly
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 统一的强度调节组件 - 同时支持时尚大片、爆款开发和爆款延伸页面 */}
              {(pageType === 'fashion' && getTaskComponent(selectedImage, 'weightPanel')) || 
               (pageType === 'trending' && getTaskComponent(selectedImage, 'weightPanel')) ||
               (pageType === 'divergent' && getTaskComponent(selectedImage, 'weightPanel')) ? (
                <div className="info-group">
                  <h3 className="group-title">强度调节</h3>
                  <div className="info-item creative-strength-item">
                    <div className="weight-sliders">
                      {/* 第一个强度滑块 - 参考图/版型 - 在时尚大片页面中隐藏 */}
                      {pageType === 'trending' && (
                        <div className="weight-slider-row">
                          <div className="weight-item-label">
                            版型
                          </div>
                          <div className="weight-value">
                            {(() => {
                              const value = getTaskComponent(selectedImage, 'weightPanel.patternWeight');
                              if (value >= 0.995) {
                                return '1.00';
                              }
                              return value !== undefined ? value.toFixed(2) : '0.50';
                            })()}
                          </div>
                          <div className="slider-container">
                            <div className="slider-track">
                              <div 
                                className="slider-fill" 
                                style={{ 
                                  width: `${(getTaskComponent(selectedImage, 'weightPanel.patternWeight') || 0.5) * 100}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {/* 第二个强度滑块 - 描述词/印花 */}
                      <div className="weight-slider-row">
                        <div className="weight-item-label">
                          {pageType === 'fashion' ? '描述词' : 
                           pageType === 'divergent' ? '变化' : '印花'}
                        </div>
                        <div className="weight-value">
                          {(() => {
                            if (pageType === 'fashion') {
                              const value = getTaskComponent(selectedImage, 'weightPanel.descriptionWeight');
                              if (value >= 0.995) {
                                return '1.00';
                              }
                              return value !== undefined ? value.toFixed(2) : '0.50';
                            } else if (pageType === 'divergent') {
                              const weights = getTaskComponent(selectedImage, 'weightPanel.weights');
                              const value = weights?.item2;
                              if (value >= 0.995) {
                                return '1.00';
                              }
                              return value !== undefined ? value.toFixed(2) : '0.50';
                            } else {
                              const value = getTaskComponent(selectedImage, 'weightPanel.printingWeight');
                              if (value >= 0.995) {
                                return '1.00';
                              }
                              return value !== undefined ? value.toFixed(2) : '0.50';
                            }
                          })()}
                        </div>
                        <div className="slider-container">
                          <div className="slider-track">
                            <div 
                              className="slider-fill" 
                              style={{ 
                                width: pageType === 'fashion'
                                  ? `${(typeof getTaskComponent(selectedImage, 'weightPanel.descriptionWeight') === 'number' ? getTaskComponent(selectedImage, 'weightPanel.descriptionWeight') : 0) * 100}%`
                                  : pageType === 'divergent'
                                  ? `${(typeof getTaskComponent(selectedImage, 'weightPanel.weights.item2') === 'number' ? getTaskComponent(selectedImage, 'weightPanel.weights.item2') : 0) * 100}%`
                                  : `${(typeof getTaskComponent(selectedImage, 'weightPanel.printingWeight') === 'number' ? getTaskComponent(selectedImage, 'weightPanel.printingWeight') : 0) * 100}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}

              {/* 添加放大倍数与尺寸信息组 - 在upscale、extend和change-model页面显示 */}
              {(pageType === 'upscale' || pageType === 'extend' || pageType === 'change-model') && selectedImage && (
                <div className="info-group">
                  <h3 className="group-title">
                    {pageType === 'extend' ? '扩图比例与尺寸' : 
                     pageType === 'change-model' ? '扩图比例与尺寸' : 
                     '放大倍数与尺寸'}
                  </h3>
                  <div className="info-item">
                    <div className="info-content">
                      <div className="info-row">
                        <span className="label">原图尺寸：</span>
                        <span className="value">
                          {getTaskComponent(selectedImage, 'sourceImagePanel.fileInfo')||getTaskComponent(selectedImage, 'modelMaskPanel.fileInfo') ? 
                            `${getTaskComponent(selectedImage, 'sourceImagePanel.fileInfo.width')||getTaskComponent(selectedImage, 'modelMaskPanel.fileInfo.width')} × ${getTaskComponent(selectedImage, 'sourceImagePanel.fileInfo.height')||getTaskComponent(selectedImage, 'modelMaskPanel.fileInfo.height')}px` : 
                            '未知'}
                        </span>
                      </div>
                      
                      {/* 显示平台信息 - 仅在extend页面且有平台相关信息时显示 */}
                      {pageType === 'extend' && (
                        (() => {
                          // 尝试从不同位置获取平台信息
                          const activePlatform = getTaskComponent(selectedImage, 'platformPanel.activePlatform') || 
                                                getTaskComponent(selectedImage, 'sizePanel.activePlatform');
                          
                          const isInPlatformTab = getTaskComponent(selectedImage, 'sizePanel.activeTab') === 'platform';
                          
                          // 如果有平台信息，显示相应内容
                          if (activePlatform && (isInPlatformTab || !getTaskComponent(selectedImage, 'sizePanel.activeTab'))) {
                            let platformName = '未知平台';
                            switch (activePlatform) {
                              case 'default': platformName = '默认尺寸'; break;
                              case 'amazon': platformName = '亚马逊'; break;
                              case 'walmart': platformName = '沃尔玛'; break;
                              case 'etsy': platformName = 'Etsy'; break;
                              case 'mercado': platformName = '美客多'; break;
                              case 'ebay': platformName = 'eBay'; break;
                              case 'temu': platformName = 'Temu'; break;
                              case 'shopify-square': platformName = 'Shopify方形'; break;
                              case 'shopify-landscape': platformName = 'Shopify横版'; break;
                              case 'shopify-portrait': platformName = 'Shopify竖版'; break;
                              case 'shein': platformName = 'SHEIN'; break;
                              case 'aliexpress': platformName = '速卖通'; break;
                              case 'shopee': platformName = 'Shopee'; break;
                              case 'lazada': platformName = 'Lazada'; break;
                              case 'tiktok': platformName = 'Tiktok Shop'; break;
                              case 'douyin': platformName = '抖音小店'; break;
                              case 'taobao': platformName = '淘宝/天猫'; break;
                              case 'jd': platformName = '京东'; break;
                              case 'wayfair': platformName = 'Wayfair'; break;
                              case 'cdiscount': platformName = 'Cdiscount'; break;
                              case 'jumia': platformName = 'Jumia'; break;
                              case 'rakuten': platformName = 'Rakuten'; break;
                              case 'wish': platformName = 'Wish'; break;
                              default: platformName = `${activePlatform}`; break;
                            }
                            
                            return (
                              <div className="info-row">
                                <span className="label">平台要求：</span>
                                <span className="value">{platformName}</span>
                              </div>
                            );
                          }
                          return null;
                        })()
                      )}
                      
                      {/* 显示宽高比信息 - 仅在extend页面且有宽高比设置并且不是平台模式时显示 */}
                      {pageType === 'extend' && (
                        (() => {
                          // 尝试从不同位置获取宽高比信息
                          const activeRatio = getTaskComponent(selectedImage, 'aspectRatioPanel.activeRatio') || 
                                          getTaskComponent(selectedImage, 'sizePanel.activeRatio');
                          
                          // 判断是否在平台模式下
                          const activePlatform = getTaskComponent(selectedImage, 'platformPanel.activePlatform') || 
                                               getTaskComponent(selectedImage, 'sizePanel.activePlatform');
                          
                          const isInPlatformTab = getTaskComponent(selectedImage, 'sizePanel.activeTab') === 'platform';
                          
                          const isInPlatformMode = activePlatform && (isInPlatformTab || !getTaskComponent(selectedImage, 'sizePanel.activeTab'));
                          
                          // 只有在非平台模式下且有宽高比时显示
                          if (activeRatio && !isInPlatformMode) {
                            return (
                              <div className="info-row">
                                <span className="label">宽高比例：</span>
                                <span className="value">{activeRatio}</span>
                              </div>
                            );
                          }
                          return null;
                        })()
                      )}
                      
                      {/* 如果是自定义尺寸，显示扩图比例 - 仅在非比例模式和非平台模式下显示 */}
                      {(() => {
                        // 只在extend页面处理复杂逻辑
                        if (pageType !== 'extend') {
                          // 非extend页面显示标准的放大倍数
                          return (
                            <div className="info-row">
                              <span className="label">放大倍数：</span>
                              <span className="value">
                                {(() => {
                                  // 获取原图和目标图尺寸
                                  const origWidth = getTaskComponent(selectedImage, 'sourceImagePanel.fileInfo.width') || getTaskComponent(selectedImage, 'modelMaskPanel.fileInfo.width');
                                  const origHeight = getTaskComponent(selectedImage, 'sourceImagePanel.fileInfo.height') || getTaskComponent(selectedImage, 'modelMaskPanel.fileInfo.height');
                                  const targetWidth = getTaskComponent(selectedImage, 'extendSize.width') || getTaskComponent(selectedImage, 'magnificationSize.width');
                                  const targetHeight = getTaskComponent(selectedImage, 'extendSize.height') || getTaskComponent(selectedImage, 'magnificationSize.height');
                                  const scale = getTaskComponent(selectedImage, 'magnificationSize.scale') || getTaskComponent(selectedImage, 'extendSize.scale');
                                  const activeTab = getTaskComponent(selectedImage, 'sizePanel.activeTab');
                                  if (activeTab === 'custom' || activeTab === 'platform') {
                                    return '因长宽比不同无法计算倍数';
                                  }
                                  if (!origWidth || !origHeight || !targetWidth || !targetHeight || !scale) {
                                    return '未知';
                                  }
                                  // 判断长宽比是否一致（允许极小误差）
                                  const origRatio = origWidth / origHeight;
                                  const targetRatio = targetWidth / targetHeight;
                                  if (Math.abs(origRatio - targetRatio) < 0.01) {
                                    return `${parseFloat(scale).toFixed(1)}倍`;
                                  } else {
                                    return '因长宽比不同无法计算倍数';
                                  }
                                })()}
                              </span>
                            </div>
                          );
                        }
                        
                        // 对于extend页面，判断是否应该显示扩图比例
                        // 获取平台和比例信息
                        const activePlatform = getTaskComponent(selectedImage, 'platformPanel.activePlatform') || 
                                           getTaskComponent(selectedImage, 'sizePanel.activePlatform');
                        
                        const isInPlatformTab = getTaskComponent(selectedImage, 'sizePanel.activeTab') === 'platform';
                        
                        const isInPlatformMode = activePlatform && (isInPlatformTab || !getTaskComponent(selectedImage, 'sizePanel.activeTab'));
                        
                        const activeRatio = getTaskComponent(selectedImage, 'aspectRatioPanel.activeRatio') || 
                                        getTaskComponent(selectedImage, 'sizePanel.activeRatio');
                        
                        // 只有在既不是平台模式也不是宽高比模式时才显示扩图比例
                        if (!isInPlatformMode && !activeRatio) {
                          return (
                            <div className="info-row">
                              <span className="label">扩图比例：</span>
                              <span className="value">
                                {(() => {
                                  // 获取当前使用的标签页
                                  const activeTab = getTaskComponent(selectedImage, 'sizePanel.activeTab');
                                  
                                  // 如果是平台尺寸或自定义尺寸标签页，显示为"未知"
                                  if (activeTab === 'platform' || activeTab === 'custom') {
                                    return '未知';
                                  }
                                  
                                  // 只有在scale标签页才显示实际倍数
                                  // 尝试从多个可能的位置获取scale值
                                  const scaleValue = getTaskComponent(selectedImage, 'extendSize.scale');
                                  
                                  return scaleValue ? 
                                    `${parseFloat(scaleValue).toFixed(1)}倍` : 
                                    '未知';
                                })()}
                              </span>
                            </div>
                          );
                        }
                        return null;
                      })()}
                    </div>
                  </div>
                </div>
              )}

              {/* 文件信息组 */}
              <div className="info-group">
                {/* 添加种子信息 - 仅当有真实种子值且不是extract、upscale或recolor页面时显示 */}
                {pageType !== 'extract' && pageType !== 'upscale' && pageType !== 'recolor' && (pageType === 'detail-migration' || getTaskComponent(selectedImage, 'randomSeedSelector.value') >= 0) && (
                  <div className="info-item seed-item">
                    <span className="label">随机种子：</span>
                    <div className="value-with-copy">
                      <span className="value">
                        {getTaskComponent(selectedImage, 'randomSeedSelector.value')}
                      </span>
                      <button 
                        className="copy-id-btn"
                        onClick={() => {
                          const seedValue = getTaskComponent(selectedImage, 'randomSeedSelector.value');
                          navigator.clipboard.writeText(seedValue.toString());
                          message.success('已复制种子值');
                        }}
                        title="复制种子值"
                      >
                        <MdContentCopy />
                      </button>
                    </div>
                  </div>
                )}
                <div className="info-item">
                  <span className="label">图片尺寸：</span>
                  <span className="value">
                    {isLoadingFileInfo ? '加载中...' :
                      (actualFileInfo?.width && actualFileInfo?.height ?
                        `${actualFileInfo.width} × ${actualFileInfo.height}` :
                        (selectedImage.fileInfo?.width && selectedImage.fileInfo?.height ?
                          `${selectedImage.fileInfo.width} × ${selectedImage.fileInfo.height}` :
                          '未知')
                        )
                      }
                    </span>
                </div>
                <div className="info-item">
                  <span className="label">文件大小：</span>
                  <span className="value">
                    {isLoadingFileInfo ? '加载中...' : 
                      (actualFileInfo?.size ? 
                        formatFileSize(actualFileInfo.size) : 
                        (typeof selectedImage.fileInfo?.size === 'number' 
                          ? formatFileSize(selectedImage.fileInfo?.size)
                          : selectedImage.fileInfo?.size || '未知')
                      )
                    }
                  </span>
                </div>
                <div className="info-item">
                  <span className="label">文件格式：</span>
                  <span className="value">
                    {isLoadingFileInfo ? '加载中...' : 
                      (actualFileInfo?.format ? 
                        actualFileInfo.format.replace('image/', '') : 
                        selectedImage.fileInfo?.format || (actualFileInfo?.mimetype ? actualFileInfo.mimetype.replace('image/', '') : 
                        selectedImage.fileInfo?.type || '未知')
                      )
                    }
                  </span>
                </div>
              </div>
            </div>

            {/* 中间图片展示区 */}
            <div className="details-main">
              {renderImages}
            </div>

            {/* 右侧操作栏 */}
            <div className="details-actions">
              {/* 缩略图列表 */}
              <ThumbnailList
                images={taskImages.length > 0 ? taskImages : (() => {
                  // 1. 先从generationTasks中尝试获取任务
                  const task = generationTasks?.find(task => task.taskId === selectedImage.taskId);
                  
                  // 2. 如果找到任务并且有images或generatedImages，使用它们
                  if (task?.images?.length > 0 || task?.generatedImages?.length > 0) {
                    return task.images || task.generatedImages || [];
                  }
                  
                  // 3. 如果没有找到任务的图片数组，但selectedImage有url，
                  // 创建一个只包含当前图片的数组
                  if (selectedImage?.url) {
                    // 构造与images数组格式兼容的单张图片对象
                    return [{
                      url: selectedImage.url,
                      taskId: selectedImage.taskId,
                      imageIndex: 0
                    }];
                  }
                  
                  // 4. 最后兜底，返回空数组
                  return [];
                })()}
                selectedIndex={selectedImage.imageIndex || 0}
                onSelect={(image, index) => {
                  // 重置图片位置和缩放状态
                  setImagePosition({ x: 0, y: 0 });
                  lastPosition.current = { x: 0, y: 0 };
                  // 强制更新缩放值为初始值
                  const validInitialScale = initialScale > 0 ? initialScale : 100;
                  setImageScale(validInitialScale);
                  setIsDragging(false);
                  
                  // 更新选中的图片 - 保留原始selectedImage中的重要属性
                  setSelectedImage(prev => {
                    // 提取当前图片的基本数据
                    const newImageData = {
                      ...image,
                      taskId: prev.taskId,
                      createdAt: prev.createdAt,
                      imageIndex: index
                    };
                    
                    // 保留components和sourceImagePanel等关键属性
                    if (prev.components) {
                      newImageData.components = prev.components;
                    }
                    
                    // 保留特定页面类型的属性
                    if (pageType === 'matting') {
                      if (prev.sourceImagePanel) {
                        newImageData.sourceImagePanel = prev.sourceImagePanel;
                      }
                      if (prev.hasSourceImagePanel !== undefined) {
                        newImageData.hasSourceImagePanel = prev.hasSourceImagePanel;
                      }
                    }
                    
                    // 保留服务器文件名相关属性
                    if (prev.serverFileName) {
                      newImageData.serverFileName = prev.serverFileName;
                    }
                    if (prev.primaryImageFileName) {
                      newImageData.primaryImageFileName = prev.primaryImageFileName;
                    }
                    
                    return newImageData;
                  });
                }}
                style={{ marginTop: '45px' }}
              />

              {/* 缩放控制组件 */}
              <ImageZoomControl 
                key={`zoom-control-${selectedImage?.imageIndex || 0}-${initialScale}`}
                initialScale={initialScale}
                onScaleChange={handleScaleChange}
                onReset={handleReset}
                onPreview={() => {
                  // 设置当前任务的所有图片作为预览图片集
                  console.log('onPreview', generationTasks, selectedImage); 
                  if (!generationTasks || !selectedImage) return;
                  
                  // 优先使用已获取的任务图片
                  if (taskImages.length > 0) {
                    const imageUrls = taskImages.map(img => img.url).filter(Boolean);
                    if (imageUrls.length === 0) return;
                    
                    setPreviewImages(imageUrls);
                    setCurrentPreviewIndex(selectedImage.imageIndex || 0);
                    setPreviewImage(selectedImage.url);
                    setShowPreviewModal(true);
                    return;
                  }
                  
                  // 如果没有已获取的任务图片，使用原来的逻辑
                  const currentTask = generationTasks.find(task => task.taskId === selectedImage?.taskId);
                  
                  // 处理没有图片数组的情况
                  let imageUrls = [];
                  
                  // 1. 如果任务有images或generatedImages，使用它们
                  if (currentTask?.images?.length > 0 || currentTask?.generatedImages?.length > 0) {
                    const currentTaskImages = currentTask.images || currentTask.generatedImages || [];
                    imageUrls = currentTaskImages.map(img => img.url);
                  } 
                  // 2. 如果没有图片数组但有当前图片，创建单个URL的数组
                  else if (selectedImage?.url) {
                    imageUrls = [selectedImage.url];
                  }
                  
                  // 如果没有图片URL，不执行预览
                  if (imageUrls.length === 0) return;
                  
                  setPreviewImages(imageUrls);
                  setCurrentPreviewIndex(selectedImage.imageIndex || 0);
                  setPreviewImage(selectedImage.url);
                  setShowPreviewModal(true);
                }}
                imagePosition={imagePosition}
                compareWidthRatio={compareWidthRatio}
                defaultCompareWidthRatio={50}
              />

              {/* 图片导航控制 */}
              {generationTasks && selectedImage && (taskImages.length > 1 || (() => {
                // 1. 先从generationTasks中尝试获取任务
                const taskForNavCheck = generationTasks?.find(task => task.taskId === selectedImage.taskId);
                  
                // 2. 如果找到任务并且有images或generatedImages，使用它们的长度
                if (taskForNavCheck?.images?.length > 0 || taskForNavCheck?.generatedImages?.length > 0) {
                  const images = taskForNavCheck.images || taskForNavCheck.generatedImages || [];
                  return images.length > 1;
                }
                
                // 3. 如果只有单张图片的情况，不显示导航控制
                return false;
              })()) && (
                <div className="navigator-container">
                  <ImageNavigator
                    currentIndex={selectedImage.imageIndex || 0}
                    totalImages={taskImages.length > 0 ? taskImages.length : (() => {
                      // 1. 先从generationTasks中尝试获取任务
                      const taskForCount = generationTasks?.find(task => task.taskId === selectedImage.taskId);
                      
                      // 2. 如果找到任务并且有images或generatedImages，使用它们的长度
                      if (taskForCount?.images?.length > 0 || taskForCount?.generatedImages?.length > 0) {
                        return (taskForCount.images || taskForCount.generatedImages || []).length;
                      }
                      
                      // 3. 如果只有单张图片的情况，返回1
                      if (selectedImage?.url) {
                        return 1;
                      }
                      
                      // 4. 默认返回0
                      return 0;
                    })()}
                    onNavigate={(newIndex) => {
                      // 重置图片位置和缩放状态
                      setImagePosition({ x: 0, y: 0 });
                      lastPosition.current = { x: 0, y: 0 };
                      // 强制更新缩放值为初始值
                      const validInitialScale = initialScale > 0 ? initialScale : 100;
                      setImageScale(validInitialScale);
                      setIsDragging(false);
                      
                      // 获取图片数组
                      const images = taskImages.length > 0 ? taskImages : (() => {
                        // 1. 先从generationTasks中尝试获取任务
                        const taskForNav = generationTasks?.find(task => task.taskId === selectedImage.taskId);
                        
                        // 2. 获取图片数组
                        if (taskForNav?.images?.length > 0 || taskForNav?.generatedImages?.length > 0) {
                          return taskForNav.images || taskForNav.generatedImages || [];
                        } else if (selectedImage?.url) {
                          // 如果没有图片数组，但有单张图片，创建单图片数组
                          return [{
                            url: selectedImage.url,
                            taskId: selectedImage.taskId,
                            imageIndex: 0
                          }];
                        } else {
                          return [];
                        }
                      })();
                      
                      if (!images[newIndex]) return;
                      
                      // 更新选中的图片
                      setSelectedImage(prev => {
                        // 提取当前图片的基本数据
                        const newImageData = {
                          ...images[newIndex],
                          taskId: prev.taskId,
                          createdAt: prev.createdAt,
                          imageIndex: newIndex
                        };
                        
                        // 保留components和sourceImagePanel等关键属性
                        if (prev.components) {
                          newImageData.components = prev.components;
                        }
                        
                        // 保留特定页面类型的属性
                        if (pageType === 'matting') {
                          if (prev.sourceImagePanel) {
                            newImageData.sourceImagePanel = prev.sourceImagePanel;
                          }
                          if (prev.hasSourceImagePanel !== undefined) {
                            newImageData.hasSourceImagePanel = prev.hasSourceImagePanel;
                          }
                        }
                        
                        // 保留服务器文件名相关属性
                        if (prev.serverFileName) {
                          newImageData.serverFileName = prev.serverFileName;
                        }
                        if (prev.primaryImageFileName) {
                          newImageData.primaryImageFileName = prev.primaryImageFileName;
                        }
                        
                        return newImageData;
                      });
                    }}
                  />
                </div>
              )}

              {/* 按钮容器 */}
              <div className="button-container">
                {/* 只在非抠图页面显示编辑按钮 */}
                {pageType !== 'matting' && (
                  <button 
                    className="edit-btn"
                    onClick={handleEditTask}
                    title="重新编辑"
                  >
                    重新编辑
                  </button>
                )}

                <button 
                  className="action-btn download-btn"
                  onClick={() => handleDownloadImage(selectedImage.url, selectedImage.taskId, selectedImage.imageIndex)}
                >
                  <span className="icon">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z" fill="currentColor"/>
                    </svg>
                  </span>
                  <span>下载</span>
                </button>
                
                {/* 虚拟模特页面添加"添加为虚拟模特"按钮 */}
                {pageType === 'virtual' && (
                  <button 
                    className="add-exclusive-model-btn"
                    onClick={() => {
                      // 打开模特登记弹窗
                      setShowModelRegistration(true);
                    }}
                  >
                    <div className="btn-text-container">
                      <span>添加为</span>
                      <span>虚拟模特</span>
                    </div>
                  </button>
                )}
              </div>
            </div>
          </div>
        )}
      </Modal>
      
      {/* 图片预览模态框 */}
      <ImagePreviewModal
        visible={showPreviewModal}
        imageUrl={previewImage}
        onClose={() => setShowPreviewModal(false)}
        alt={"预览图片"}
        showHint={false}
      />
      {/* 多图片预览的翻页按钮 */}
      {showPreviewModal && previewImages && previewImages.length > 1 && (
        <div className="multi-image-preview-controls">
          <button 
            className="prev-image-button"
            onClick={handlePrevImage}
            style={{
              position: 'fixed',
              left: '20px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 10001,
              background: 'rgba(0, 0, 0, 0.5)',
              border: 'none',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              color: 'white',
              fontSize: '20px'
            }}
          >
            <MdChevronLeft />
          </button>
          <button 
            className="next-image-button"
            onClick={handleNextImage}
            style={{
              position: 'fixed',
              right: '20px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 10001,
              background: 'rgba(0, 0, 0, 0.5)',
              border: 'none',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              cursor: 'pointer',
              color: 'white',
              fontSize: '20px'
            }}
          >
            <MdChevronRight />
          </button>
          <div 
            className="image-index-indicator"
            style={{
              position: 'fixed',
              bottom: '20px',
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 10001,
              background: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              padding: '8px 16px',
              borderRadius: '20px',
              fontSize: '14px'
            }}
          >
            {currentPreviewIndex + 1} / {previewImages.length}
          </div>
        </div>
      )}

      {/* 模特登记弹窗 */}
      {showModelRegistration && selectedImage && (
        <ModelRegistrationModal
          isOpen={showModelRegistration}
          onClose={() => setShowModelRegistration(false)}
          modelData={{
            url: selectedImage?.url,
            prompt: getTaskComponent(selectedImage, 'modelPanel.prompt') || '',
            negative_prompt: getTaskComponent(selectedImage, 'modelPanel.negative_prompt') || '',
            tags: getTaskComponent(selectedImage, 'modelPanel.tags') || []
          }}
        />
      )}
    </React.Fragment>
  );
};

export default React.memo(ImageDetailsModal); 