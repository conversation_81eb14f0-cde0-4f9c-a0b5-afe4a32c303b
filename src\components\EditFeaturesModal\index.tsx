import React, { useState, useEffect, useRef } from 'react';
import { FiList } from 'react-icons/fi';
import './styles.css';

interface EditFeaturesModalProps {
  isOpen: boolean;
  isSidebarCollapsed: boolean;
  allFeatures: { [key: string]: string[] };
  commonFeatures: string[];
  onClose: () => void;
  onFeatureChange: (featureName: string) => void;
  renderIcon: (IconComponent: any) => React.ReactNode;
}

const EditFeaturesModal: React.FC<EditFeaturesModalProps> = ({
  isOpen,
  isSidebarCollapsed,
  allFeatures,
  commonFeatures,
  onClose,
  onFeatureChange,
  renderIcon
}) => {
  // 拖拽相关状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [modalPosition, setModalPosition] = useState({ x: 0, y: 0 });
  const modalRef = useRef<HTMLDivElement>(null);

  // 处理弹窗鼠标按下事件
  const handleModalMouseDown = (e: React.MouseEvent) => {
    // 只在标题区域允许拖动
    if (e.target === e.currentTarget.querySelector('h3') || 
        (e.target as HTMLElement).closest('h3')) {
      setIsDragging(true);
      
      const modalElement = modalRef.current;
      if (modalElement) {
        const rect = modalElement.getBoundingClientRect();
        
        setDragOffset({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
        
        // 防止文本选择
        document.body.classList.add('no-select');
        modalElement.classList.add('dragging');
        
        e.preventDefault();
      }
    }
  };

  // 处理鼠标移动事件
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging && modalRef.current) {
        const newX = e.clientX - dragOffset.x;
        const newY = e.clientY - dragOffset.y;
        
        // 限制弹窗不超出视窗边界
        const modalRect = modalRef.current.getBoundingClientRect();
        const maxX = window.innerWidth - modalRect.width;
        const maxY = window.innerHeight - modalRect.height;
        
        const clampedX = Math.max(0, Math.min(newX, maxX));
        const clampedY = Math.max(0, Math.min(newY, maxY));
        
        setModalPosition({ x: clampedX, y: clampedY });
      }
    };

    const handleMouseUp = () => {
      if (isDragging) {
        setIsDragging(false);
        document.body.classList.remove('no-select');
        if (modalRef.current) {
          modalRef.current.classList.remove('dragging');
        }
      }
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset]);

  // 当弹窗打开时重置位置
  useEffect(() => {
    if (isOpen) {
      setModalPosition({ x: 0, y: 0 });
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const isCommon = (featureName: string): boolean => {
    return commonFeatures.includes(featureName);
  };

  return (
    <div 
      ref={modalRef}
      className={`edit-modal ${isSidebarCollapsed ? 'sidebar-collapsed' : ''}`} 
      style={{
        ...(modalPosition.x !== 0 || modalPosition.y !== 0 ? {
          left: modalPosition.x,
          top: modalPosition.y
        } : {}),
        cursor: isDragging ? 'grabbing' : 'default'
      }}
      onClick={(e) => e.stopPropagation()}
      onMouseDown={handleModalMouseDown}
    >
      <h3 style={{ cursor: 'grab' }}>选择常用功能</h3>
      <div className="feature-list">
        {Object.keys(allFeatures).map((parent) => (
          <div key={parent}>
            {/* 暂时隐藏AI视频分组标题 */}
            {parent !== 'AI视频' && <h4>{parent}</h4>}
            {Array.isArray(allFeatures[parent]) && allFeatures[parent].map((child) => (
              // 临时隐藏指定页面按钮（如需恢复显示"图文成片"、"多图成片"，请删除下方 includes 判断）
              [
                '图文成片', // imgtextvideo
                '多图成片'  // mulimgvideo
              ].includes(child) 
                ? null // === 恢复显示方法：删除本 includes 判断及其内容 ===
                : (
                  <label key={child} className="checkbox-label">
                    <input
                      type="checkbox"
                      checked={isCommon(child)}
                      onChange={() => onFeatureChange(child)}
                    />
                    <span className="toggle-track"></span>
                    <span>{child}</span>
                  </label>
                )
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default EditFeaturesModal; 