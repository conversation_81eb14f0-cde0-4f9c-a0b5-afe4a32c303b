# 拖拽删除并重新上传功能实现指南

## 概述

本指南描述了如何为面板组件添加拖拽删除并重新上传功能。当用户拖动图片到面板上时，会删除当前面板并立即上传拖拽的新图片。

## 功能特点

- **拖拽删除**：拖动图片到面板上时删除当前内容
- **立即重新上传**：删除后立即上传拖拽的新图片
- **视觉反馈**：拖拽时显示半透明遮罩和"重新上传"提示
- **用户体验**：操作简单直观，符合用户直觉

## JavaScript 实现

### 1. 添加状态管理

```javascript
const [isDragOver, setIsDragOver] = useState(false);
```

### 2. 实现拖拽事件处理函数

```javascript
// 处理拖拽进入
const handleDragEnter = (e) => {
  e.preventDefault();
  e.stopPropagation();
  setIsDragOver(true);
};

// 处理拖拽离开
const handleDragLeave = (e) => {
  e.preventDefault();
  e.stopPropagation();
  // 只有当离开整个组件时才重置状态
  if (!e.currentTarget.contains(e.relatedTarget)) {
    setIsDragOver(false);
  }
};

// 处理拖拽悬停
const handleDragOver = (e) => {
  e.preventDefault();
  e.stopPropagation();
  // 确保拖拽悬停时保持状态
  if (!isDragOver) {
    setIsDragOver(true);
  }
};

// 处理拖拽放下
const handleDrop = async (e) => {
  e.preventDefault();
  e.stopPropagation();
  setIsDragOver(false);
  
  // 获取拖拽的文件
  const files = Array.from(e.dataTransfer.files);
  
  if (files.length > 0) {
    // 先删除当前面板
    if (panel && panel.componentId) {
      onDelete?.(panel.componentId);
      message.success('已删除面板');
    }
    
    // 延迟一点时间后处理拖拽的文件
    setTimeout(() => {
      // 处理第一个文件
      const file = files[0];
      if (file && file.type.startsWith('image/')) {
        // 直接处理文件上传，创建新的面板
        const imageId = `${panelType}-${Date.now()}`;
        const imageUrl = URL.createObjectURL(file);
        
        const newPanel = {
          componentId: imageId,
          title: panelTitle,
          status: 'completed',
          serverFileName: file.name,
          url: imageUrl,
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type,
            serverFileName: file.name
          },
          type: panelType,
          file: file,
          processInfo: {
            serverFileName: file.name
          }
        };
        
        // 通知父组件创建新面板
        onPanelsChange([newPanel]);
        message.success('图片上传成功');
      } else {
        message.error('请拖拽图片文件');
      }
    }, 50);
  } else {
    // 如果没有文件，只执行删除操作
    if (panel && panel.componentId) {
      onDelete?.(panel.componentId);
      message.success('已删除面板');
    }
  }
};
```

### 3. 在组件根元素添加拖拽事件监听器

```javascript
return (
  <div 
    className={`panel-component ${isDragOver ? 'drag-over' : ''}`}
    onDragEnter={handleDragEnter}
    onDragLeave={handleDragLeave}
    onDragOver={handleDragOver}
    onDrop={handleDrop}
  >
    {/* 组件内容 */}
  </div>
);
```

## CSS 样式实现

### 1. 基础样式设置

```css
/* 拖拽删除功能样式 */
.panel-component.drag-over {
  position: relative;
}
```

### 2. 半透明遮罩效果

```css
.panel-component.drag-over::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
  z-index: 5;
  pointer-events: none;
}
```

### 3. 文字提示样式

```css
.panel-component.drag-over::after {
  content: "重新上传";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--brand-primary);
  font-size: 14px;
  font-weight: normal;
  z-index: 10;
  pointer-events: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
```

## 关键实现要点

### 1. 拖拽状态管理
- 使用 `isDragOver` 状态跟踪拖拽状态
- 在 `handleDragLeave` 中检查是否真正离开组件
- 在 `handleDragOver` 中确保状态保持

### 2. 文件处理
- 从 `e.dataTransfer.files` 获取拖拽的文件
- 验证文件类型是否为图片
- 创建本地 URL 用于预览

### 3. 面板数据结构
- 生成唯一的 `componentId`
- 保存原始文件对象供后续上传使用
- 设置正确的业务类型和来源标记

### 4. 视觉反馈
- 使用 `::before` 伪元素创建半透明遮罩
- 使用 `::after` 伪元素显示文字提示
- 使用 `backdrop-filter` 实现模糊效果

## 适用组件类型

本指南适用于以下类型的面板组件：
- 图片预览面板
- 文件上传面板
- 缩略图面板
- 任何需要支持拖拽替换的面板组件

## 注意事项

1. **事件冒泡**：确保调用 `e.preventDefault()` 和 `e.stopPropagation()`
2. **状态重置**：在 `handleDrop` 中重置拖拽状态
3. **文件验证**：检查文件类型和大小
4. **内存管理**：及时释放 `URL.createObjectURL` 创建的对象
5. **用户体验**：提供清晰的视觉反馈和操作提示

## 示例组件

参考 `ClothingPanel` 组件的完整实现：
- `src/components/ClothingPanel/index.jsx`
- `src/components/ClothingPanel/index.css` 