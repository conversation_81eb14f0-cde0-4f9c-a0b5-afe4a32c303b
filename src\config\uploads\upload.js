// 图片上传相关配置
export const UPLOAD_CONFIG = {
  // 支持的文件类型
  supportedTypes: ['jpg', 'jpeg', 'png', 'avif', 'webp'],
  
  // 文件大小限制（单位：MB）
  maxSize: 10,
  
  // 分辨率限制
  resolution: {
    min: 512,
    max: 6656
  },
  
  // 获取支持的文件类型字符串
  getSupportedTypesString: () => {
    return UPLOAD_CONFIG.supportedTypes.join('、');
  },
  
  // 获取文件类型的accept属性值
  getAcceptTypes: () => {
    return UPLOAD_CONFIG.supportedTypes.map(type => `.${type}`).join(',');
  },
  
  // 获取分辨率范围字符串
  getResolutionString: () => {
    return `${UPLOAD_CONFIG.resolution.min}-${UPLOAD_CONFIG.resolution.max}`;
  },
  
  // 验证文件类型
  isValidFileType: (filename) => {
    const ext = filename.split('.').pop().toLowerCase();
    return UPLOAD_CONFIG.supportedTypes.includes(ext);
  },
  
  // 验证文件大小（参数单位：bytes）
  isValidFileSize: (size) => {
    return size <= UPLOAD_CONFIG.maxSize * 1024 * 1024;
  },
  
  // 验证图片分辨率
  isValidResolution: (width, height) => {
    const { min, max } = UPLOAD_CONFIG.resolution;
    return width >= min && width <= max && height >= min && height <= max;
  },
  
  // 获取图片分辨率（异步方法）
  getImageResolution: (file) => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
      };
      img.onerror = () => {
        reject(new Error('无法读取图片分辨率'));
      };
      img.src = URL.createObjectURL(file);
    });
  }
};

// 默认导出上传配置
export default {
  UPLOAD_CONFIG
};

// 统一的文件校验函数
export const validateFile = async (file) => {
  // 1. 校验文件类型
  if (!UPLOAD_CONFIG.isValidFileType(file.name)) {
    return {
      isValid: false,
      error: '不支持的文件类型',
      type: 'fileType'
    };
  }
  
  // 2. 校验文件大小
  if (!UPLOAD_CONFIG.isValidFileSize(file.size)) {
    return {
      isValid: false,
      error: `文件需${UPLOAD_CONFIG.maxSize}MB以内`,
      type: 'fileSize'
    };
  }
  
  // 3. 校验图片分辨率
  try {
    const { width, height } = await UPLOAD_CONFIG.getImageResolution(file);
    if (!UPLOAD_CONFIG.isValidResolution(width, height)) {
      return {
        isValid: false,
        error: `图片分辨率需在${UPLOAD_CONFIG.getResolutionString()}px范围内`,
        type: 'resolution'
      };
    }
  } catch (error) {
    return {
      isValid: false,
      error: '无法读取图片分辨率，请检查图片文件',
      type: 'resolution'
    };
  }
  
  // 所有校验通过
  return {
    isValid: true,
    error: null,
    type: null
  };
}; 