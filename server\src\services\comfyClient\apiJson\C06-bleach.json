{"6": {"inputs": {"text": "Change the clothing (including but not limited to swimsuits, bikinis, bras, panties) to white, keep the material and texture unchanged.", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "8": {"inputs": {"samples": ["31", 0], "vae": ["39", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "31": {"inputs": {"seed": 817764563109543, "steps": 30, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["37", 0], "positive": ["35", 0], "negative": ["135", 0], "latent_image": ["124", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "随机种子"}}, "35": {"inputs": {"guidance": 2.5, "conditioning": ["177", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "37": {"inputs": {"unet_name": "flux1-kontext-dev.safetensors", "weight_dtype": "fp8_e4m3fn_fast"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "38": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn_scaled.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "39": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "124": {"inputs": {"pixels": ["196", 0], "vae": ["39", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "135": {"inputs": {"conditioning": ["6", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "177": {"inputs": {"conditioning": ["6", 0], "latent": ["124", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "196": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "longest", "scale_to_length": 1024, "background_color": "#000000", "image": ["223", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "202": {"inputs": {"upscale_model": ["204", 0], "image": ["8", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "204": {"inputs": {"model_name": "RealESRGAN_x2.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "215": {"inputs": {"width": ["216", 0], "height": ["216", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": "crop", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 1, "device": "cpu", "image": ["202", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "216": {"inputs": {"image": ["223", 0]}, "class_type": "GetImageSize", "_meta": {"title": "Get Image Size"}}, "218": {"inputs": {"filename_prefix": "Bleach", "images": ["215", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "223": {"inputs": {"url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "上传图片"}}}