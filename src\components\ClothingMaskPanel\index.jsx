import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { message } from 'antd';
import ThumbnailWithPreview from '../common/ThumbnailWithPreview';
import './index.css';

/**
 * 蒙版面板组件 - 用于展示服装原图和状态
 * 
 * 由 ModelMaskPanel 复制并重命名为 ClothingMaskPanel
 */
const ClothingMaskPanel = ({
  panel,
  onExpandClick,
  onDelete,
  onReupload,
  onStatusChange,
  isActive,
  onPanelsChange,
  onDrawMask,
  isEnhanced = false,
  pageType = 'try-on' // 默认为模特换装页面
}) => {
  const [isDragOver, setIsDragOver] = useState(false);

  // 处理拖拽进入
  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  // 处理拖拽离开
  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // 只有当离开整个组件时才重置状态
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
    }
  };

  // 处理拖拽悬停
  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // 确保拖拽悬停时保持状态
    if (!isDragOver) {
      setIsDragOver(true);
    }
  };

  // 处理拖拽放下
  const handleDrop = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
    
    // 首先检查是否有自定义的拖拽数据（从TaskPanel拖拽过来的图片）
    try {
      const customData = e.dataTransfer.getData('application/json');
      if (customData) {
        const dragData = JSON.parse(customData);
        if (dragData.type === 'image-upload') {
          // 处理从TaskPanel拖拽过来的图片
          await handleImageFromTaskPanel(dragData);
          return;
        }
      }
    } catch (error) {
      console.log('解析拖拽数据失败，尝试处理文件:', error);
    }
    
    // 获取拖拽的文件
    const files = Array.from(e.dataTransfer.files);
    
    if (files.length > 0) {
      // 先删除当前面板
      if (panel && panel.componentId) {
        onDelete?.(panel.componentId);
        const successMessage = pageType === 'optimize' ? '已删除款式图面板' : '已删除服装面板';
        message.success(successMessage);
      }
      
      // 延迟一点时间后处理拖拽的文件
      setTimeout(() => {
        // 处理第一个文件
        const file = files[0];
        if (file && file.type.startsWith('image/')) {
          // 直接处理文件上传，创建新的面板
          const imageId = `clothing-mask-${Date.now()}`;
          const imageUrl = URL.createObjectURL(file);
          
          const newPanel = {
            componentId: imageId,
            title: getPanelTitle(),
            status: 'completed',
            serverFileName: file.name,
            url: imageUrl,
            fileInfo: {
              name: file.name,
              size: file.size,
              type: file.type,
              serverFileName: file.name
            },
            type: 'clothing-mask',
            file: file,
            processInfo: {
              serverFileName: file.name
            }
          };
          
          // 通知父组件创建新面板
          onPanelsChange([newPanel]);
          message.success('图片上传成功');
          
          // 延迟一点时间后自动弹出蒙版绘制弹窗
          setTimeout(() => {
            if (onDrawMask) {
              onDrawMask(newPanel);
            }
          }, 100);
        } else {
          message.error('请拖拽图片文件');
        }
      }, 50);
    } else {
      // 如果没有文件，只执行删除操作
      if (panel && panel.componentId) {
        onDelete?.(panel.componentId);
        const successMessage = pageType === 'optimize' ? '已删除款式图面板' : '已删除服装面板';
        message.success(successMessage);
      }
    }
  };

  // 处理从TaskPanel拖拽过来的图片
  const handleImageFromTaskPanel = async (dragData) => {
    try {
      const { imageUrl, fileName, taskId, imageIndex } = dragData;
      
      // 先删除当前面板
      if (panel && panel.componentId) {
        onDelete?.(panel.componentId);
        const successMessage = pageType === 'optimize' ? '已删除款式图面板' : '已删除服装面板';
        message.success(successMessage);
      }
      
      // 延迟一点时间后处理拖拽的图片
      setTimeout(async () => {
        try {
          // 将HTTP转换为HTTPS，解决混合内容问题
          const httpsUrl = imageUrl.replace(/^http:/, 'https:');
          
          // 从URL获取图片数据
          const response = await fetch(httpsUrl);
          if (!response.ok) {
            throw new Error('无法获取图片数据');
          }
          
          const blob = await response.blob();
          
          // 创建一个File对象
          const file = new File([blob], fileName, {
            type: blob.type || 'image/jpeg',
            lastModified: Date.now()
          });
          
          console.log(`处理从任务面板拖拽的图片: ${fileName}, 任务ID: ${taskId}, 图片索引: ${imageIndex}`);
          
          // 创建新的面板
          const imageId = `clothing-mask-${Date.now()}`;
          const newImageUrl = URL.createObjectURL(file);
          
          const newPanel = {
            componentId: imageId,
            title: getPanelTitle(),
            status: 'completed',
            serverFileName: fileName,
            url: newImageUrl,
            fileInfo: {
              name: fileName,
              size: file.size,
              type: file.type,
              serverFileName: fileName
            },
            type: 'clothing-mask',
            file: file,
            processInfo: {
              serverFileName: fileName
            }
          };
          
          // 通知父组件创建新面板
          onPanelsChange([newPanel]);
          message.success('图片重新上传成功');
          
          // 延迟一点时间后自动弹出蒙版绘制弹窗
          setTimeout(() => {
            if (onDrawMask) {
              onDrawMask(newPanel);
            }
          }, 100);
          
        } catch (error) {
          console.error('处理拖拽图片时出错:', error);
          message.error('处理图片失败，请重试');
        }
      }, 50);
      
    } catch (error) {
      console.error('处理从TaskPanel拖拽的图片时出错:', error);
      message.error('处理图片失败，请重试');
    }
  };

  const handleExpandClick = (e) => {
    const buttonRect = e.currentTarget.getBoundingClientRect();
    onExpandClick(panel, {
      top: buttonRect.top,
      left: buttonRect.left + buttonRect.width
    });
  };

  const handleDelete = () => {
    // 使用统一标准方案，仅使用componentId
    const panelId = panel.componentId;
    if (panelId) {
      onDelete?.(panelId);
      // 根据页面类型显示不同的成功消息
      const successMessage = pageType === 'optimize' ? '已删除款式图面板' : '已删除模特原图面板';
      message.success(successMessage);
    }
  };

  const handleReupload = () => {
    if (panel) {
      onReupload?.(panel);
    }
  };

  const getImageInfo = () => {
    if (!panel || !panel.processedFile) return null;

    if (panel.fileInfo) {
      return {
        size: `${(panel.fileInfo.size / (1024 * 1024)).toFixed(2)} MB`,
        resolution: `${panel.fileInfo.width} x ${panel.fileInfo.height} px`,
        format: panel.fileInfo.format
      };
    }

    return {
      size: '--',
      resolution: '--',
      format: '--'
    };
  };

  // 状态文本
  const getStatusText = () => {
    return '上传完成';
  };

  // 获取面板标题 - 根据页面类型
  const getPanelTitle = () => {
    // 换脸页面显示脸部原图
    if (pageType === 'change-face') {
      return '脸部原图';
    }
    // 细节还原页面显示服装
    if (pageType === 'detail-migration') {
      return '服装';
    }
    if (panel && panel.title) {
      return panel.title; // 如果面板本身已经有标题，优先使用
    }
    return '模特原图';
  };

  return (
    <div 
      className={`panel-component ${isDragOver ? 'drag-over' : ''}`}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <div className="component-header">
        <div className="component-info">
          <ThumbnailWithPreview
            imageUrl={panel.processedFile || panel.url}
            alt={`${getPanelTitle()} 上传结果`}
            status={panel.status}
            error={panel.error}
            onStatusChange={onStatusChange}
            featureName={pageType === 'detail-migration' ? '服装预览' : '模特原图预览'}
          />
          <div className="component-text">
            <h3>{getPanelTitle()}</h3>
            <div className="component-content">
              <p>
                {panel.status === 'completed' && (
                  <>
                    {getStatusText()}
                    {/* 蒙版相关提示 - 仅在换模特页面隐藏
                        如需在换模特页面也显示蒙版功能，请删除 pageType !== 'change-model' 条件 */}
                    {pageType !== 'change-model' && (
                      panel.hasMask ? (
                        <span className="mask-status">已手动绘制蒙版</span>
                      ) : pageType === 'optimize' || pageType === 'detail-migration' || pageType === 'try-on-other' || pageType === 'change-face' ? (
                        <span className="mask-status warning">需要手动绘制蒙版</span>
                      ) : (
                        <span className="mask-status">将自动绘制蒙版</span>
                      )
                    )}
                    {/* 蒙版提示功能结束 */}
                  </>
                )}
                {panel.status === 'processing' && '处理中...'}
                {panel.status === 'error' && panel.error}
              </p>
            </div>
          </div>
        </div>
        <button 
          className={`expand-btn ${isActive ? 'active' : ''}`}
          onClick={handleExpandClick}
        >
          <span></span>
        </button>
      </div>
    </div>
  );
};

ClothingMaskPanel.propTypes = {
  panel: PropTypes.shape({
    // 只支持componentId作为标准ID
    componentId: PropTypes.string.isRequired,
    title: PropTypes.string,
    status: PropTypes.oneOf(['processing', 'completed', 'error']).isRequired,
    error: PropTypes.string,
    processedFile: PropTypes.string,
    url: PropTypes.string,
    serverFileName: PropTypes.string,
    originalImage: PropTypes.string,
    // 蒙版相关属性 - 临时隐藏但保留以便将来扩展
    hasMask: PropTypes.bool,
    fileInfo: PropTypes.shape({
      size: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
      width: PropTypes.number,
      height: PropTypes.number,
      format: PropTypes.string,
      serverFileName: PropTypes.string
    }),
    showOriginal: PropTypes.bool,
    type: PropTypes.string,
    source: PropTypes.string,
  }).isRequired,
  onExpandClick: PropTypes.func.isRequired,
  onDelete: PropTypes.func,
  onReupload: PropTypes.func,
  onStatusChange: PropTypes.func,
  // 蒙版绘制功能 - 临时隐藏但保留以便将来扩展
  onDrawMask: PropTypes.func,
  isActive: PropTypes.bool,
  isEnhanced: PropTypes.bool,
  onPanelsChange: PropTypes.func,
  pageType: PropTypes.string,
};

export default ClothingMaskPanel; 