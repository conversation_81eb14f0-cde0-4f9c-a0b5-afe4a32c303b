{"4": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "longest", "scale_to_length": 1536, "background_color": "#000000", "image": ["33", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "32": {"inputs": {"path": "/root/ComfyUI/output/", "needInput": true, "filename_prefix": "Extract", "filename_delimiter": "_", "filename_number_padding": 0, "file_extension": ".txt", "encoding": "utf-8", "filename_suffix": "", "text": ["51", 0]}, "class_type": "Save Text File", "_meta": {"title": "导出txt"}}, "33": {"inputs": {"needInput": true, "url": "https://", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "LoadImagesFromURL", "_meta": {"title": "原始图片上传"}}, "34": {"inputs": {"caption_type": "Descriptive", "caption_length": "long", "max_new_tokens": 512, "top_p": 0.9, "top_k": 0, "temperature": 0.6, "user_prompt": ["38", 0], "speak_and_recognation": {"__value__": [false, true]}, "image": ["4", 0], "joycaption_beta1_model": ["35", 0]}, "class_type": "LayerUtility: JoyCaptionBeta1", "_meta": {"title": "LayerUtility: JoyCaption Beta One (Advance)"}}, "35": {"inputs": {"model": "fancyfeast/llama-joycaption-beta-one-hf-llava", "quantization_mode": "nf4", "device": "cuda"}, "class_type": "LayerUtility: LoadJoyCaptionBeta1Model", "_meta": {"title": "LayerUtility: Load JoyCaption Beta One Model (Advance)"}}, "36": {"inputs": {"String": "Describe only the clothing, not anything else", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "String", "_meta": {"title": "服装"}}, "38": {"inputs": {"needInput": true, "select": 3, "sel_mode": false, "input1": ["36", 0], "input2": ["39", 0], "input3": ["40", 0], "input4": ["42", 0]}, "class_type": "ImpactSwitch", "_meta": {"title": "1衣服，2模特，3整体，4自定义"}}, "39": {"inputs": {"String": "Only describe the character's skin color, race, hair details, posture details, and expression. Do not describe anything other than the character.", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "String", "_meta": {"title": "String"}}, "40": {"inputs": {"String": "", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "String", "_meta": {"title": "String"}}, "42": {"inputs": {"from_translate": "auto", "to_translate": "english", "add_proxies": false, "proxies": "", "auth_data": "", "service": "GoogleTranslator", "text": "", "Show proxy": "proxy_hide", "Show authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "Deep Translator Text Node"}}, "49": {"inputs": {"text_0": "Photograph of a tall, slim, tan-skinned woman with long, straight brown hair standing barefoot on a white boat. She wears a colorful, floral-patterned, deep-V one-piece swimsuit that highlights her small breasts and toned physique. The background features a clear blue sky, lush green mountains, and a small, white, Mediterranean-style house with a red-tiled roof nestled among the trees. The sunlight casts a natural glow on her glistening, slightly oiled skin. She stands confidently, with one leg slightly bent and her right hand resting on her thigh. The image is crisp, vibrant, and captures the serene, picturesque outdoor setting.", "text": ["34", 0]}, "class_type": "ShowText|pysssss", "_meta": {"title": "Show Text 🐍"}}, "51": {"inputs": {"delimiter": "-------", "clean_whitespace": "true", "text_a": ["53", 0], "text_b": ["49", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "53": {"inputs": {"from_translate": "english", "to_translate": "chinese (simplified)", "add_proxies": false, "proxies": "", "auth_data": "", "service": "GoogleTranslator", "text": ["34", 0], "Show proxy": "proxy_hide", "Show authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "Deep Translator Text Node"}}}