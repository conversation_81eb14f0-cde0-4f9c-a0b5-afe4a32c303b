/**
 * 算力值扣费标准配置文件
 * 统一管理所有功能页面的算力值扣费标准
 * 单位：C（算力值）
 */

const BILLING_CONFIG = {
  // 款式设计相关功能
  'trending': 35,          // 爆款开发
  'divergent': 38,         // 爆款延伸
  'optimize': 30,          // 款式优化
  'inspiration': 30,       // 灵感探索
  'fabric': 35,            // 换面料
  'drawing': 40,           // 生成线稿
 
  // 模特图相关功能
  'fashion': 150,          // 时尚大片
  'try-on': 110,           // 模特换装
  'change-model': 150,     // 换模特
  'recolor': 20,           // 服装复色
  'background': 25,        // 换背景
  'change-posture': 50,    // 换姿势
  'change-face': 25,       // 模特换脸
  'virtual': 100,          // 虚拟模特
  'detail-migration': 80,  // 细节还原
  'hand-fix': 80,          // 手部修复
  
  // 快捷工具相关功能
  'extract': 25,           // 图片取词
  'upscale': 10,           // 高清放大
  'matting': {             // 自动抠图支持多标签页定价
    'tab1': 10,            // 去背景
    'tab2': 20             // 抠衣服
  },
  'extend': 100,           // 智能扩图
  'inpaint': 25,           // 消除笔
  'bleach': 30,            // 服装去色
  
  // 视频工具相关功能
  'imgtextvideo': 300,    // 图文成片
  'mulimgvideo': 400,     // 多图成片
};



// 流程图映射
const FLOW_MAP = {
  'A01-trending': 'trending',
  'A06-divergent': 'divergent',
  'A03-optimize': 'optimize',
  'A04-inspiration': 'inspiration',
  'A05-drawing': 'drawing',
  'B01-fashion': 'fashion',
  'B02-tryonauto': 'try-on',
  'B02-tryonmanual': 'try-on',
  'B04-recolor': 'recolor',
  'B05-fabric': 'fabric',
  'B06-background': 'background',
  'B07-change-posture': 'change-posture',
  'B08-virtual': 'virtual',
  'C01-extract': 'extract',
  'C02-upscale': 'upscale',
  'C03-matting': 'matting',
  'C04-inpaint': 'inpaint',
  'D01-imgtextvideo': 'imgtextvideo',
  'D02-mulimgvideo': 'mulimgvideo',
}
// 获取指定功能的扣费标准
const getBillingCost = (featureName, subType) => {
  const config = BILLING_CONFIG[featureName];
  if (typeof config === 'number') {
    return config;
  } else if (typeof config === 'object' && config !== null) {
    if (subType && config[subType] !== undefined) {
      return config[subType];
    }
    return config._default || 0;
  }
  return 0;
};

module.exports = {
  BILLING_CONFIG,
  getBillingCost
}; 