/* 使用通用面板样式 */
@import '../../styles/panels.css';

/* 拖拽删除功能样式 */
.panel-component.drag-over {
  position: relative;
}

.panel-component.drag-over::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
  z-index: 5;
  pointer-events: none;
}

.panel-component.drag-over::after {
  content: "重新上传";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--brand-primary);
  font-size: 14px;
  font-weight: normal;
  z-index: 10;
  pointer-events: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 面料印花图片特有的样式可以在此处添加 */
.printing-status {
  margin-left: 8px;
  font-size: 13px;
  color: var(--brand-primary);
  display: inline-block;
}

.selected-model-preview {
  position: relative;
  background-image: linear-gradient(45deg, #d1d1d1 25%, transparent 25%),
                    linear-gradient(-45deg, #d1d1d1 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, #d1d1d1 75%),
                    linear-gradient(-45deg, transparent 75%, #d1d1d1 75%);
  background-size: 12px 12px;
  background-position: 0 0, 0 6px, 6px -6px, -6px 0px;
  background-color: var(--bg-primary);
}
[data-theme="dark"] .selected-model-preview {
  background-image: linear-gradient(45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(-45deg, var(--bg-secondary) 25%, transparent 25%),
                    linear-gradient(45deg, transparent 75%, var(--bg-secondary) 75%),
                    linear-gradient(-45deg, transparent 75%, var(--bg-secondary) 75%);
  background-size: 12px 12px;
  background-position: 0 0, 0 6px, 6px -6px, -6px 0px;
  background-color: var(--bg-primary);
} 