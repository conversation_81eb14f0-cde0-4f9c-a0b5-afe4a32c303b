import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { UPLOAD_CONFIG, validateFile } from '../../config/uploads/upload';
import './index.css';
import { getCSRFToken } from '../../api/security/csrf';
import { uploadImage } from '../../api/upload';
import { message } from 'antd';

const UploadBox_Model = ({
  id,
  onUpload,
  onShowGuide,
  onUploadResult,
  modelPanels = [],
  className,
  pageType = 'try-on', // 默认为模特换装页面
  uploadType = 'model', // 默认为模特图片上传
  title // 可选标题参数，用于自定义显示
}) => {
  const [isDragging, setIsDragging] = useState(false);

  // 根据页面类型和上传类型获取上传提示文本
  const getUploadText = () => {
    if (pageType === 'fabric' && uploadType === 'fabric') {
      return '面料图片';
    } else if (pageType === 'optimize') {
      return '款式图片';
    } else if (pageType === 'divergent') {
      return '款式图片';
    } else if (pageType === 'trending' && uploadType === 'printing') {
      return '面料印花参考图片';
    } else if (pageType === 'change-model') {
      return '服装图片';
    } else if (pageType === 'change-posture') {
      return '模特原图';
    } else if (pageType === 'change-face' && uploadType === 'clothing') {
      return '脸部原图';
    } else if (pageType === 'change-face' && uploadType === 'model') {
      return '模特图片';
    } else if (pageType === 'detail-migration' || pageType === 'hand-fix') {
      return uploadType === 'model' ? '模特图片' : '服装原图';
    } else if (pageType === 'inpaint') {
      return '原始图片';
    } else {
      return '模特原图';
    }
  };

  // 获取API上传时的参数
  const getApiPageType = () => {
    return pageType;
  };

  const getApiImageType = () => {
    if (pageType === 'fabric' && uploadType === 'fabric') {
      return 'fabric';
    } else if (pageType === 'optimize') {
      return 'design'; // 款式优化页面上传的是款式图
    } else if (pageType === 'divergent') {
      return 'design'; // 爆款延伸页面上传的是款式图
    } else if (pageType === 'trending' && uploadType === 'printing') {
      return 'printing'; // 爆款开发页面上传的是面料印花图
    } else {
      return uploadType || 'model';
    }
  };

  // 获取适当的面板标题
  const getPanelTitle = () => {
    if (title) {
      return title; // 如果提供了自定义标题，使用它
    } else if (pageType === 'fabric' && uploadType === 'fabric') {
      return '面料图片';
    } else if (pageType === 'optimize') {
      return '款式图片';
    } else if (pageType === 'divergent') {
      return '款式图片';
    } else if (pageType === 'trending' && uploadType === 'printing') {
      return '面料印花参考图片';
    } else if (pageType === 'change-model') {
      return '服装图片';
    } else if (pageType === 'change-posture') {
      return '模特原图';
    } else if (pageType === 'change-face' && uploadType === 'clothing') {
      return '脸部原图';
    } else if (pageType === 'change-face' && uploadType === 'model') {
      return '模特图片';
    } else if (pageType === 'detail-migration' || pageType === 'hand-fix') {
      return uploadType === 'model' ? '模特图片' : '服装原图';
    } else if (pageType === 'inpaint') {
      return '原始图片';
    } else {
      return '模特原图';
    }
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    // 首先检查是否有自定义的拖拽数据（从TaskPanel拖拽过来的图片）
    try {
      const customData = e.dataTransfer.getData('application/json');
      if (customData) {
        const dragData = JSON.parse(customData);
        if (dragData.type === 'image-upload') {
          // 处理从TaskPanel拖拽过来的图片
          await handleImageFromTaskPanel(dragData);
          return;
        }
      }
    } catch (error) {
      console.log('解析拖拽数据失败，尝试处理文件:', error);
    }

    const files = Array.from(e.dataTransfer.files);
    
    // 使用统一的文件校验函数
    for (const file of files) {
      const validation = await validateFile(file);
      if (!validation.isValid) {
        message.error(validation.error);
        return;
      }
    }

    // 如果选择了多个文件，只处理第一个并给出提示
    if (files.length > 1) {
      message.info('只能上传一张图片，已选择第一张图片');
    }

    // 处理第一个文件
    if (files.length > 0) {
      handleSingleFileUpload(files[0]);
    }
  };

  const handleSingleFileUpload = async (file) => {
      try {
        // 为图片创建唯一ID，使用与服务器一致的格式，不使用前缀
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 10);
        const imageId = `${timestamp}-${random}`;
        const imageUrl = URL.createObjectURL(file);
        
        // 创建完成状态的面板数据（直接使用原始图片，不进行抠图处理）
        const completedPanels = [{
          componentId: imageId, // 使用标准ID属性
          title: getPanelTitle(),
          status: 'completed',
          serverFileName: file.name, // 替换为serverFileName
          url: imageUrl, // 直接使用本地URL
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type,
            serverFileName: file.name // 在fileInfo中也设置serverFileName
          },
          file: file, // 保存原始文件对象，供后续上传使用
          type: getApiImageType(), // 设置业务类型
          source: 'upload', // 设置来源为用户上传
          // 确保在processInfo中也设置serverFileName
          processInfo: {
            serverFileName: file.name
          }
        }];
        
        // 通知父组件创建完成的面板
        onUploadResult?.({ type: 'panels', panels: completedPanels });
        
        // 按照优化流程，不立即上传图片到服务器，而是在点击生成按钮时再上传
        console.log(`图片本地预览成功，将在点击生成时上传到服务器`);
      } catch (error) {
        console.error('处理上传图片时出错:', error);
        onUploadResult?.({ 
          type: 'error', 
          error: error.message 
        });
      }
  };

  const handleImageFromTaskPanel = async (dragData) => {
    try {
      const { imageUrl, fileName, taskId, imageIndex } = dragData;
      
      // 将HTTP转换为HTTPS，解决混合内容问题
      const httpsUrl = imageUrl.replace(/^http:/, 'https:');
      
      // 从URL获取图片数据
      const response = await fetch(httpsUrl);
      if (!response.ok) {
        throw new Error('无法获取图片数据');
      }
      
      const blob = await response.blob();
      
      // 创建一个File对象
      const file = new File([blob], fileName, {
        type: blob.type || 'image/jpeg',
        lastModified: Date.now()
      });
      
      console.log(`处理从任务面板拖拽的图片: ${fileName}, 任务ID: ${taskId}, 图片索引: ${imageIndex}`);
      
      // 对从TaskPanel拖拽过来的图片进行校验
      const validation = await validateFile(file);
      if (!validation.isValid) {
        message.error(validation.error);
        return;
      }
      
      // 使用现有的文件上传逻辑
      await handleSingleFileUpload(file);
      
      // message.success('图片已添加到上传区域');
      
    } catch (error) {
      console.error('处理拖拽图片时出错:', error);
      message.error('处理图片失败，请重试');
    }
  };

  return (
    <div className={`upload-area ${className || ''}`}>
      <input 
        type="file" 
        id={id}
        className="file-input" 
        accept={UPLOAD_CONFIG.getAcceptTypes()}
        onChange={async (e) => {
          const files = Array.from(e.target.files || []);
          
          // 如果选择了多个文件，只处理第一个并给出提示
          if (files.length > 1) {
            message.info('只能上传一张图片，已选择第一张图片');
          }
          
          const file = files[0];
          if (file) {
            // 使用统一的文件校验函数
            const validation = await validateFile(file);
            if (!validation.isValid) {
              message.error(validation.error);
              return;
            }
            
            try {
              // 为图片创建唯一ID，使用与服务器一致的格式，不使用前缀
              const timestamp = Date.now();
              const random = Math.random().toString(36).substring(2, 10);
              const imageId = `${timestamp}-${random}`;
              const imageUrl = URL.createObjectURL(file);
              
              // 创建完成状态的面板数据（直接使用原始图片，不进行抠图处理）
              const completedPanels = [{
                componentId: imageId, // 使用标准ID属性
                title: getPanelTitle(),
                status: 'completed',
                serverFileName: file.name, // 替换为serverFileName
                url: imageUrl, // 直接使用本地URL
                fileInfo: {
                  name: file.name,
                  size: file.size,
                  type: file.type,
                  serverFileName: file.name // 在fileInfo中也设置serverFileName
                },
                file: file, // 保存原始文件对象，供后续上传使用
                type: getApiImageType(), // 设置业务类型
                source: 'upload', // 设置来源为用户上传
                // 确保在processInfo中也设置serverFileName
                processInfo: {
                  serverFileName: file.name
                }
              }];
              
              // 通知父组件创建完成的面板
              onUploadResult?.({ type: 'panels', panels: completedPanels });
              
              // 按照优化流程，不立即上传图片到服务器，而是在点击生成按钮时再上传
              console.log(`图片本地预览成功，将在点击生成时上传到服务器`);
            } catch (error) {
              console.error('处理上传图片时出错:', error);
              onUploadResult?.({ 
                type: 'error', 
                error: error.message 
              });
            }
          }
        }}
        onClick={(e) => {
          // 按照UploadBox的方式：某些页面直接触发文件选择，其他页面触发上传指南
          if (pageType === 'detail-migration' || pageType === 'hand-fix' || pageType === 'inpaint' || pageType === 'change-face') {
            // detail-migration、hand-fix、inpaint 和 change-face 页面直接触发文件选择，不阻止默认行为
            return;
          }
          // 其他页面（包括optimize和change-model）阻止默认行为并触发上传指南
          e.preventDefault();
          e.stopPropagation();
          onShowGuide?.();
        }}
      />
      <label 
        htmlFor={id} 
        className={`upload-zone ${modelPanels.length > 0 ? 'compact' : ''} ${isDragging ? 'dragging' : ''}`}
        onDragEnter={handleDragEnter}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={(e) => {
          // 按照UploadBox的方式：某些页面直接触发文件选择，其他页面触发上传指南
          if (pageType === 'detail-migration' || pageType === 'hand-fix' || pageType === 'inpaint' || pageType === 'change-face') {
            // detail-migration、hand-fix、inpaint 和 change-face 页面直接触发文件选择，不阻止默认行为
            return;
          }
          // 其他页面（包括optimize和change-model）阻止默认行为并触发上传指南
          e.preventDefault();
          e.stopPropagation();
          onShowGuide?.();
        }}
      >
        <div className="upload-content">
          {modelPanels.length === 0 && (
            <div className="upload-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 14V6H13V14H11Z" fill="currentColor"/>
                <path d="M7 10L12 5L17 10H7Z" fill="currentColor"/>
                <path d="M6 18V16H18V18H6Z" fill="currentColor"/>
              </svg>
            </div>
          )}
          <div className="upload-text">
            <span className="primary-text">
              {modelPanels.length > 0 ? "重新上传" : (
                <>拖拽 或 点击上传<br /><span className="highlight">{getUploadText()}</span></>
              )}
            </span>
            <span className="secondary-text">
              {modelPanels.length > 0 ? (
                <div className="requirements-container">
                  支持{UPLOAD_CONFIG.getSupportedTypesString()}
                  <span className="divider"></span>
                  文件{UPLOAD_CONFIG.maxSize}MB以内
                  <span className="divider"></span>
                  分辨率范围 {UPLOAD_CONFIG.getResolutionString()}px
                </div>
              ) : (
                <>
                  支持 {UPLOAD_CONFIG.getSupportedTypesString()}
                  <br />
                  文件{UPLOAD_CONFIG.maxSize}MB以内
                  <br />
                  分辨率范围 {UPLOAD_CONFIG.getResolutionString()}px
                </>
              )}
            </span>
          </div>
        </div>
      </label>
    </div>
  );
};

UploadBox_Model.propTypes = {
  id: PropTypes.string,
  onUpload: PropTypes.func,
  onShowGuide: PropTypes.func,
  onUploadResult: PropTypes.func,
  modelPanels: PropTypes.array,
  className: PropTypes.string,
  pageType: PropTypes.string,
  uploadType: PropTypes.string,
  title: PropTypes.string
};

export default UploadBox_Model; 

