/* 使用通用面板样式 */
@import '../../styles/panels.css';

/* 模特原图特有的样式可以在此处添加 */
.mask-status {
  margin-left: 8px !important;
  padding: 2px 8px !important;
  background-color: rgba(0, 200, 0, 0.1) !important;
  color: #42b983 !important;
  border-radius: 4px !important;
  font-size: 12px !important;
}

.mask-status.warning {
  background-color: rgba(255, 153, 0, 0.1) !important;
  color: #ff9900 !important;
  font-weight: 500 !important;
}

/* 拖拽删除功能样式 */
.panel-component.drag-over {
  position: relative;
}

.panel-component.drag-over::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
  z-index: 5;
  pointer-events: none;
}

.panel-component.drag-over::after {
  content: "重新上传";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--brand-primary);
  font-size: 14px;
  font-weight: normal;
  z-index: 10;
  pointer-events: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
} 