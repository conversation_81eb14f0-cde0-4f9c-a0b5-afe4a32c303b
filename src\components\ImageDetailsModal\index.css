/* 导入统一样式 */
@import '../../styles/modals.css';
@import '../../styles/scrollbars.css';

/* 描述词文本框样式 */
.description-text-container {
  width: 100%;
  margin-top: 8px;
}

.description-text-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.description-text-area {
  width: 100%;
  min-height: 120px;
  max-height: 180px;
  padding: 8px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-xs);
  resize: none;
  overflow-y: auto;
  cursor: default;
  user-select: text;
  outline: none;
}

.description-text-area:focus {
  outline: none;
  border-color: var(--border-color);
  box-shadow: none;
}

.description-text-area:hover {
  border-color: var(--border-color);
}

/* 带按钮的标题样式 */
.title-with-button {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  border-bottom: 1px solid var(--border-light);
  padding-bottom: 0;
}

.title-with-button .group-title {
  margin-bottom: var(--spacing-xxs);
  padding-top: var(--spacing-md);
  margin-right: var(--spacing-xs);
  border-bottom: none;
}

.title-with-button .copy-id-btn {
  align-self: center;
  margin-top: 0;
  margin-bottom: var(--spacing-xxs);
  position: relative;
  top: calc(var(--spacing-md) / 5);
}

/* 修复分割线问题 */
.info-group:has(.title-with-button) {
  border-bottom: none;
}

/* 图片详情弹窗样式更新 */
.image-details-modal .ant-modal-content {
    padding: 0;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    height: calc(100vh - 200px);
    min-height: 600px;
    will-change: transform;
    backface-visibility: hidden;
    transform: translateZ(0);
  }
  
  .image-details-modal .ant-modal-body {
    padding: 0;
    height: 100%;
  }
  
  /* 虚拟模特页面弹窗宽度调整 */
  .image-details-modal.virtual-page-modal .ant-modal-content {
    max-width: 1800px;
  }
  
  /* 透明背景 - 灰白格子，用于自动抠图页面显示透明区域 */
  .image-container.transparent-bg {
    background-image: linear-gradient(45deg, #d1d1d1 25%, transparent 25%),
                      linear-gradient(-45deg, #d1d1d1 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, #d1d1d1 75%),
                      linear-gradient(-45deg, transparent 75%, #d1d1d1 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    background-color: var(--bg-primary);
  }
  [data-theme="dark"] .image-container.transparent-bg {
    background-image: linear-gradient(45deg, var(--bg-secondary) 25%, transparent 25%),
                      linear-gradient(-45deg, var(--bg-secondary) 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, var(--bg-secondary) 75%),
                      linear-gradient(-45deg, transparent 75%, var(--bg-secondary) 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    background-color: var(--bg-primary);
  }
  
  /* 单图模式的样式 */
  .image-compare.single-image {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .image-compare.single-image .result-image.full-width {
    width: 100%;
    height: 100%;
    flex: 1;
  }
  
  .image-compare.single-image .image-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* 弹窗打开动画 - 简化并提高性能 */
  .ant-modal.image-details-modal {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
    transition: none !important;
  }
  
  /* 简化内容渐入动画 */
  .image-details-modal-content {
    opacity: 1;
    transform: none;
    transition: none;
    will-change: auto;
  }
  
  .image-details-modal-content.show-content {
    opacity: 1;
    transform: none;
  }
  
  /* 去除默认蒙层动画 */
  .ant-modal-mask {
    animation: none !important;
    transition: none !important;
    backdrop-filter: none !important;
  }
  
  .image-details-modal .ant-modal-close {
    top: var(--spacing-md);
    right: var(--spacing-md);
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    background: var(--bg-hover);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease-out;
  }
  
  .image-details-modal .ant-modal-close:hover {
    background: var(--bg-active);
  }
  
  .image-details-modal .ant-modal-close-x {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .image-details-modal .ant-modal-close-x svg {
    width: var(--font-size-lg);
    height: var(--font-size-lg);
    color: var(--text-secondary);
  }
  
  .image-details-modal .ant-modal-close:hover .ant-modal-close-x svg {
    color: var(--text-primary);
  }
  
  .image-details-modal-content {
    display: flex;
    width: 100%;
    height: 100%;
    position: relative;
    overflow: visible;
    background: var(--bg-primary);
    transform: translateZ(0);
  }
  
  /* 左侧信息栏样式 */
  .details-sidebar {
    width: 280px;
    flex-shrink: 0;
    border-right: 1px solid var(--border-color);
    padding: var(--spacing-md);
    overflow-y: auto;
    background: var(--bg-primary);
    will-change: scroll-position;
  }
  
  .details-sidebar .info-group {
    border-bottom: 1px solid var(--border-light);
    padding: var(--spacing-xs) 0;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
  }
  
  .details-sidebar .info-group:nth-child(2) {
    margin-top: var(--spacing-md);
  }
  
  .details-sidebar .info-group + .info-group {
    margin-top: var(--spacing-xs);
  }
  
  .details-sidebar .info-group:last-child {
    border-bottom: none;
  }
  
  .details-sidebar .group-title {
    font-size: var(--font-size-md);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xxs);
    padding-top: 0; /* 移除顶部内边距，避免与info-group的padding重叠 */
    padding-left: 0; /* 确保标题无左内边距 */
  }
  
  .details-sidebar .info-item:not(.with-thumbnail) {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xxs) 0;
  }
  
  .details-sidebar .info-item .label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
  }
  
  .details-sidebar .info-item .value {
    color: var(--text-primary);
    font-size: var(--font-size-xs);
    font-weight: 500;
  }
  
  .details-sidebar .info-item.ellipsis-value .value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
  }
  
  .details-sidebar .info-item .tag {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    margin-right: 8px;
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
  }
  
  /* 中间图片展示区样式 */
  .details-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-secondary);
    overflow: hidden;
    position: relative;
    min-width: 0;
    height: 100%;
    transform: translateZ(0);
    contain: layout;
  }
  
  .image-compare {
    display: flex;
    flex: 1;
    overflow: hidden;
    position: relative;
    height: 100%;
  }
  
  /* 确保原图和生成图容器可以滚动且高度正确 */
  .original-image,
  .result-image {
    height: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;
  }
  
  .image-title {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-mask);
    color: #fff;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    z-index: 1;
    backdrop-filter: blur(4px);
    border: 1px solid var(--border-light);
  }
  
  /* 确保图片容器能正确伸缩 */
  .image-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    min-height: 200px;
    padding: var(--spacing-sm);
  }
  
  .image-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    margin: 0 auto;
    display: block;
    transform-origin: center center;
    transition: transform 0.2s ease-out, opacity 0.2s ease-out;
  }
  
  /* 左侧原图区域保持默认鼠标样式 */
  .original-image .image-container,
  .original-image .image-container img {
    cursor: default;
    pointer-events: none;
  }
  
  /* 添加全局图片过渡效果 */
  .image-container img, 
  .original-image img,
  .references-container img,
  .reference-item img {
    transition: transform 0.2s ease-out, opacity 0.2s ease-out;
  }
  
  /* 右侧操作栏样式 */
  .details-actions {
    width: 100px;
    background: var(--bg-primary);
    border-left: 1px solid var(--border-color);
    padding: 12px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    height: 100%;
    position: relative;
    overflow-y: auto;
  }
  
  .details-actions .thumbnail-list {
    margin-top: 45px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-top: 12px;
    border-top: 1px solid var(--border-color);
    overflow-y: auto;
    max-height: 50vh;
    flex-shrink: 0;
  }
  
  /* 调整缩放控制组件在操作栏中的样式 */
  .details-actions .image-zoom-control {
    margin: 6px 0;
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
    width: 100%; /* 确保适应容器宽度 */
    padding: 6px 0;
    flex-shrink: 0;
  }
  
  /* 按钮容器 - 使用flex布局确保按钮在底部 */
  .details-actions .button-container {
    margin-top: auto;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-top: 12px;
    flex-shrink: 0;
  }
  
  .details-actions .action-btn.download-btn {
    width: 76px;
    margin: 0 auto;
    color: var(--brand-primary);
    border-color: var(--brand-primary);
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* 在抠图页面中调整下载按钮位置 */
  .image-details-modal[data-page-type="matting"] .details-actions .action-btn.download-btn {
    order: -1; /* 将下载按钮移到编辑按钮之前 */
  }
  
  /* 添加为虚拟模特按钮样式 */
  .details-actions .add-exclusive-model-btn {
    width: 76px;
    margin: 0 auto;
    height: 54px;
    padding: 0;
    background: var(--brand-gradient);
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-inverse);
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .details-actions .add-exclusive-model-btn:hover {
    filter: brightness(1.1);
  }
  
  .details-actions .add-exclusive-model-btn .btn-text-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    line-height: 1.2;
  }
  
  .details-actions .edit-btn {
    width: 76px;
    margin: 0 auto;
    height: 32px;
    padding: 8px 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .details-actions .edit-btn:disabled {
    background: var(--bg-secondary);
    border-color: var(--border-light);
    color: var(--text-disabled);
    cursor: not-allowed;
  }
  
  .details-actions .edit-btn:not(:disabled):hover {
    border-color: var(--brand-primary);
    color: var(--brand-primary);
  }
  
  .details-actions .action-btn {
    width: 100%;
    padding: 8px 0;
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    color: var(--text-secondary);
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 6px;
  }
  
  .details-actions .action-btn:hover {
    border-color: var(--brand-primary);
    color: var(--brand-primary);
  }
  
  .details-actions .action-btn .icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .details-actions .thumbnail-card {
    width: 100%;
    aspect-ratio: 3/4;
    border-radius: var(--radius-sm);
    overflow: hidden;
    cursor: pointer;
    position: relative;
    border: 2px solid transparent;
    transition: var(--transition-normal);
    background: var(--bg-secondary);
  }
  
  .details-actions .thumbnail-card img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0; /* 修改为直角 */
  }
  
  /* 移除遮罩效果 */
  .details-actions .thumbnail-card::after {
    display: none;
  }
  
  .details-actions .thumbnail-card:hover {
    border-color: var(--border-color);
  }
  
  .details-actions .thumbnail-card.active {
    border-color: var(--brand-primary);
  }
  
  .details-actions .thumbnail-card.active::after {
    display: none;
  }
  
  /* 导航器容器样式 */
  .navigator-container {
    margin: 0;
    padding: 6px 0;
    border-top: 1px solid var(--border-light);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: center;
    width: 100%;
    flex-shrink: 0;
  }
  
  /* 图片详情弹窗中的复制按钮和缩略图样式 */
  .details-sidebar .value-with-copy {
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
    padding: 0;
    margin: 0;
  }
  
  .details-sidebar .value-with-copy .value {
    flex: 1;
    font-size: var(--font-size-xs);
    color: var(--text-primary);
    font-weight: 500;
  }
  
  .details-sidebar .value-with-copy.ellipsis-value .value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
  }
  
  .details-sidebar .copy-id-btn {
    width: 24px;
    height: 24px;
    min-width: 24px;
    border-radius: var(--radius-sm);
    border: none;
    background: none;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: none;
    padding: 0;
    margin: 0;
  }
  
  .details-sidebar .copy-id-btn:hover {
    color: var(--brand-primary);
  }
  
  .details-sidebar .info-item.with-thumbnail {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) 0;
  }
  
  .details-sidebar .component-thumbnail-wrapper {
    position: relative;
    flex-shrink: 0;
  }
  
  .details-sidebar .component-thumbnail {
    width: 48px;
    height: 48px;
    border-radius: 0;
    overflow: hidden;
    border: 1px solid var(--border-light);
    transition: var(--transition-normal);
    background-image: linear-gradient(45deg, #d1d1d1 25%, transparent 25%),
                      linear-gradient(-45deg, #d1d1d1 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, #d1d1d1 75%),
                      linear-gradient(-45deg, transparent 75%, #d1d1d1 75%);
    background-size: 10px 10px;
    background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
    background-color: var(--bg-primary);
    flex-shrink: 0;
  }
  
  .details-sidebar .component-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background: none !important;
  }
  
  /* 移除彩色边框效果 */
  .details-sidebar .component-thumbnail-wrapper:hover .component-thumbnail {
    /* 移除彩色边框效果 */
    border-color: var(--border-light);
  }
  
  .details-sidebar .thumbnail-overlay {
    position: absolute;
    inset: 0;
    background: var(--bg-mask);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
    cursor: pointer;
    border-radius: 0;
  }
  
  /* 悬浮效果仍然保留，但只对非自定义图标的缩略图生效 */
  .details-sidebar .component-thumbnail-wrapper:hover .thumbnail-overlay {
    opacity: 1;
  }
  
  /* 自定义类型的缩略图不显示悬浮效果，并且鼠标指针为默认 */
  .details-sidebar .component-thumbnail-wrapper.custom-type {
    cursor: default;
  }
  
  .details-sidebar .component-thumbnail-wrapper.custom-type:hover .thumbnail-overlay {
    opacity: 0;
  }
  
  .details-sidebar .thumbnail-overlay svg {
    width: var(--font-size-xl);
    height: var(--font-size-xl);
    color: #fff;
  }
  
  /* 文本按钮基础样式 */
  .text-button {
    background: none;
    border: none;
    padding: var(--spacing-xxs) var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: none;
  }
  
  .text-button:hover {
    color: var(--brand-primary);
  }
  
  .text-button svg {
    width: var(--font-size-md);
    height: var(--font-size-md);
  }
  
  .details-sidebar .info-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
  }
  
  .details-sidebar .info-row {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: var(--spacing-xs);
    min-height: 22px;
  }
  
  .details-sidebar .info-row:last-child {
    margin-bottom: 0;
  }
  
  .details-sidebar .info-row .label,
  .details-sidebar .info-row .value,
  .details-sidebar .info-content .value-with-copy .value {
    line-height: 1.5;
    display: inline-block;
  }
  
  .details-sidebar .info-row .label {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    min-width: 50px;
    align-self: baseline;
  }
  
  .details-sidebar .info-row .value {
    color: var(--text-primary);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-align: right;
    align-self: baseline;
  }
  
  .details-sidebar .info-content .value-with-copy .value {
    flex: 1;
    word-break: normal;
    margin-right: var(--spacing-xs);
    font-size: var(--font-size-xs);
  }
  
  .details-sidebar .info-content .value-with-copy.ellipsis-value .value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
  }
  
  .custom-modal-wrap {
    position: relative;
  }
  
  /* 修改加载骨架屏，移除圆角 */
  .image-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border-radius: 0; /* 修改为直角 */
    z-index: 1;
    contain: strict;
    will-change: opacity;
    transition: opacity 0.2s ease-out;
  }
  
  /* 加载动画 */
  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(150, 150, 150, 0.2);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s linear infinite;
    will-change: transform;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* 创意强度项目特殊处理 */
  .details-sidebar .info-item.creative-strength-item {
    padding: var(--spacing-xs) 0;
    padding-left: 0;
    padding-right: 0;
    margin-left: 0;
    display: flex;
    flex-direction: column;
  }
  
  .color-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .color-swatch {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  /* 修改颜色样例，移除圆角 */
  .color-swatch-large {
    width: 48px;
    height: 48px;
    border-radius: 0; /* 修改为直角 */
    border: 1px solid var(--border-light);
    background-color: #cccccc;
  }

  /* 创意强度轨道样式 */
  .creative-strength-item {
    padding: var(--spacing-xs) 0;
    padding-left: 0;
    padding-right: 0;
  }
  
  .creative-slider.read-only {
    position: relative;
    padding: 8px 0;
  }
  
  .creative-slider.read-only .slider-track {
    position: relative;
    height: 2px;
    background: var(--border-light);
    border-radius: var(--radius-sm);
    margin: 8px 0;
    width: 100%;
  }
  
  .creative-slider.read-only .slider-fill {
    position: absolute;
    height: 100%;
    background: var(--brand-gradient);
    border-radius: var(--radius-sm);
    transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .creative-slider.read-only .creative-value {
    position: absolute;
    right: 0;
    top: -4px;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: 500;
  }
  
  /* 强度调节轨道样式 */
  .weight-slider {
    position: relative;
    padding: 8px 0;
    margin: 8px 0;
    width: 100%;
  }
  
  .weight-values {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    width: 100%;
  }
  
  .weight-min-value,
  .weight-max-value {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    font-weight: 500;
  }
  
  .weight-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 4px;
    width: 100%;
  }
  
  .weight-min-label,
  .weight-max-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
  }
  
  .weight-slider .slider-track {
    position: relative;
    height: 2px;
    background: var(--border-light);
    border-radius: var(--radius-sm);
    width: 100%;
  }
  
  .weight-slider .slider-fill {
    position: absolute;
    height: 100%;
    background: var(--brand-gradient);
    border-radius: var(--radius-sm);
    transition: width 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* 爆款开发页面参考图显示样式 */
  .references-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    gap: var(--spacing-md);
  }
  
  .reference-item {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
    border-radius: 0;
    overflow: hidden;
    height: calc(50% - var(--spacing-md) / 2); /* 固定高度，平分容器减去间距的一半 */
  }
  
  .reference-item .image-container {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    overflow: hidden; /* 超出部分隐藏 */
  }
  
  .reference-item .image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 填充模式，可能会裁剪部分图片内容 */
  }
  
  .reference-item .image-title {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    z-index: 2;
  }
  /* 调整对比区域分割线样式 */
  .image-compare .resize-handle {
    height: 100%;
    margin: 0 12px;
    z-index: 100;
  }
  
  .image-compare .resize-handle::after {
    height: 100%;
  }

  /* 确保预览关闭按钮位于图片容器之外 */
  .image-preview-modal .preview-content {
    position: relative !important;
    overflow: visible !important; /* 关键：允许内容溢出，这样按钮在容器外也可见 */
  }
  
  /* 预览翻页按钮样式 */
  .image-preview-modal .prev-image-button,
  .image-preview-modal .next-image-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    font-size: 24px;
    transition: color 0.2s ease;
    z-index: 1;
  }

  /* 添加焦点样式移除黑色边框 */
  .image-preview-modal .prev-image-button:focus,
  .image-preview-modal .next-image-button:focus,
  .image-zoom-control .zoom-button:focus {
    outline: none;
    box-shadow: none;
  }

  .image-preview-modal .prev-image-button {
    left: -70px;
  }

  .image-preview-modal .next-image-button {
    right: -70px;
  }

  .image-preview-modal .prev-image-button:hover,
  .image-preview-modal .next-image-button:hover {
    color: rgba(255, 255, 255, 0.8);
  }

  .image-preview-modal .prev-image-button svg,
  .image-preview-modal .next-image-button svg {
    width: 36px;
    height: 36px;
    filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.5));
  }

  /* 图片索引指示器样式 */
  .image-preview-modal .image-index-indicator {
    position: absolute;
    bottom: -45px;
    left: 50%;
    transform: translateX(-50%);
    background: transparent;
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.7);
  }

  /* 响应式适配 */
  @media (max-width: 768px) {
    .image-preview-modal .prev-image-button,
    .image-preview-modal .next-image-button {
      width: 40px;
      height: 40px;
    }
    
    .image-preview-modal .prev-image-button {
      left: -50px;
    }
    
    .image-preview-modal .next-image-button {
      right: -50px;
    }
    
    .image-preview-modal .prev-image-button svg,
    .image-preview-modal .next-image-button svg {
      width: 30px;
      height: 30px;
    }
    
    .image-preview-modal .image-index-indicator {
      bottom: -35px;
      font-size: 12px;
    }
  }

  /* 添加图片错误相关样式 */
  .modal-image-error {
    min-height: 200px;
    background-color: var(--bg-secondary);
    border: 1px dashed var(--border-color);
    opacity: 0.6;
  }

  .modal-error-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 10;
    text-align: center;
    max-width: 80%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  /* 提高弹窗z-index，解决与侧边栏折叠按钮的层级冲突 */
  .ant-modal.image-details-modal,
  .ant-modal-mask,
  .ant-modal-wrap {
    z-index: 1010 !important; /* 高于侧边栏折叠按钮(1001) */
  }

  /* 调整文件信息组的间距 */
  .details-sidebar .info-group:last-of-type {
    margin-top: var(--spacing-md);
  }

  /* 强度调节滑块容器样式 - 仅限图片详情弹窗 */
  .details-sidebar .weight-sliders {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 4px 0;
    gap: 12px;
    padding-left: 0;
    margin-left: 0;
  }

  /* 单个滑块行样式 - 仅限图片详情弹窗 */
  .details-sidebar .weight-slider-row {
    display: flex;
    align-items: center;
    gap: 4px; /* 减小元素间距 */
    width: 100%;
    padding-left: 0;
    margin-left: 0;
  }

  /* 滑块标签样式 - 仅限图片详情弹窗 */
  .details-sidebar .weight-item-label {
    font-size: 12px;
    color: var(--text-secondary);
    white-space: nowrap;
    min-width: 40px; /* 稍微减小宽度 */
    max-width: 40px; /* 稍微减小最大宽度 */
    text-align: left;
    padding-left: 0; /* 确保无左内边距 */
    margin-left: 0; /* 确保无左外边距 */
    margin-right: 0; /* 减小右边距 */
  }

  /* 权重值样式 - 仅限图片详情弹窗 */
  .details-sidebar .weight-value {
    font-size: 12px;
    color: var(--brand-primary);
    font-weight: 500;
    width: 32px; /* 稍微减小宽度 */
    text-align: right;
    margin-right: 3px; /* 减小右边距 */
    margin-left: 0; /* 确保无左边距 */
  }

  /* 滑块容器样式 - 仅限图片详情弹窗 */
  .details-sidebar .slider-container {
    flex: 1;
    position: relative;
    margin: 0;
    padding: 0; /* 确保无内边距 */
    display: flex;
    align-items: center;
  }

  /* 滑块轨道样式 - 仅限图片详情弹窗 */
  .details-sidebar .slider-track {
    position: relative;
    height: 2px;
    background: var(--border-light);
    border-radius: 2px;
    width: 100%;
    margin: 0;
  }

  /* 滑块填充样式 - 仅限图片详情弹窗 */
  .details-sidebar .slider-fill {
    position: absolute;
    height: 100%;
    background: var(--brand-primary);
    border-radius: 2px;
    transition: width 0.1s ease-out;
  }

  /* 修改标签显示样式，使用未选择状态的标签样式 */
  .details-sidebar .tag-display {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .details-sidebar .tag-item {
    padding: 2px 8px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--bg-primary);
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    line-height: normal;
    display: inline-block;
    vertical-align: baseline;
    margin-top: 0;
    margin-bottom: 0;
  }

  /* 调整标签描述组件的样式 */
  .details-sidebar .info-content .info-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: var(--spacing-xs);
  }

  /* 确保自定义描述可以换行显示 */
  .details-sidebar .info-content .value-with-copy {
    display: flex;
    align-items: baseline;
    max-width: 180px;
  }

  .details-sidebar .info-content .value-with-copy .value {
    flex: 1;
    word-break: normal;
    margin-right: var(--spacing-xs);
    font-size: var(--font-size-xs);
  }

  .details-sidebar .info-content .value-with-copy.ellipsis-value .value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
  }

  /* 专门为蒙版描述组件的信息行添加样式 */
  .details-sidebar .info-group:has(.tag-display) .info-row,
  .details-sidebar .info-content .info-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: baseline !important;
    margin-bottom: var(--spacing-xs);
  }

  /* 专门为标签和值元素添加更明确的样式 */
  .details-sidebar .info-row .label {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    min-width: 50px;
    height: auto !important;
    text-transform: none;
    align-self: baseline !important;
    padding: 0;
    vertical-align: baseline !important;
  }

  .details-sidebar .info-row .value,
  .details-sidebar .tag-display,
  .details-sidebar .value-with-copy {
    height: auto !important;
    font-size: var(--font-size-xs);
    align-self: baseline !important;
    vertical-align: baseline !important;
    padding: 0;
  }

  /* 蒙版扩张样式 */
  .mask-expander-info {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 4px 0;
    margin-left: 0;
  }

  .mask-expander-info .weight-slider-row {
    display: flex;
    align-items: center;
    gap: 4px;
    position: relative;
    width: 100%;
    padding-left: 0;
    margin-left: 0;
  }

  .mask-expander-info .weight-item-label {
    font-size: 12px;
    color: var(--text-secondary);
    white-space: nowrap;
    min-width: 40px;
    max-width: 40px;
    text-align: left;
    padding-left: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .mask-expander-info .weight-value {
    font-size: 12px;
    color: var(--brand-primary);
    font-weight: 500;
    width: 32px;
    text-align: right;
    margin-right: 3px;
    margin-left: 0;
  }

  .mask-expander-info .slider-container {
    flex: 1;
    position: relative;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
  }

  .mask-expander-info .slider-track {
    position: relative;
    height: 2px;
    background: var(--border-light);
    border-radius: 2px;
    width: 100%;
    margin: 0;
  }

  .mask-expander-info .slider-fill {
    position: absolute;
    height: 100%;
    background: var(--brand-primary);
    border-radius: 2px;
    transition: width 0.1s ease-out;
  }

  /* 通用信息项的值样式 */
  .details-sidebar .info-item .value {
    color: var(--text-primary);
    font-size: var(--font-size-xs);
    font-weight: 500;
  }

  /* 信息组中的值样式 - 需要省略号 */
  .details-sidebar .info-group .info-item .value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 102px;
  }

  /* 通用复制按钮值样式 */
  .details-sidebar .value-with-copy .value {
    flex: 1;
    font-size: var(--font-size-xs);
    color: var(--text-primary);
    font-weight: 500;
  }

  /* 信息组中的复制按钮值样式 - 需要省略号 */
  .details-sidebar .info-group .value-with-copy .value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 102px;
  }

  /* 通用信息内容值样式 */
  .details-sidebar .info-content .value-with-copy .value {
    flex: 1;
    word-break: normal;
    margin-right: var(--spacing-xs);
    font-size: var(--font-size-xs);
  }

  /* 信息组中的信息内容值样式 - 需要省略号 */
  .details-sidebar .info-group .info-content .value-with-copy .value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 102px;
  }

  /* 顶部通用信息样式 - 不限制宽度 */
  .details-sidebar .info-group:first-child .info-item .value,
  .details-sidebar .info-group:first-child .value-with-copy .value,
  .details-sidebar .info-group:first-child .info-content .value-with-copy .value {
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    max-width: none;
  }

  /* 中间信息组样式 - 限制宽度并显示省略号 */
  .details-sidebar .info-group:not(:first-child) .info-item .value,
  .details-sidebar .info-group:not(:first-child) .value-with-copy .value,
  .details-sidebar .info-group:not(:first-child) .info-content .value-with-copy .value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 102px;
  }

  .image-container.dragging {
    cursor: grabbing !important;
  }

  .result-image .image-container img:hover {
    cursor: grab;
  }

  .result-image .image-container img:active {
    cursor: grabbing !important;
  }

  .preview-content {
    /* 移除棋盘格背景相关样式，保持布局 */
  }

  .preview-image-container {
    background-image: linear-gradient(45deg, #d1d1d1 25%, transparent 25%),
                      linear-gradient(-45deg, #d1d1d1 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, #d1d1d1 75%),
                      linear-gradient(-45deg, transparent 75%, #d1d1d1 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    background-color: var(--bg-primary);
  }

  [data-theme="dark"] .details-sidebar .component-thumbnail {
    background-image: linear-gradient(45deg, var(--bg-secondary) 25%, transparent 25%),
                      linear-gradient(-45deg, var(--bg-secondary) 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, var(--bg-secondary) 75%),
                      linear-gradient(-45deg, transparent 75%, var(--bg-secondary) 75%);
    background-size: 10px 10px;
    background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
    background-color: var(--bg-primary);
  }

  [data-theme="dark"] .preview-image-container {
    background-image: linear-gradient(45deg, var(--bg-secondary) 25%, transparent 25%),
                      linear-gradient(-45deg, var(--bg-secondary) 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, var(--bg-secondary) 75%),
                      linear-gradient(-45deg, transparent 75%, var(--bg-secondary) 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    background-color: var(--bg-primary);
  }

  /* 随机种子特殊样式 - 允许更宽的显示空间 */
  .details-sidebar .info-item.seed-item .value-with-copy .value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px; /* 为随机种子提供更宽的显示空间 */
  }

  /* 防止文本选择的全局样式 */
  .no-select {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 场景信息允许多行显示，避免省略号 */
  .details-sidebar .scene-value {
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
    max-width: none;
    word-break: break-all;
  }

  /* 强制场景信息完整显示，彻底去除省略号和宽度限制 */
  .details-sidebar .scene-value {
    white-space: normal !important;
    overflow: visible !important;
    text-overflow: unset !important;
    max-width: 100% !important;
    word-break: break-all !important;
    display: block !important;
  }

